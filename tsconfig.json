{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "target": "ESNext", "module": "Node16", "moduleResolution": "node16", "jsx": "preserve", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "noEmit": true, "isolatedModules": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["*"]}, "incremental": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}