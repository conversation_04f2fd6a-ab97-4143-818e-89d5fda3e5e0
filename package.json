{"name": "cleanconnect-app", "version": "1.0.0", "private": true, "scripts": {"start": "npx expo start", "android": "npx expo start --android", "ios": "npx expo start --ios", "lint": "eslint ."}, "overrides": {"react-dom": "19.0.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "dependencies": {"@babel/plugin-transform-runtime": "^7.26.10", "@babel/runtime": "^7.27.0", "@expo/config-plugins": "~10.0.0", "@expo/metro-config": "^0.20.14", "@expo/prebuild-config": "~9.0.0", "@expo/vector-icons": "^14.0.0", "@hookform/resolvers": "^3.9.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "date-fns": "^4.0.0", "expo": "^53.0.9", "expo-haptics": "^14.1.4", "expo-image-manipulator": "~13.1.6", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-location": "~18.1.4", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "react": "^19.0.0", "react-hook-form": "^7.54.1", "react-native": "^0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "^3.16.7", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "zod": "^3.24.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@expo/cli": "^0.22.26", "@types/node": "22.14.1", "@types/react": "~19.0.10", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "babel-preset-expo": "~13.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.0.0", "eslint-plugin-react-native": "^4.0.0", "typescript": "^5.0.0"}}