const fs = require('fs');
const path = require('path');

// Create a simple 1x1 pixel PNG buffer (blue color)
const bluePixelBuffer = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==', 'base64');

// Paths to the image files
const iconPath = path.join(__dirname, 'assets', 'icon.png');
const splashPath = path.join(__dirname, 'assets', 'splash.png');
const adaptiveIconPath = path.join(__dirname, 'assets', 'adaptive-icon.png');

// Write the buffer to each file
fs.writeFileSync(iconPath, bluePixelBuffer);
fs.writeFileSync(splashPath, bluePixelBuffer);
fs.writeFileSync(adaptiveIconPath, bluePixelBuffer);

console.log('Placeholder images created successfully!');
