# CleanConnect App Migration Guide

## Overview
This guide helps you migrate from the old component structure to the new domain-driven architecture.

## Import Path Changes

### ✅ New Domain Structure (Recommended)

```typescript
// Shared components (use these for new code)
import { But<PERSON>, Card, ResponsiveText, Header, Badge } from '../domains/shared/components'
import { ToastService } from '../domains/shared/components'

// Auth components
import { LoginScreen, OtpVerificationModal } from '../domains/auth'

// Services components  
import { ServiceCard, ServiceListItem, ServiceBrowsingScreen } from '../domains/services'

// Design system
import { theme, colors, fonts, spacing, layout } from '../constants'
import { wp, hp, responsiveSpacing, responsiveFontSizes } from '../utils/responsive'
```

### 🔄 Legacy Imports (Still work, but deprecated)

```typescript
// These still work but will be gradually phased out
import { But<PERSON>, Card, ResponsiveText } from '../components/common'
import { Badge } from '../components/common'
```

## Component Updates

### Button Component
**Old:**
```typescript
import { Button } from '../components/common'

<Button 
  title="Click me" 
  onPress={handlePress}
  variant="primary"
/>
```

**New (Enhanced):**
```typescript
import { Button } from '../domains/shared/components'

<Button 
  title="Click me" 
  onPress={handlePress}
  variant="primary"
  size="medium"        // New: small, medium, large
  fullWidth={true}     // Enhanced responsive behavior
  loading={isLoading}  // Enhanced loading state
/>
```

### Card Component
**Old:**
```typescript
import { Card } from '../components/common'

<Card variant="elevated">
  <Text>Content</Text>
</Card>
```

**New (Enhanced):**
```typescript
import { Card } from '../domains/shared/components'

<Card 
  variant="elevated"
  padding="medium"     // New: none, small, medium, large
>
  <Text>Content</Text>
</Card>
```

### ResponsiveText Component
**Old:**
```typescript
import { ResponsiveText } from '../components/common'

<ResponsiveText variant="h1">
  Title
</ResponsiveText>
```

**New (Enhanced):**
```typescript
import { ResponsiveText } from '../domains/shared/components'

<ResponsiveText 
  variant="h1"
  color={theme.colors.primary}  // New: custom color support
  accessibilityLabel="Title"    // New: accessibility support
>
  Title
</ResponsiveText>
```

## Styling Best Practices

### ❌ Old Approach (Avoid)
```typescript
const styles = StyleSheet.create({
  container: {
    padding: 16,
    margin: 8,
    backgroundColor: '#6FD1FF',
    borderRadius: 8,
    fontSize: 16,
  }
})
```

### ✅ New Approach (Recommended)
```typescript
import { responsiveSpacing, responsiveFontSizes } from '../utils/responsive'
import { theme } from '../constants'

const styles = StyleSheet.create({
  container: {
    padding: responsiveSpacing.md,
    margin: responsiveSpacing.sm,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    fontSize: responsiveFontSizes.md,
  }
})
```

### Separate Style Files
**Create dedicated `.styles.js` files:**

```typescript
// MyComponent.tsx
import { myComponentStyles } from './MyComponent.styles'

const MyComponent = () => {
  const theme = useTheme()
  const styles = myComponentStyles(theme)
  
  return <View style={styles.container}>...</View>
}

// MyComponent.styles.js
import { StyleSheet } from 'react-native'
import { responsiveSpacing } from '../utils/responsive'

export const myComponentStyles = (theme) => {
  return StyleSheet.create({
    container: {
      padding: responsiveSpacing.md,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
    }
  })
}
```

## Custom Hooks

### Form Management
```typescript
import { useLoginForm, useLoginValidation } from '../domains/auth/hooks'

const MyLoginScreen = () => {
  const { formData, updateFormData } = useLoginForm()
  const { errors, validateForm } = useLoginValidation()
  
  // Use hooks for clean form management
}
```

### Service Management
```typescript
import { useServiceBrowsing, useServiceFilters } from '../domains/services/hooks'

const MyServiceScreen = () => {
  const { services, isLoading } = useServiceBrowsing()
  const { filteredServices, searchQuery, setSearchQuery } = useServiceFilters(services)
  
  // Use hooks for service logic
}
```

## Responsive Design

### Width/Height Percentages
```typescript
import { wp, hp } from '../utils/responsive'

const styles = StyleSheet.create({
  container: {
    width: wp(90),    // 90% of screen width
    height: hp(50),   // 50% of screen height
  }
})
```

### Responsive Values
```typescript
import { responsiveValue } from '../utils/responsive'

const fontSize = responsiveValue({
  xs: 14,
  md: 16,
  lg: 18
})
```

## Theme Usage

### Colors
```typescript
import { theme } from '../constants'

const styles = StyleSheet.create({
  primary: { color: theme.colors.primary },
  success: { color: theme.colors.success },
  error: { color: theme.colors.error },
})
```

### Typography
```typescript
import { theme } from '../constants'

const styles = StyleSheet.create({
  title: {
    ...theme.typography.h1,
    color: theme.colors.text,
  },
  body: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
  }
})
```

## Migration Checklist

### For Each Component:
- [ ] Move to appropriate domain folder
- [ ] Create separate `.styles.js` file
- [ ] Use responsive utilities instead of hardcoded values
- [ ] Use theme tokens instead of hardcoded colors/fonts
- [ ] Extract logic to custom hooks where appropriate
- [ ] Add proper TypeScript interfaces
- [ ] Update import statements
- [ ] Test responsive behavior

### For Each Screen:
- [ ] Move to appropriate domain folder
- [ ] Break down into smaller components
- [ ] Use custom hooks for state management
- [ ] Implement proper error handling
- [ ] Add loading states
- [ ] Test on different screen sizes

## Benefits of New Architecture

1. **Better Organization**: Domain-driven structure makes code easier to find and maintain
2. **Consistent Design**: Centralized theme ensures UI consistency
3. **Responsive**: Automatic scaling across device sizes
4. **Reusable**: Components and hooks can be easily shared
5. **Type Safe**: Better TypeScript support and interfaces
6. **Performance**: Optimized styling patterns
7. **Maintainable**: Clear separation of concerns

## Need Help?

- Check `REFACTORING_PROGRESS.md` for current status
- Look at existing refactored components for examples
- Use the new design system tokens in `src/constants/`
- Leverage responsive utilities in `src/utils/responsive.js`
