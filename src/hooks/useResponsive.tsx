import { useState, useEffect } from "react"
import { Dimensions, Platform, type ScaledSize } from "react-native"

// Define breakpoints for responsive design
export const breakpoints = {
  xs: 0,
  sm: 360,  // Small phones
  md: 480,  // Large phones
  lg: 768,  // Tablets
}

export type DeviceType = "phone" | "tablet"
export type Orientation = "portrait" | "landscape"

interface ResponsiveInfo {
  width: number
  height: number
  isPortrait: boolean
  isLandscape: boolean
  deviceType: DeviceType
  isIOS: boolean
  isAndroid: boolean
  isWeb: boolean
}

export const useResponsive = (): ResponsiveInfo => {
  const isIOS = Platform.OS === "ios"
  const isAndroid = Platform.OS === "android"
  const isWeb = Platform.OS === "web"

  const [dimensions, setDimensions] = useState<ScaledSize>(Dimensions.get("window"))

  useEffect(() => {
    const handleDimensionsChange = ({ window }: { window: ScaledSize }) => {
      setDimensions(window)
    }

    const subscription = Dimensions.addEventListener("change", handleDimensionsChange)

    return () => {
      if (subscription && typeof subscription.remove === "function") {
        subscription.remove()
      }
    }
  }, [])

  const { width, height } = dimensions
  const isPortrait = height > width
  const isLandscape = width > height

  // Determine device type based on screen width
  let deviceType: DeviceType = "phone"
  if (width >= breakpoints.lg) {
    deviceType = "tablet"
  }

  return {
    width,
    height,
    isPortrait,
    isLandscape,
    deviceType,
    isIOS,
    isAndroid,
    isWeb,
  }
}
