// Local booking service for frontend-only app
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BookingStatus } from '../types/booking';
import { localDataService, STORAGE_KEYS, delay, generateSuccessResponse, generateErrorResponse, generateId, gambianServices, gambianProviders, gambianAddresses } from './localDataService';

// Types
export interface CreateBookingData {
  serviceId: string;
  addressId: string;
  date: string;
  startTime: string;
  endTime?: string;
  notes?: string;
  providerId?: string;
  frequency?: 'ONE_TIME' | 'WEEKLY' | 'BI_WEEKLY' | 'MONTHLY';
  paymentMethod?: string;
  price?: number;
  total?: number;
}

export interface Booking {
  id: string;
  serviceId: string;
  serviceName: string;
  serviceImage: string;
  customerId: string;
  customerName: string;
  customerPhone: string;
  providerId?: string;
  providerName?: string;
  providerPhone?: string;
  addressId: string;
  address: {
    id: string;
    label: string;
    address: string;
    area?: string;
  };
  date: string;
  time: string;
  startTime: string;
  endTime?: string;
  duration: string;
  status: BookingStatus;
  price: number;
  total: number;
  paymentMethod: string;
  paymentStatus: 'PENDING' | 'PAID' | 'FAILED';
  notes?: string;
  frequency: string;
  createdAt: string;
  updatedAt: string;
}

// Sample Gambian bookings
const sampleBookings: Booking[] = [
  {
    id: 'BK-001',
    serviceId: '1',
    serviceName: 'Regular Home Cleaning',
    serviceImage: 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=600&h=300&fit=crop',
    customerId: '1',
    customerName: 'Fatou Jallow',
    customerPhone: '+**********',
    providerId: 'prov1',
    providerName: 'Fatou Jallow',
    providerPhone: '+220 7123456',
    addressId: '1',
    address: {
      id: '1',
      label: 'Home',
      address: '123 Kairaba Avenue, Serrekunda',
      area: 'Serrekunda',
    },
    date: '2024-12-20',
    time: '10:00 AM',
    startTime: '10:00',
    duration: '2-3 hours',
    status: BookingStatus.CONFIRMED,
    price: 500,
    total: 500,
    paymentMethod: 'Mobile Money',
    paymentStatus: 'PAID',
    notes: 'Please focus on kitchen and bathrooms',
    frequency: 'ONE_TIME',
    createdAt: '2024-12-15T10:00:00Z',
    updatedAt: '2024-12-15T10:00:00Z',
  },
  {
    id: 'BK-002',
    serviceId: '2',
    serviceName: 'Deep Cleaning Service',
    serviceImage: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=600&h=300&fit=crop',
    customerId: '1',
    customerName: 'Fatou Jallow',
    customerPhone: '+**********',
    providerId: 'prov3',
    providerName: 'Mariama Saine',
    providerPhone: '+220 7987654',
    addressId: '2',
    address: {
      id: '2',
      label: 'Office',
      address: '45 Independence Drive, Banjul',
      area: 'Banjul Central',
    },
    date: '2024-12-25',
    time: '09:00 AM',
    startTime: '09:00',
    duration: '4-5 hours',
    status: BookingStatus.PENDING,
    price: 800,
    total: 800,
    paymentMethod: 'Pay Later',
    paymentStatus: 'PENDING',
    notes: 'Need thorough cleaning of all areas',
    frequency: 'ONE_TIME',
    createdAt: '2024-12-16T14:30:00Z',
    updatedAt: '2024-12-16T14:30:00Z',
  },
];

export const localBookingService = {
  /**
   * Initialize bookings data
   */
  async initializeBookings() {
    try {
      const existingBookings = await AsyncStorage.getItem(STORAGE_KEYS.BOOKINGS);
      if (!existingBookings) {
        await AsyncStorage.setItem(STORAGE_KEYS.BOOKINGS, JSON.stringify(sampleBookings));
        console.log('Sample bookings initialized');
      }
    } catch (error) {
      console.error('Error initializing bookings:', error);
    }
  },

  /**
   * Get all bookings for current user
   */
  async getBookings(status?: BookingStatus): Promise<Booking[]> {
    try {
      await delay(500); // Simulate network delay

      const bookings = await localDataService.getData(STORAGE_KEYS.BOOKINGS);
      
      if (status) {
        return bookings.filter((booking: Booking) => booking.status === status);
      }
      
      return bookings;
    } catch (error) {
      console.error('Error getting bookings:', error);
      return [];
    }
  },

  /**
   * Get booking by ID
   */
  async getBookingById(id: string): Promise<Booking | null> {
    try {
      await delay(300); // Simulate network delay

      const bookings = await localDataService.getData(STORAGE_KEYS.BOOKINGS);
      const booking = bookings.find((b: Booking) => b.id === id);
      
      return booking || null;
    } catch (error) {
      console.error('Error getting booking by ID:', error);
      return null;
    }
  },

  /**
   * Create new booking
   */
  async createBooking(data: CreateBookingData, userId: string, userName: string, userPhone: string): Promise<Booking> {
    try {
      await delay(1000); // Simulate network delay

      console.log('Creating local booking:', data);

      // Get service details
      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      const service = services.find((s: any) => s.id === data.serviceId);
      
      if (!service) {
        throw new Error('Service not found');
      }

      // Get address details
      const addresses = await localDataService.getData(STORAGE_KEYS.ADDRESSES);
      const address = addresses.find((a: any) => a.id === data.addressId);
      
      if (!address) {
        throw new Error('Address not found');
      }

      // Get provider details if specified
      let provider = null;
      if (data.providerId) {
        const providers = await localDataService.getData(STORAGE_KEYS.PROVIDERS);
        provider = providers.find((p: any) => p.id === data.providerId);
      }

      // Create booking object
      const newBooking: Booking = {
        id: `BK-${generateId()}`,
        serviceId: data.serviceId,
        serviceName: service.title,
        serviceImage: service.image,
        customerId: userId,
        customerName: userName,
        customerPhone: userPhone,
        providerId: provider?.id,
        providerName: provider?.name,
        providerPhone: provider?.phone,
        addressId: data.addressId,
        address: {
          id: address.id,
          label: address.label,
          address: address.address,
          area: address.area,
        },
        date: data.date,
        time: data.startTime,
        startTime: data.startTime,
        endTime: data.endTime,
        duration: service.duration,
        status: BookingStatus.PENDING,
        price: data.price || service.price,
        total: data.total || data.price || service.price,
        paymentMethod: data.paymentMethod || 'Pay Later',
        paymentStatus: data.paymentMethod === 'Pay Later' ? 'PENDING' : 'PAID',
        notes: data.notes,
        frequency: data.frequency || 'ONE_TIME',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Save to local storage
      const bookings = await localDataService.getData(STORAGE_KEYS.BOOKINGS);
      bookings.push(newBooking);
      await localDataService.saveData(STORAGE_KEYS.BOOKINGS, bookings);

      console.log('Booking created successfully:', newBooking.id);
      return newBooking;
    } catch (error: any) {
      console.error('Error creating booking:', error);
      throw new Error(error.message || 'Failed to create booking');
    }
  },

  /**
   * Update booking status
   */
  async updateBookingStatus(bookingId: string, status: BookingStatus): Promise<Booking> {
    try {
      await delay(500); // Simulate network delay

      const bookings = await localDataService.getData(STORAGE_KEYS.BOOKINGS);
      const bookingIndex = bookings.findIndex((b: Booking) => b.id === bookingId);
      
      if (bookingIndex === -1) {
        throw new Error('Booking not found');
      }

      // Update booking status
      bookings[bookingIndex].status = status;
      bookings[bookingIndex].updatedAt = new Date().toISOString();

      // Save updated bookings
      await localDataService.saveData(STORAGE_KEYS.BOOKINGS, bookings);

      console.log(`Booking ${bookingId} status updated to ${status}`);
      return bookings[bookingIndex];
    } catch (error: any) {
      console.error('Error updating booking status:', error);
      throw new Error(error.message || 'Failed to update booking status');
    }
  },

  /**
   * Cancel booking
   */
  async cancelBooking(bookingId: string): Promise<Booking> {
    try {
      return await this.updateBookingStatus(bookingId, BookingStatus.CANCELLED);
    } catch (error: any) {
      console.error('Error cancelling booking:', error);
      throw new Error(error.message || 'Failed to cancel booking');
    }
  },

  /**
   * Get provider bookings (for provider dashboard)
   */
  async getProviderBookings(providerId: string, status?: BookingStatus): Promise<Booking[]> {
    try {
      await delay(500); // Simulate network delay

      const bookings = await localDataService.getData(STORAGE_KEYS.BOOKINGS);
      let providerBookings = bookings.filter((booking: Booking) => booking.providerId === providerId);
      
      if (status) {
        providerBookings = providerBookings.filter((booking: Booking) => booking.status === status);
      }
      
      return providerBookings;
    } catch (error) {
      console.error('Error getting provider bookings:', error);
      return [];
    }
  },

  /**
   * Accept booking (for providers)
   */
  async acceptBooking(bookingId: string): Promise<Booking> {
    try {
      return await this.updateBookingStatus(bookingId, BookingStatus.CONFIRMED);
    } catch (error: any) {
      console.error('Error accepting booking:', error);
      throw new Error(error.message || 'Failed to accept booking');
    }
  },

  /**
   * Reject booking (for providers)
   */
  async rejectBooking(bookingId: string): Promise<Booking> {
    try {
      return await this.updateBookingStatus(bookingId, BookingStatus.CANCELLED);
    } catch (error: any) {
      console.error('Error rejecting booking:', error);
      throw new Error(error.message || 'Failed to reject booking');
    }
  },

  /**
   * Complete booking (for providers)
   */
  async completeBooking(bookingId: string): Promise<Booking> {
    try {
      return await this.updateBookingStatus(bookingId, BookingStatus.COMPLETED);
    } catch (error: any) {
      console.error('Error completing booking:', error);
      throw new Error(error.message || 'Failed to complete booking');
    }
  },

  /**
   * Get booking statistics
   */
  async getBookingStats(userId: string, userRole: string): Promise<any> {
    try {
      await delay(300); // Simulate network delay

      const bookings = await localDataService.getData(STORAGE_KEYS.BOOKINGS);
      
      let userBookings;
      if (userRole === 'PROVIDER') {
        userBookings = bookings.filter((b: Booking) => b.providerId === userId);
      } else {
        userBookings = bookings.filter((b: Booking) => b.customerId === userId);
      }

      const stats = {
        total: userBookings.length,
        pending: userBookings.filter((b: Booking) => b.status === BookingStatus.PENDING).length,
        confirmed: userBookings.filter((b: Booking) => b.status === BookingStatus.CONFIRMED).length,
        completed: userBookings.filter((b: Booking) => b.status === BookingStatus.COMPLETED).length,
        cancelled: userBookings.filter((b: Booking) => b.status === BookingStatus.CANCELLED).length,
        totalEarnings: userBookings
          .filter((b: Booking) => b.status === BookingStatus.COMPLETED)
          .reduce((sum: number, b: Booking) => sum + b.total, 0),
      };

      return stats;
    } catch (error) {
      console.error('Error getting booking stats:', error);
      return {
        total: 0,
        pending: 0,
        confirmed: 0,
        completed: 0,
        cancelled: 0,
        totalEarnings: 0,
      };
    }
  },
};
