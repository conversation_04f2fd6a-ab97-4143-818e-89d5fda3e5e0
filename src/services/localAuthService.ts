// Local authentication service for frontend-only app
import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserRole } from '../types/user';
import { gambianUsers, delay, generateSuccessResponse, generateErrorResponse, generateId } from './localDataService';

// Storage keys for authentication
const AUTH_STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_DATA: 'userData',
  USER_ROLE: 'userRole',
  IS_AUTHENTICATED: 'isAuthenticated',
  REMEMBER_ME: 'rememberMe',
  CACHED_PHONE: 'cached_phone',
  CACHED_EMAIL: 'cached_email',
};

// Types
export interface LoginData {
  email?: string;
  password?: string;
  phone?: string;
  role?: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email?: string;
  password?: string;
  phone?: string;
  role: UserRole;
}

export interface AuthResponse {
  success: boolean;
  data?: {
    user?: any;
    token?: string;
    tokens?: {
      accessToken: string;
      refreshToken: string;
    };
    requiresVerification?: boolean;
  };
  message?: string;
  error?: string;
  user?: any;
  tokens?: any;
}

// Predefined credentials for demo
const DEMO_CREDENTIALS = {
  // Provider credentials
  '<EMAIL>': {
    password: 'M12345a@',
    user: gambianUsers.find(u => u.email === '<EMAIL>'),
  },
  '<EMAIL>': {
    password: 'password123',
    user: gambianUsers.find(u => u.email === '<EMAIL>'),
  },
  '<EMAIL>': {
    password: 'password123',
    user: gambianUsers.find(u => u.email === '<EMAIL>'),
  },
  // Customer credentials
  '<EMAIL>': {
    password: 'password123',
    user: gambianUsers.find(u => u.email === '<EMAIL>'),
  },
  // Phone numbers for OTP (customers)
  '+**********': {
    user: gambianUsers.find(u => u.phone === '+**********'),
  },
  '+**********': {
    user: gambianUsers.find(u => u.phone === '+**********'),
  },
};

// Generate mock tokens
const generateMockToken = (userId: string) => {
  return `mock_token_${userId}_${Date.now()}`;
};

const generateMockRefreshToken = (userId: string) => {
  return `mock_refresh_${userId}_${Date.now()}`;
};

// Local authentication service
export const localAuthService = {
  /**
   * Login with email/password (for providers) or phone OTP (for customers)
   */
  async login(data: LoginData): Promise<AuthResponse> {
    try {
      await delay(1000); // Simulate network delay

      console.log('Local login attempt:', { ...data, password: data.password ? '[MASKED]' : undefined });

      // Email/password login (providers)
      if (data.email && data.password) {
        const credentials = DEMO_CREDENTIALS[data.email as keyof typeof DEMO_CREDENTIALS];
        
        if (!credentials || !('password' in credentials) || credentials.password !== data.password) {
          return generateErrorResponse('Invalid email or password');
        }

        const user = credentials.user;
        if (!user) {
          return generateErrorResponse('User not found');
        }

        // Generate tokens
        const accessToken = generateMockToken(user.id);
        const refreshToken = generateMockRefreshToken(user.id);

        // Store authentication data
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.ACCESS_TOKEN, accessToken);
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.USER_DATA, JSON.stringify(user));
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.USER_ROLE, user.role);
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.IS_AUTHENTICATED, 'true');

        // Store remember me preference
        if (data.rememberMe) {
          await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.REMEMBER_ME, 'true');
          await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.CACHED_EMAIL, data.email);
        }

        return generateSuccessResponse({
          user,
          tokens: { accessToken, refreshToken },
          token: accessToken,
        });
      }

      // Phone OTP login (customers)
      if (data.phone) {
        const credentials = DEMO_CREDENTIALS[data.phone as keyof typeof DEMO_CREDENTIALS];
        
        if (!credentials || !('user' in credentials)) {
          // Auto-register new phone numbers
          const newUser = {
            id: generateId(),
            firstName: 'New',
            lastName: 'Customer',
            phone: data.phone,
            role: UserRole.CUSTOMER,
            avatar: 'https://randomuser.me/api/portraits/lego/1.jpg',
            isVerified: true,
            createdAt: new Date().toISOString(),
          };

          // Add to local users
          const users = await AsyncStorage.getItem('local_users');
          const usersList = users ? JSON.parse(users) : gambianUsers;
          usersList.push(newUser);
          await AsyncStorage.setItem('local_users', JSON.stringify(usersList));

          // Generate tokens
          const accessToken = generateMockToken(newUser.id);
          const refreshToken = generateMockRefreshToken(newUser.id);

          // Store authentication data
          await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.ACCESS_TOKEN, accessToken);
          await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
          await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.USER_DATA, JSON.stringify(newUser));
          await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.USER_ROLE, newUser.role);
          await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.IS_AUTHENTICATED, 'true');

          return generateSuccessResponse({
            user: newUser,
            tokens: { accessToken, refreshToken },
            token: accessToken,
          });
        }

        const user = credentials.user;
        if (!user) {
          return generateErrorResponse('User not found');
        }

        // Generate tokens
        const accessToken = generateMockToken(user.id);
        const refreshToken = generateMockRefreshToken(user.id);

        // Store authentication data
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.ACCESS_TOKEN, accessToken);
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.USER_DATA, JSON.stringify(user));
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.USER_ROLE, user.role);
        await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.IS_AUTHENTICATED, 'true');

        // Store remember me preference
        if (data.rememberMe) {
          await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.REMEMBER_ME, 'true');
          await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.CACHED_PHONE, data.phone);
        }

        return generateSuccessResponse({
          user,
          tokens: { accessToken, refreshToken },
          token: accessToken,
        });
      }

      return generateErrorResponse('Invalid login data');
    } catch (error: any) {
      console.error('Local login error:', error);
      return generateErrorResponse(error.message || 'Login failed');
    }
  },

  /**
   * Register new user
   */
  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      await delay(1000); // Simulate network delay

      console.log('Local registration attempt:', { ...data, password: data.password ? '[MASKED]' : undefined });

      // Check if user already exists
      const users = await AsyncStorage.getItem('local_users');
      const usersList = users ? JSON.parse(users) : gambianUsers;

      const existingUser = usersList.find((u: any) => 
        (data.email && u.email === data.email) || 
        (data.phone && u.phone === data.phone)
      );

      if (existingUser) {
        return generateErrorResponse('User already exists with this email or phone number');
      }

      // Create new user
      const newUser = {
        id: generateId(),
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        role: data.role,
        avatar: `https://randomuser.me/api/portraits/${data.role === UserRole.PROVIDER ? 'men' : 'women'}/${Math.floor(Math.random() * 50)}.jpg`,
        isVerified: true,
        rating: data.role === UserRole.PROVIDER ? 5.0 : undefined,
        completedJobs: data.role === UserRole.PROVIDER ? 0 : undefined,
        createdAt: new Date().toISOString(),
      };

      // Add to local users
      usersList.push(newUser);
      await AsyncStorage.setItem('local_users', JSON.stringify(usersList));

      // Generate tokens
      const accessToken = generateMockToken(newUser.id);
      const refreshToken = generateMockRefreshToken(newUser.id);

      // Store authentication data
      await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.ACCESS_TOKEN, accessToken);
      await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
      await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.USER_DATA, JSON.stringify(newUser));
      await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.USER_ROLE, newUser.role);
      await SecureStore.setItemAsync(AUTH_STORAGE_KEYS.IS_AUTHENTICATED, 'true');

      return generateSuccessResponse({
        user: newUser,
        tokens: { accessToken, refreshToken },
        token: accessToken,
      });
    } catch (error: any) {
      console.error('Local registration error:', error);
      return generateErrorResponse(error.message || 'Registration failed');
    }
  },

  /**
   * Send OTP (mock implementation)
   */
  async sendOtp(phone: string): Promise<AuthResponse> {
    try {
      await delay(500); // Simulate network delay
      
      // Always return success for demo
      const mockOtp = '1234'; // Fixed OTP for demo
      console.log(`Mock OTP sent to ${phone}: ${mockOtp}`);
      
      return generateSuccessResponse({
        message: 'OTP sent successfully',
        otp: mockOtp, // In real app, this wouldn't be returned
      });
    } catch (error: any) {
      return generateErrorResponse(error.message || 'Failed to send OTP');
    }
  },

  /**
   * Verify OTP (mock implementation)
   */
  async verifyOtp(phone: string, otp: string): Promise<AuthResponse> {
    try {
      await delay(500); // Simulate network delay
      
      // Accept any 4-digit OTP for demo
      if (otp.length === 4 && /^\d+$/.test(otp)) {
        return generateSuccessResponse({
          verified: true,
          message: 'OTP verified successfully',
        });
      }
      
      return generateErrorResponse('Invalid OTP');
    } catch (error: any) {
      return generateErrorResponse(error.message || 'OTP verification failed');
    }
  },

  /**
   * Logout
   */
  async logout(): Promise<void> {
    try {
      // Clear all authentication data
      await SecureStore.deleteItemAsync(AUTH_STORAGE_KEYS.ACCESS_TOKEN);
      await SecureStore.deleteItemAsync(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
      await SecureStore.deleteItemAsync(AUTH_STORAGE_KEYS.USER_DATA);
      await SecureStore.deleteItemAsync(AUTH_STORAGE_KEYS.USER_ROLE);
      await SecureStore.deleteItemAsync(AUTH_STORAGE_KEYS.IS_AUTHENTICATED);
      
      console.log('Local logout completed');
    } catch (error) {
      console.error('Error during local logout:', error);
    }
  },

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const isAuth = await SecureStore.getItemAsync(AUTH_STORAGE_KEYS.IS_AUTHENTICATED);
      const token = await SecureStore.getItemAsync(AUTH_STORAGE_KEYS.ACCESS_TOKEN);
      return isAuth === 'true' && !!token;
    } catch (error) {
      console.error('Error checking authentication status:', error);
      return false;
    }
  },

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<any | null> {
    try {
      const userData = await SecureStore.getItemAsync(AUTH_STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  /**
   * Get user role
   */
  async getUserRole(): Promise<UserRole | null> {
    try {
      const role = await SecureStore.getItemAsync(AUTH_STORAGE_KEYS.USER_ROLE);
      return role as UserRole || null;
    } catch (error) {
      console.error('Error getting user role:', error);
      return null;
    }
  },
};

export { AUTH_STORAGE_KEYS };
