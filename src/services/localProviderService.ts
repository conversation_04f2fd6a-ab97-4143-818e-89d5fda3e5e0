// Local provider service for frontend-only app
import { localDataService, STORAGE_KEYS, delay, generateSuccessResponse, generateErrorResponse, gambianProviders } from './localDataService';

// Types
export interface Provider {
  id: string;
  name: string;
  image: string;
  rating: number;
  jobs: number;
  verified: boolean;
  price?: number;
  phone?: string;
  services?: string[];
  areas?: string[];
  bio?: string;
}

export const localProviderService = {
  /**
   * Get all providers
   */
  async getProviders(): Promise<Provider[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching providers from local storage');

      const providers = await localDataService.getData(STORAGE_KEYS.PROVIDERS);
      
      if (providers && providers.length > 0) {
        console.log(`Found ${providers.length} providers`);
        return providers;
      } else {
        // Return default Gambian providers if none found
        console.log('No providers found, returning default Gambian providers');
        return gambianProviders;
      }
    } catch (error) {
      console.error('Error getting providers:', error);
      // Return default providers as fallback
      return gambianProviders;
    }
  },

  /**
   * Get top rated providers
   */
  async getTopRatedProviders(limit: number = 5): Promise<Provider[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching top rated providers, limit:', limit);

      const providers = await localDataService.getData(STORAGE_KEYS.PROVIDERS);
      
      // Sort by rating and take top providers
      const sortedProviders = providers
        .sort((a: Provider, b: Provider) => b.rating - a.rating)
        .slice(0, limit);

      console.log(`Found ${sortedProviders.length} top rated providers`);
      return sortedProviders;
    } catch (error) {
      console.error('Error getting top rated providers:', error);
      return [];
    }
  },

  /**
   * Get provider by ID
   */
  async getProviderById(id: string): Promise<Provider | null> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching provider by ID:', id);

      const providers = await localDataService.getData(STORAGE_KEYS.PROVIDERS);
      const provider = providers.find((p: Provider) => p.id === id);
      
      if (provider) {
        console.log('Found provider:', provider.name);
        return provider;
      } else {
        console.log('Provider not found:', id);
        return null;
      }
    } catch (error) {
      console.error('Error getting provider by ID:', error);
      return null;
    }
  },

  /**
   * Get providers by service
   */
  async getProvidersByService(serviceId: string): Promise<Provider[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching providers for service:', serviceId);

      const providers = await localDataService.getData(STORAGE_KEYS.PROVIDERS);
      
      const serviceProviders = providers.filter((provider: Provider) => 
        provider.services && provider.services.includes(serviceId)
      );

      console.log(`Found ${serviceProviders.length} providers for service ${serviceId}`);
      return serviceProviders;
    } catch (error) {
      console.error('Error getting providers by service:', error);
      return [];
    }
  },

  /**
   * Get providers by area
   */
  async getProvidersByArea(area: string): Promise<Provider[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching providers for area:', area);

      const providers = await localDataService.getData(STORAGE_KEYS.PROVIDERS);
      
      const areaProviders = providers.filter((provider: Provider) => 
        provider.areas && provider.areas.some(providerArea => 
          providerArea.toLowerCase().includes(area.toLowerCase())
        )
      );

      console.log(`Found ${areaProviders.length} providers in area ${area}`);
      return areaProviders;
    } catch (error) {
      console.error('Error getting providers by area:', error);
      return [];
    }
  },

  /**
   * Search providers
   */
  async searchProviders(query: string): Promise<Provider[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Searching providers with query:', query);

      if (!query || query.trim() === '') {
        return await this.getProviders();
      }

      const providers = await localDataService.getData(STORAGE_KEYS.PROVIDERS);
      const searchQuery = query.toLowerCase().trim();

      const filteredProviders = providers.filter((provider: Provider) => 
        provider.name.toLowerCase().includes(searchQuery) ||
        (provider.bio && provider.bio.toLowerCase().includes(searchQuery)) ||
        (provider.areas && provider.areas.some(area => 
          area.toLowerCase().includes(searchQuery)
        ))
      );

      console.log(`Found ${filteredProviders.length} providers matching "${query}"`);
      return filteredProviders;
    } catch (error) {
      console.error('Error searching providers:', error);
      return [];
    }
  },

  /**
   * Get available providers for booking
   */
  async getAvailableProviders(serviceId: string, date: string, area?: string): Promise<Provider[]> {
    try {
      await delay(400); // Simulate network delay
      console.log('Fetching available providers for service:', serviceId, 'on date:', date, 'in area:', area);

      let providers = await this.getProvidersByService(serviceId);
      
      // Filter by area if specified
      if (area) {
        providers = providers.filter((provider: Provider) => 
          provider.areas && provider.areas.some(providerArea => 
            providerArea.toLowerCase().includes(area.toLowerCase())
          )
        );
      }

      // For demo purposes, randomly mark some providers as unavailable
      const availableProviders = providers.filter(() => Math.random() > 0.2);

      console.log(`Found ${availableProviders.length} available providers`);
      return availableProviders;
    } catch (error) {
      console.error('Error getting available providers:', error);
      return [];
    }
  },

  /**
   * Get provider reviews
   */
  async getProviderReviews(providerId: string): Promise<any[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching reviews for provider:', providerId);

      // Generate mock reviews for demo
      const mockReviews = [
        {
          id: '1',
          customerId: 'customer1',
          customerName: 'Aminata Jallow',
          rating: 5,
          comment: 'Excellent service! Very thorough and professional.',
          date: '2024-12-10',
        },
        {
          id: '2',
          customerId: 'customer2',
          customerName: 'Lamin Ceesay',
          rating: 4,
          comment: 'Good work, arrived on time and cleaned well.',
          date: '2024-12-08',
        },
        {
          id: '3',
          customerId: 'customer3',
          customerName: 'Isatou Saine',
          rating: 5,
          comment: 'Highly recommend! Will book again.',
          date: '2024-12-05',
        },
      ];

      console.log(`Found ${mockReviews.length} reviews for provider`);
      return mockReviews;
    } catch (error) {
      console.error('Error getting provider reviews:', error);
      return [];
    }
  },

  /**
   * Update provider profile
   */
  async updateProviderProfile(providerId: string, updates: Partial<Provider>): Promise<Provider> {
    try {
      await delay(500); // Simulate network delay
      console.log('Updating provider profile:', providerId);

      const providers = await localDataService.getData(STORAGE_KEYS.PROVIDERS);
      const providerIndex = providers.findIndex((p: Provider) => p.id === providerId);
      
      if (providerIndex === -1) {
        throw new Error('Provider not found');
      }

      const updatedProvider = { ...providers[providerIndex], ...updates };
      providers[providerIndex] = updatedProvider;
      
      await localDataService.saveData(STORAGE_KEYS.PROVIDERS, providers);

      console.log('Provider profile updated successfully:', providerId);
      return updatedProvider;
    } catch (error) {
      console.error('Error updating provider profile:', error);
      throw new Error('Failed to update provider profile');
    }
  },

  /**
   * Get provider statistics
   */
  async getProviderStats(providerId: string): Promise<any> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching stats for provider:', providerId);

      const provider = await this.getProviderById(providerId);
      if (!provider) {
        throw new Error('Provider not found');
      }

      // Generate mock statistics
      const stats = {
        totalJobs: provider.jobs || 0,
        completedJobs: Math.floor((provider.jobs || 0) * 0.95),
        cancelledJobs: Math.floor((provider.jobs || 0) * 0.05),
        averageRating: provider.rating,
        totalEarnings: (provider.jobs || 0) * (provider.price || 500),
        thisMonthJobs: Math.floor(Math.random() * 10) + 5,
        thisMonthEarnings: Math.floor(Math.random() * 5000) + 2000,
        responseTime: '< 2 hours',
        completionRate: '95%',
      };

      console.log('Provider stats:', stats);
      return stats;
    } catch (error) {
      console.error('Error getting provider stats:', error);
      throw error;
    }
  },

  /**
   * Add provider to favorites (for customers)
   */
  async addToFavorites(customerId: string, providerId: string): Promise<void> {
    try {
      await delay(200); // Simulate network delay
      console.log('Adding provider to favorites:', providerId, 'for customer:', customerId);

      // In a real app, this would be stored in a favorites table
      // For demo, we'll just log it
      console.log('Provider added to favorites successfully');
    } catch (error) {
      console.error('Error adding provider to favorites:', error);
      throw new Error('Failed to add provider to favorites');
    }
  },

  /**
   * Remove provider from favorites
   */
  async removeFromFavorites(customerId: string, providerId: string): Promise<void> {
    try {
      await delay(200); // Simulate network delay
      console.log('Removing provider from favorites:', providerId, 'for customer:', customerId);

      // In a real app, this would remove from favorites table
      // For demo, we'll just log it
      console.log('Provider removed from favorites successfully');
    } catch (error) {
      console.error('Error removing provider from favorites:', error);
      throw new Error('Failed to remove provider from favorites');
    }
  },
};
