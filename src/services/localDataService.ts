// Local data service for frontend-only app
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { UserRole } from '../types/user';
import { BookingStatus } from '../types/booking';

// Storage keys
const STORAGE_KEYS = {
  USERS: 'local_users',
  SERVICES: 'local_services',
  PROVIDERS: 'local_providers',
  BOOKINGS: 'local_bookings',
  ADDRESSES: 'local_addresses',
  NOTIFICATIONS: 'local_notifications',
  REVIEWS: 'local_reviews',
};

// Gambian context data
export const gambianUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Fatou',
    lastName: 'Jallow',
    role: UserRole.CUSTOMER,
    phone: '+**********',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    isVerified: true,
    createdAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: 'Ousman',
    lastName: 'Ceesay',
    role: UserRole.PROVIDER,
    phone: '+**********',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    isVerified: true,
    rating: 4.7,
    completedJobs: 85,
    createdAt: '2024-02-20T14:30:00Z',
  },
  {
    id: '3',
    email: '<EMAIL>',
    firstName: 'Momodou',
    lastName: 'Jallow',
    role: UserRole.PROVIDER,
    phone: '+**********',
    avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
    isVerified: true,
    rating: 4.9,
    completedJobs: 120,
    createdAt: '2024-03-10T09:15:00Z',
  },
  {
    id: '4',
    email: '<EMAIL>',
    firstName: 'Mariama',
    lastName: 'Saine',
    role: UserRole.PROVIDER,
    phone: '+**********',
    avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
    isVerified: true,
    rating: 4.8,
    completedJobs: 95,
    createdAt: '2024-04-05T16:45:00Z',
  },
];

export const gambianServices = [
  {
    id: '1',
    title: 'Regular Home Cleaning',
    description: 'Standard cleaning service for homes in Banjul and surrounding areas',
    image: 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=600&h=300&fit=crop',
    price: 500,
    duration: '2-3 hours',
    category: 'HOME',
    whatsIncluded: [
      'Sweeping and mopping all floors',
      'Dusting furniture and surfaces',
      'Cleaning bathrooms and toilets',
      'Kitchen cleaning and dishwashing',
      'Trash removal'
    ],
  },
  {
    id: '2',
    title: 'Deep Cleaning Service',
    description: 'Thorough deep cleaning for homes and offices in Greater Banjul Area',
    image: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=600&h=300&fit=crop',
    price: 800,
    duration: '4-5 hours',
    category: 'HOME',
    whatsIncluded: [
      'Complete deep cleaning of all rooms',
      'Window cleaning (interior)',
      'Appliance cleaning',
      'Detailed bathroom sanitization',
      'Floor scrubbing and polishing',
      'Furniture deep cleaning'
    ],
  },
  {
    id: '3',
    title: 'Office Cleaning',
    description: 'Professional cleaning for offices in Serrekunda and Kanifing',
    image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=300&fit=crop',
    price: 600,
    duration: '2-4 hours',
    category: 'OFFICE',
    whatsIncluded: [
      'Desk and workspace cleaning',
      'Floor vacuuming and mopping',
      'Restroom sanitization',
      'Trash emptying',
      'Surface disinfection'
    ],
  },
  {
    id: '4',
    title: 'Post-Construction Cleanup',
    description: 'Specialized cleaning after construction or renovation work',
    image: 'https://images.unsplash.com/photo-1504148455328-c376907d081c?w=600&h=300&fit=crop',
    price: 1200,
    duration: '6-8 hours',
    category: 'SPECIALIZED',
    whatsIncluded: [
      'Dust and debris removal',
      'Paint splatter cleaning',
      'Window and glass cleaning',
      'Floor deep cleaning',
      'Final inspection and touch-ups'
    ],
  },
];

export const gambianAddresses = [
  {
    id: '1',
    label: 'Home',
    address: '123 Kairaba Avenue, Serrekunda',
    area: 'Serrekunda',
    city: 'Serrekunda',
    isPrimary: true,
    coordinates: {
      latitude: 13.4399,
      longitude: -16.6775,
    },
  },
  {
    id: '2',
    label: 'Office',
    address: '45 Independence Drive, Banjul',
    area: 'Banjul Central',
    city: 'Banjul',
    isPrimary: false,
    coordinates: {
      latitude: 13.4549,
      longitude: -16.5790,
    },
  },
  {
    id: '3',
    label: 'Family House',
    address: '78 Mosque Road, Bakau',
    area: 'Bakau',
    city: 'Bakau',
    isPrimary: false,
    coordinates: {
      latitude: 13.4781,
      longitude: -16.6820,
    },
  },
];

export const gambianProviders = [
  {
    id: 'prov1',
    name: 'Fatou Jallow',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    rating: 4.9,
    jobs: 120,
    verified: true,
    price: 500,
    phone: '+220 7123456',
    services: ['1', '2'], // Regular and Deep cleaning
    areas: ['Serrekunda', 'Kanifing', 'Bakau'],
    bio: 'Experienced cleaner with 5+ years serving families in Greater Banjul Area',
  },
  {
    id: 'prov2',
    name: 'Ousman Ceesay',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    rating: 4.7,
    jobs: 85,
    verified: true,
    price: 600,
    phone: '+220 7654321',
    services: ['1', '3'], // Regular and Office cleaning
    areas: ['Banjul', 'Serrekunda'],
    bio: 'Professional cleaner specializing in office and commercial spaces',
  },
  {
    id: 'prov3',
    name: 'Mariama Saine',
    image: 'https://randomuser.me/api/portraits/women/68.jpg',
    rating: 4.8,
    jobs: 95,
    verified: true,
    price: 550,
    phone: '+220 7987654',
    services: ['2', '4'], // Deep cleaning and Post-construction
    areas: ['Kanifing', 'Bakau', 'Fajara'],
    bio: 'Specialist in deep cleaning and post-construction cleanup services',
  },
];

// Helper functions
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const generateId = () => Date.now().toString() + Math.random().toString(36).substr(2, 9);

export const generateSuccessResponse = (data: any) => ({
  success: true,
  data,
  message: 'Operation successful',
});

export const generateErrorResponse = (error: string) => ({
  success: false,
  data: null,
  error,
});

// Local storage operations
export const localDataService = {
  // Initialize local data
  async initializeData() {
    try {
      // Check if data already exists
      const existingUsers = await AsyncStorage.getItem(STORAGE_KEYS.USERS);
      if (!existingUsers) {
        // Initialize with Gambian data
        await AsyncStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(gambianUsers));
        await AsyncStorage.setItem(STORAGE_KEYS.SERVICES, JSON.stringify(gambianServices));
        await AsyncStorage.setItem(STORAGE_KEYS.PROVIDERS, JSON.stringify(gambianProviders));
        await AsyncStorage.setItem(STORAGE_KEYS.ADDRESSES, JSON.stringify(gambianAddresses));
        await AsyncStorage.setItem(STORAGE_KEYS.BOOKINGS, JSON.stringify([]));
        await AsyncStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify([]));
        await AsyncStorage.setItem(STORAGE_KEYS.REVIEWS, JSON.stringify([]));
        console.log('Local data initialized with Gambian context');
      }
    } catch (error) {
      console.error('Error initializing local data:', error);
    }
  },

  // Get data from local storage
  async getData(key: string) {
    try {
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error(`Error getting data for key ${key}:`, error);
      return [];
    }
  },

  // Save data to local storage
  async saveData(key: string, data: any) {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error(`Error saving data for key ${key}:`, error);
      return false;
    }
  },

  // Clear all local data
  async clearAllData() {
    try {
      const keys = Object.values(STORAGE_KEYS);
      await AsyncStorage.multiRemove(keys);
      console.log('All local data cleared');
    } catch (error) {
      console.error('Error clearing local data:', error);
    }
  },
};

export { STORAGE_KEYS };
