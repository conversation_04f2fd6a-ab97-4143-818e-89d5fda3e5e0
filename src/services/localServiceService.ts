// Local service service for frontend-only app
import { localDataService, STORAGE_KEYS, delay, generateSuccessResponse, generateErrorResponse, gambianServices, gambianProviders } from './localDataService';

// Types
export interface Service {
  id: string;
  title: string;
  description: string;
  image: string;
  price: number;
  duration: string;
  category: string;
  whatsIncluded?: string[];
}

export interface ServiceWithProviders extends Service {
  providers: any[];
}

export const localServiceService = {
  /**
   * Get all services
   */
  async getServices(): Promise<Service[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching services from local storage');

      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      
      if (services && services.length > 0) {
        console.log(`Found ${services.length} services`);
        return services;
      } else {
        // Return default Gambian services if none found
        console.log('No services found, returning default Gambian services');
        return gambianServices;
      }
    } catch (error) {
      console.error('Error getting services:', error);
      // Return default services as fallback
      return gambianServices;
    }
  },

  /**
   * Get service by ID with providers
   */
  async getServiceById(id: string): Promise<ServiceWithProviders | null> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching service by ID:', id);

      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      const service = services.find((s: Service) => s.id === id);
      
      if (!service) {
        console.log('Service not found:', id);
        return null;
      }

      // Get providers for this service
      const allProviders = await localDataService.getData(STORAGE_KEYS.PROVIDERS);
      const serviceProviders = allProviders.filter((provider: any) => 
        provider.services && provider.services.includes(id)
      );

      const serviceWithProviders: ServiceWithProviders = {
        ...service,
        providers: serviceProviders,
      };

      console.log(`Found service ${service.title} with ${serviceProviders.length} providers`);
      return serviceWithProviders;
    } catch (error) {
      console.error('Error getting service by ID:', error);
      return null;
    }
  },

  /**
   * Get services by category
   */
  async getServicesByCategory(category: string): Promise<Service[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching services by category:', category);

      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      
      if (category === 'all' || !category) {
        return services;
      }

      const filteredServices = services.filter((service: Service) => 
        service.category.toLowerCase() === category.toLowerCase()
      );

      console.log(`Found ${filteredServices.length} services in category ${category}`);
      return filteredServices;
    } catch (error) {
      console.error('Error getting services by category:', error);
      return [];
    }
  },

  /**
   * Search services
   */
  async searchServices(query: string): Promise<Service[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Searching services with query:', query);

      if (!query || query.trim() === '') {
        return await this.getServices();
      }

      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      const searchQuery = query.toLowerCase().trim();

      const filteredServices = services.filter((service: Service) => 
        service.title.toLowerCase().includes(searchQuery) ||
        service.description.toLowerCase().includes(searchQuery) ||
        service.category.toLowerCase().includes(searchQuery)
      );

      console.log(`Found ${filteredServices.length} services matching "${query}"`);
      return filteredServices;
    } catch (error) {
      console.error('Error searching services:', error);
      return [];
    }
  },

  /**
   * Get popular services
   */
  async getPopularServices(limit: number = 5): Promise<Service[]> {
    try {
      await delay(300); // Simulate network delay
      console.log('Fetching popular services, limit:', limit);

      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      
      // For demo purposes, return the first few services as "popular"
      const popularServices = services.slice(0, limit);

      console.log(`Found ${popularServices.length} popular services`);
      return popularServices;
    } catch (error) {
      console.error('Error getting popular services:', error);
      return [];
    }
  },

  /**
   * Get service pricing
   */
  async getServicePricing(serviceId: string): Promise<any> {
    try {
      await delay(200); // Simulate network delay
      console.log('Fetching pricing for service:', serviceId);

      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      const service = services.find((s: Service) => s.id === serviceId);
      
      if (!service) {
        throw new Error('Service not found');
      }

      // Return pricing information
      const pricing = {
        basePrice: service.price,
        currency: 'GMD',
        duration: service.duration,
        priceBreakdown: {
          serviceCharge: service.price,
          tax: Math.round(service.price * 0.1), // 10% tax
          total: Math.round(service.price * 1.1),
        },
        discounts: {
          firstTime: 0.1, // 10% first time discount
          weekly: 0.05, // 5% weekly booking discount
          monthly: 0.15, // 15% monthly booking discount
        }
      };

      console.log('Service pricing:', pricing);
      return pricing;
    } catch (error) {
      console.error('Error getting service pricing:', error);
      throw error;
    }
  },

  /**
   * Get available time slots for a service
   */
  async getAvailableTimeSlots(serviceId: string, date: string): Promise<string[]> {
    try {
      await delay(200); // Simulate network delay
      console.log('Fetching available time slots for service:', serviceId, 'on date:', date);

      // Generate mock time slots for demo
      const timeSlots = [
        '08:00', '09:00', '10:00', '11:00', 
        '14:00', '15:00', '16:00', '17:00'
      ];

      // Randomly remove some slots to simulate bookings
      const availableSlots = timeSlots.filter(() => Math.random() > 0.3);

      console.log(`Found ${availableSlots.length} available time slots`);
      return availableSlots;
    } catch (error) {
      console.error('Error getting available time slots:', error);
      return [];
    }
  },

  /**
   * Add new service (for providers)
   */
  async addService(serviceData: Omit<Service, 'id'>): Promise<Service> {
    try {
      await delay(500); // Simulate network delay
      console.log('Adding new service:', serviceData.title);

      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      
      const newService: Service = {
        ...serviceData,
        id: `service_${Date.now()}`,
      };

      services.push(newService);
      await localDataService.saveData(STORAGE_KEYS.SERVICES, services);

      console.log('Service added successfully:', newService.id);
      return newService;
    } catch (error) {
      console.error('Error adding service:', error);
      throw new Error('Failed to add service');
    }
  },

  /**
   * Update service
   */
  async updateService(serviceId: string, updates: Partial<Service>): Promise<Service> {
    try {
      await delay(500); // Simulate network delay
      console.log('Updating service:', serviceId);

      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      const serviceIndex = services.findIndex((s: Service) => s.id === serviceId);
      
      if (serviceIndex === -1) {
        throw new Error('Service not found');
      }

      const updatedService = { ...services[serviceIndex], ...updates };
      services[serviceIndex] = updatedService;
      
      await localDataService.saveData(STORAGE_KEYS.SERVICES, services);

      console.log('Service updated successfully:', serviceId);
      return updatedService;
    } catch (error) {
      console.error('Error updating service:', error);
      throw new Error('Failed to update service');
    }
  },

  /**
   * Delete service
   */
  async deleteService(serviceId: string): Promise<void> {
    try {
      await delay(300); // Simulate network delay
      console.log('Deleting service:', serviceId);

      const services = await localDataService.getData(STORAGE_KEYS.SERVICES);
      const filteredServices = services.filter((s: Service) => s.id !== serviceId);
      
      await localDataService.saveData(STORAGE_KEYS.SERVICES, filteredServices);

      console.log('Service deleted successfully:', serviceId);
    } catch (error) {
      console.error('Error deleting service:', error);
      throw new Error('Failed to delete service');
    }
  },
};
