import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Animated,
  Vibration,
  ActivityIndicator,
  Alert,
} from "react-native";
import { useNavigation, useRoute, RouteProp, CommonActions, useFocusEffect } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Feather } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from "../context/ThemeContext";
import { useAuth } from "../context/AuthContext";
import type { RootStackParamList } from "../navigation/RootNavigator";
import Button from "../components/common/Button";
import { ToastService } from "../components/common/ToastManager";
import { localAuthService } from "../services/localAuthService";
import * as SecureStore from 'expo-secure-store';

// Navigation types
type PhoneVerificationScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'PhoneVerification'>;
type PhoneVerificationScreenRouteProp = RouteProp<RootStackParamList, 'PhoneVerification'>;

// OTP verification step enum
enum VerificationStep {
  PHONE_INPUT = 'PHONE_INPUT',
  OTP_VERIFICATION = 'OTP_VERIFICATION',
  SUCCESS = 'SUCCESS'
}

const PhoneVerificationScreen = () => {
  const navigation = useNavigation<PhoneVerificationScreenNavigationProp>();
  const route = useRoute<PhoneVerificationScreenRouteProp>();
  const theme = useTheme();
  const { login } = useAuth();

  // Get screen dimensions
  const { width, height } = Dimensions.get("window");

  // State management
  const [currentStep, setCurrentStep] = useState<VerificationStep>(VerificationStep.PHONE_INPUT);
  const [phone, setPhone] = useState("");
  const [formattedPhone, setFormattedPhone] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState("");
  const [rememberMe, setRememberMe] = useState(false);

  // OTP related state
  const [otp, setOtp] = useState(['', '', '', '']);
  const [otpError, setOtpError] = useState("");
  const [resendTimer, setResendTimer] = useState(0);
  const [canResend, setCanResend] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [devOtp, setDevOtp] = useState("");

  // Animation refs
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(1)).current;
  const slideAnimation = useRef(new Animated.Value(0)).current;
  const successAnimation = useRef(new Animated.Value(0)).current;

  // Input refs
  const phoneInputRef = useRef<TextInput>(null);
  const otpInputRefs = useRef<(TextInput | null)[]>([]);

  // Load saved phone number if remember me was enabled
  useEffect(() => {
    const loadSavedPhone = async () => {
      try {
        const savedPhone = await SecureStore.getItemAsync('saved_phone');
        const rememberMeStatus = await SecureStore.getItemAsync('remember_me_phone');

        if (savedPhone && rememberMeStatus === 'true') {
          const formatted = formatPhoneNumber(savedPhone);
          setPhone(savedPhone);
          setFormattedPhone(formatted);
          setRememberMe(true);
        }
      } catch (error) {
        console.error('Error loading saved phone:', error);
      }
    };

    loadSavedPhone();
  }, []);

  // Resend timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(prev => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  // Focus effect for auto-focus
  useFocusEffect(
    useCallback(() => {
      if (currentStep === VerificationStep.PHONE_INPUT) {
        setTimeout(() => phoneInputRef.current?.focus(), 100);
      }
    }, [currentStep])
  );

  // Enhanced phone validation function
  const validatePhone = (phoneNumber: string): boolean => {
    const cleanPhone = phoneNumber.replace(/\D/g, '');

    if (!cleanPhone) {
      setPhoneError("Phone number is required");
      return false;
    }

    if (cleanPhone.length !== 10) {
      setPhoneError("Phone number must be 10 digits");
      return false;
    }

    // Indian phone number validation (starts with 6, 7, 8, or 9)
    if (!/^[6-9]\d{9}$/.test(cleanPhone)) {
      setPhoneError("Please enter a valid Indian phone number");
      return false;
    }

    setPhoneError("");
    return true;
  };

  // Format phone number for display
  const formatPhoneNumber = (phoneNumber: string): string => {
    const cleaned = phoneNumber.replace(/\D/g, '');
    const limited = cleaned.slice(0, 10);

    if (limited.length >= 6) {
      return `${limited.slice(0, 5)} ${limited.slice(5)}`;
    } else if (limited.length >= 3) {
      return `${limited.slice(0, 5)} ${limited.slice(5)}`;
    }
    return limited;
  };

  // Handle phone input change
  const handlePhoneChange = (text: string) => {
    const cleaned = text.replace(/\D/g, '');
    const limited = cleaned.slice(0, 10);

    setPhone(limited);
    setFormattedPhone(formatPhoneNumber(limited));

    // Clear errors when user starts typing
    if (phoneError) {
      setPhoneError("");
    }
    if (loginError) {
      setLoginError("");
    }

    // Provide real-time validation feedback
    if (limited.length === 10) {
      validatePhone(limited);
    }
  };

  // Handle OTP input change
  const handleOtpChange = (text: string, index: number) => {
    if (text.length > 1) {
      // Handle paste operation
      const pastedOtp = text.slice(0, 4).split('');
      const newOtp = [...otp];
      pastedOtp.forEach((digit, i) => {
        if (i < 4 && /^\d$/.test(digit)) {
          newOtp[i] = digit;
        }
      });
      setOtp(newOtp);

      // Focus on the last filled input or next empty input
      const nextIndex = Math.min(pastedOtp.length, 3);
      otpInputRefs.current[nextIndex]?.focus();

      // Auto-verify if all 4 digits are entered
      if (newOtp.every(digit => digit !== '')) {
        setTimeout(() => handleVerifyOtp(newOtp.join('')), 300);
      }
      return;
    }

    // Handle single digit input
    if (/^\d$/.test(text) || text === '') {
      const newOtp = [...otp];
      newOtp[index] = text;
      setOtp(newOtp);

      // Clear error when user starts typing
      if (otpError) {
        setOtpError("");
      }

      // Auto-focus next input
      if (text && index < 3) {
        otpInputRefs.current[index + 1]?.focus();
      }

      // Auto-verify if all 4 digits are entered
      if (text && index === 3 && newOtp.every(digit => digit !== '')) {
        setTimeout(() => handleVerifyOtp(newOtp.join('')), 300);
      }
    }
  };

  // Handle OTP input key press (for backspace)
  const handleOtpKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      otpInputRefs.current[index - 1]?.focus();
    }
  };

  // Animation functions
  const triggerShakeAnimation = () => {
    Vibration.vibrate(100);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 100, useNativeDriver: true }),
    ]).start();
  };

  const triggerSuccessAnimation = () => {
    Vibration.vibrate([100, 50, 100]);
    Animated.timing(successAnimation, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const slideToNextStep = () => {
    Animated.timing(slideAnimation, {
      toValue: -width,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setCurrentStep(VerificationStep.OTP_VERIFICATION);
      slideAnimation.setValue(width);
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        // Focus first OTP input
        setTimeout(() => otpInputRefs.current[0]?.focus(), 100);
      });
    });
  };

  // Handle send OTP
  const handleSendOtp = async () => {
    Keyboard.dismiss();

    if (!validatePhone(phone)) {
      triggerShakeAnimation();
      return;
    }

    setIsLoading(true);
    setLoginError("");

    try {
      // Save phone number if remember me is enabled
      if (rememberMe) {
        await SecureStore.setItemAsync('saved_phone', phone);
        await SecureStore.setItemAsync('remember_me_phone', 'true');
      } else {
        await SecureStore.deleteItemAsync('saved_phone');
        await SecureStore.deleteItemAsync('remember_me_phone');
      }

      // Call the local auth service for phone-based authentication
      const response = await localAuthService.login({
        phone: phone,
        role: 'CUSTOMER'
      });

      if (response.success) {
        setDevOtp(response.data?.otp || "");
        setResendTimer(60);
        setCanResend(false);
        slideToNextStep();
        ToastService.show("Verification code sent to your phone", "success");

        // Log development OTP
        if (response.data?.otp) {
          console.log(`[DEV] OTP for ${phone}: ${response.data.otp}`);
        }
      } else {
        setLoginError(response.error || "Failed to send verification code");
        triggerShakeAnimation();
      }
    } catch (error: any) {
      console.error('Send OTP error:', error);
      setLoginError(error.message || "Network error. Please try again.");
      triggerShakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP verification
  const handleVerifyOtp = async (otpCode?: string) => {
    const codeToVerify = otpCode || otp.join('');

    if (codeToVerify.length !== 4) {
      setOtpError('Please enter a valid 4-digit verification code');
      triggerShakeAnimation();
      return;
    }

    setIsVerifying(true);
    setOtpError("");
    Keyboard.dismiss();

    try {
      // Use the local auth service to verify OTP
      const response = await localAuthService.verifyOtp(phone, codeToVerify);

      if (response.success) {
        triggerSuccessAnimation();
        setCurrentStep(VerificationStep.SUCCESS);

        // Navigate to main app after a short delay
        setTimeout(() => {
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [{ name: 'Main' }],
            })
          );
          ToastService.show("Welcome to CleanConnect!", "success");
        }, 1500);
      } else {
        setOtpError(response.error || 'Invalid verification code. Please try again.');
        triggerShakeAnimation();
        // Clear OTP inputs on error
        setOtp(['', '', '', '']);
        otpInputRefs.current[0]?.focus();
      }
    } catch (error: any) {
      console.error('OTP verification error:', error);
      setOtpError(error.message || 'Verification failed. Please try again.');
      triggerShakeAnimation();
      setOtp(['', '', '', '']);
      otpInputRefs.current[0]?.focus();
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle resend OTP
  const handleResendOtp = async () => {
    if (!canResend) return;

    setIsLoading(true);
    setOtpError("");

    try {
      const response = await localAuthService.sendOtp(phone);

      if (response.success) {
        setDevOtp(response.data?.otp || "");
        setResendTimer(60);
        setCanResend(false);
        setOtp(['', '', '', '']);
        ToastService.show("New verification code sent", "success");

        // Log development OTP
        if (response.data?.otp) {
          console.log(`[DEV] Resent OTP for ${phone}: ${response.data.otp}`);
        }
      } else {
        setOtpError("Failed to resend verification code");
        triggerShakeAnimation();
      }
    } catch (error: any) {
      console.error('Resend OTP error:', error);
      setOtpError(error.message || "Failed to resend code. Please try again.");
      triggerShakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  // Handle back button
  const handleBack = () => {
    if (currentStep === VerificationStep.OTP_VERIFICATION) {
      setCurrentStep(VerificationStep.PHONE_INPUT);
      setOtp(['', '', '', '']);
      setOtpError("");
    } else {
      navigation.navigate("RoleSelection");
    }
  };

  // Render phone input step
  const renderPhoneInputStep = () => (
    <Animated.View style={[styles.stepContainer, { transform: [{ translateX: slideAnimation }] }]}>
      <View style={styles.headerContainer}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Enter your phone number
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.textLight }]}>
          We'll send you a verification code to confirm your number
        </Text>
      </View>

      <Animated.View style={[styles.formContainer, { transform: [{ translateX: shakeAnimation }] }]}>
        {loginError ? (
          <View style={[styles.errorContainer, { backgroundColor: `${theme.colors.error}15` }]}>
            <Feather name="alert-circle" size={20} color={theme.colors.error} />
            <Text style={[styles.errorText, { color: theme.colors.error }]}>
              {loginError}
            </Text>
          </View>
        ) : null}

        {/* Phone Input */}
        <View style={styles.inputContainer}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Phone Number</Text>
          <View style={[styles.phoneInputWrapper, {
            backgroundColor: theme.colors.card,
            borderColor: phoneError ? theme.colors.error :
                        phone.length === 10 ? theme.colors.primary : theme.colors.border,
            borderWidth: 2,
          }]}>
            <View style={styles.countryCodeContainer}>
              <Text style={[styles.countryCode, { color: theme.colors.text }]}>+91</Text>
            </View>
            <TextInput
              ref={phoneInputRef}
              style={[styles.phoneInput, { color: theme.colors.text }]}
              placeholder="Enter phone number"
              placeholderTextColor={theme.colors.textLight}
              keyboardType="phone-pad"
              value={formattedPhone}
              onChangeText={handlePhoneChange}
              maxLength={11} // 5 + space + 5
              returnKeyType="done"
              onSubmitEditing={handleSendOtp}
              accessibilityLabel="Phone number input field"
              accessibilityHint="Enter your 10-digit phone number"
            />
            {phone.length === 10 && (
              <Feather name="check-circle" size={20} color={theme.colors.primary} />
            )}
          </View>
          {phoneError ? (
            <Text style={[styles.fieldErrorText, { color: theme.colors.error }]}>
              {phoneError}
            </Text>
          ) : null}
        </View>

        {/* Remember Me Checkbox */}
        <TouchableOpacity
          style={styles.checkboxContainer}
          onPress={() => setRememberMe(!rememberMe)}
          accessibilityLabel="Remember me checkbox"
          accessibilityRole="checkbox"
          accessibilityState={{ checked: rememberMe }}
        >
          <View style={[styles.checkbox, {
            backgroundColor: rememberMe ? theme.colors.primary : 'transparent',
            borderColor: rememberMe ? theme.colors.primary : theme.colors.border,
          }]}>
            {rememberMe && <Feather name="check" size={14} color="#FFFFFF" />}
          </View>
          <Text style={[styles.checkboxLabel, { color: theme.colors.text }]}>
            Remember this number
          </Text>
        </TouchableOpacity>

        <Button
          title={isLoading ? "Sending Code..." : "Continue"}
          variant="primary"
          onPress={handleSendOtp}
          fullWidth
          loading={isLoading}
          disabled={isLoading || phone.length !== 10}
          accessibilityLabel="Continue button"
          accessibilityHint="Sends verification code to your phone"
        />

        <Text style={[styles.infoText, { color: theme.colors.textLight }]}>
          By continuing, you agree to our Terms of Service and Privacy Policy.
          Standard message rates may apply.
        </Text>
      </Animated.View>
    </Animated.View>
  );

  // Render OTP verification step
  const renderOtpVerificationStep = () => (
    <Animated.View style={[styles.stepContainer, { transform: [{ translateX: slideAnimation }] }]}>
      <View style={styles.headerContainer}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Enter verification code
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.textLight }]}>
          We've sent a 4-digit code to +91 {formattedPhone}
        </Text>
      </View>

      <Animated.View style={[styles.formContainer, { transform: [{ translateX: shakeAnimation }] }]}>
        {otpError ? (
          <View style={[styles.errorContainer, { backgroundColor: `${theme.colors.error}15` }]}>
            <Feather name="alert-circle" size={20} color={theme.colors.error} />
            <Text style={[styles.errorText, { color: theme.colors.error }]}>
              {otpError}
            </Text>
          </View>
        ) : null}

        {/* OTP Input */}
        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={ref => (otpInputRefs.current[index] = ref)}
              style={[styles.otpInput, {
                borderColor: otpError ? theme.colors.error :
                          digit ? theme.colors.primary : theme.colors.border,
                backgroundColor: theme.colors.card,
                color: theme.colors.text,
              }]}
              value={digit}
              onChangeText={text => handleOtpChange(text, index)}
              onKeyPress={({ nativeEvent }) => handleOtpKeyPress(nativeEvent.key, index)}
              keyboardType="number-pad"
              maxLength={1}
              textAlign="center"
              selectTextOnFocus
              accessibilityLabel={`OTP digit ${index + 1}`}
              editable={!isVerifying}
            />
          ))}
        </View>

        {/* Resend OTP */}
        <View style={styles.resendContainer}>
          {resendTimer > 0 ? (
            <Text style={[styles.resendText, { color: theme.colors.textLight }]}>
              Resend code in {resendTimer}s
            </Text>
          ) : (
            <TouchableOpacity
              onPress={handleResendOtp}
              disabled={isLoading}
              accessibilityLabel="Resend verification code"
            >
              <Text style={[styles.resendButton, { color: theme.colors.primary }]}>
                Resend Code
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <Button
          title={isVerifying ? "Verifying..." : "Verify"}
          variant="primary"
          onPress={() => handleVerifyOtp()}
          fullWidth
          loading={isVerifying}
          disabled={isVerifying || otp.some(digit => !digit)}
          accessibilityLabel="Verify OTP button"
          accessibilityHint="Verifies the entered OTP code"
        />

        {/* Development OTP display */}
        {__DEV__ && devOtp && (
          <View style={[styles.devOtpContainer, { backgroundColor: `${theme.colors.warning}20` }]}>
            <Text style={[styles.devOtpText, { color: theme.colors.warning }]}>
              DEV: {devOtp}
            </Text>
          </View>
        )}

        <TouchableOpacity onPress={handleBack} style={styles.changeNumberButton}>
          <Text style={[styles.changeNumberText, { color: theme.colors.textLight }]}>
            Change phone number
          </Text>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );

  // Render success step
  const renderSuccessStep = () => (
    <Animated.View style={[styles.stepContainer, styles.successContainer]}>
      <Animated.View style={[styles.successIcon, { opacity: successAnimation }]}>
        <Feather name="check-circle" size={80} color={theme.colors.primary} />
      </Animated.View>
      <Text style={[styles.successTitle, { color: theme.colors.text }]}>
        Verification Successful!
      </Text>
      <Text style={[styles.successSubtitle, { color: theme.colors.textLight }]}>
        Welcome to CleanConnect
      </Text>
      <ActivityIndicator size="large" color={theme.colors.primary} style={styles.successLoader} />
    </Animated.View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar backgroundColor={theme.colors.background} barStyle="dark-content" />

      {/* Header with back button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBack}
          accessibilityLabel="Go back"
        >
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>

        {/* Progress indicator */}
        <View style={styles.progressContainer}>
          <View style={[styles.progressDot, {
            backgroundColor: currentStep === VerificationStep.PHONE_INPUT ? theme.colors.primary : theme.colors.border
          }]} />
          <View style={[styles.progressDot, {
            backgroundColor: currentStep === VerificationStep.OTP_VERIFICATION || currentStep === VerificationStep.SUCCESS
              ? theme.colors.primary : theme.colors.border
          }]} />
        </View>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            {/* App Logo */}
            <View style={styles.logoContainer}>
              <LinearGradient
                colors={[theme.colors.primary, theme.colors.secondary]}
                style={styles.logoGradient}
              >
                <Feather name="smartphone" size={32} color="#FFFFFF" />
              </LinearGradient>
              <Text style={[styles.appName, { color: theme.colors.text }]}>
                CleanConnect
              </Text>
            </View>

            {/* Render current step */}
            {currentStep === VerificationStep.PHONE_INPUT && renderPhoneInputStep()}
            {currentStep === VerificationStep.OTP_VERIFICATION && renderOtpVerificationStep()}
            {currentStep === VerificationStep.SUCCESS && renderSuccessStep()}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingTop: Platform.OS === 'ios' ? 16 : 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    minHeight: Dimensions.get('window').height * 0.8,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  logoGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  formContainer: {
    width: '100%',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 59, 48, 0.2)',
  },
  errorText: {
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
    fontWeight: '500',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  phoneInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    height: 56,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  countryCodeContainer: {
    paddingRight: 12,
    borderRightWidth: 1,
    borderRightColor: 'rgba(0,0,0,0.1)',
    marginRight: 12,
  },
  countryCode: {
    fontSize: 16,
    fontWeight: '600',
  },
  phoneInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 0.5,
  },
  fieldErrorText: {
    fontSize: 12,
    marginTop: 6,
    fontWeight: '500',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 4,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkboxLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoText: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 20,
    lineHeight: 18,
    paddingHorizontal: 10,
  },
  // OTP Styles
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  otpInput: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderRadius: 12,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  resendText: {
    fontSize: 14,
    fontWeight: '500',
  },
  resendButton: {
    fontSize: 14,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  devOtpContainer: {
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    alignItems: 'center',
  },
  devOtpText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  changeNumberButton: {
    alignItems: 'center',
    marginTop: 16,
    paddingVertical: 8,
  },
  changeNumberText: {
    fontSize: 14,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  // Success Styles
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  successIcon: {
    marginBottom: 24,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  successSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
  },
  successLoader: {
    marginTop: 16,
  },
});

export default PhoneVerificationScreen;
