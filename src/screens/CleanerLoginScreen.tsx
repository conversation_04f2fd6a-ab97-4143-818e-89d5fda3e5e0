"use client"

import React, { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
} from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Button from "../components/Button"
import Storage from "../utils/storage"

type CleanerLoginScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

const CleanerLoginScreen = () => {
  const navigation = useNavigation<CleanerLoginScreenNavigationProp>()
  const theme = useTheme()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  // Get styles with the current theme
  const styles = getStyles(theme)

  const handleLogin = async () => {
    // Validate inputs
    if (!email || !password) {
      Alert.alert("Missing Information", "Please enter both email and password.")
      return
    }

    setIsLoading(true)

    try {
      // In a real app, this would be an API call to authenticate the user
      // For demo purposes, we'll simulate a successful login after a short delay
      setTimeout(async () => {
        // Store user role and authentication status
        await Storage.setItem("userRole", "provider")
        await Storage.setItem("isAuthenticated", "true")

        // Navigate to cleaner dashboard
        navigation.reset({
          index: 0,
          routes: [{ name: "CleanerDashboard" }],
        })

        setIsLoading(false)
      }, 1500)
    } catch (error) {
      console.error("Login error:", error)
      Alert.alert("Login Failed", "Please check your credentials and try again.")
      setIsLoading(false)
    }
  }

  const handleSwitchToCustomer = () => {
    navigation.navigate("Login")
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.navigate("Onboarding")}
        >
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.switchRoleButton} onPress={handleSwitchToCustomer}>
          <Text style={styles.switchRoleText}>Not a cleaner? Switch to customer</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.logoContainer}>
        <Image
          source={{ uri: "https://picsum.photos/id/24/200/200" }}
          style={styles.logo}
        />
      </View>

      <Text style={styles.title}>Sign in as a Cleaner</Text>
      <Text style={styles.subtitle}>
        Access your cleaner dashboard to manage jobs and availability
      </Text>

      <View style={styles.form}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Email</Text>
          <View style={styles.inputWrapper}>
            <Feather name="mail" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Enter your email"
              placeholderTextColor={theme.colors.textLight}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Password</Text>
          <View style={styles.inputWrapper}>
            <Feather name="lock" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Enter your password"
              placeholderTextColor={theme.colors.textLight}
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />
          </View>
        </View>

        <TouchableOpacity style={styles.forgotPasswordContainer}>
          <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
        </TouchableOpacity>

        <Button
          title={isLoading ? "Signing in..." : "Login"}
          variant="warning"
          fullWidth
          onPress={handleLogin}
          disabled={isLoading}
        />

        <View style={styles.registerContainer}>
          <Text style={styles.registerText}>
            Don'&apos't have an account?{" "}
            <Text
              style={styles.registerLink}
              onPress={() => navigation.navigate("CleanerRegistration")}
            >
              Register here
            </Text>
          </Text>
        </View>
      </View>
    </ScrollView>
  )
}

// Create styles using the theme
const getStyles = (theme) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1,
  },
  contentContainer: {
    padding: theme.spacing.lg,
  },
  header: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: theme.spacing.lg,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  switchRoleButton: {
    padding: theme.spacing.xs,
  },
  switchRoleText: {
    color: theme.colors.primary,
    fontSize: theme.fontSizes.sm,
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: theme.spacing.lg,
  },
  logo: {
    borderRadius: 50,
    height: 100,
    width: 100,
  },
  title: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    marginBottom: theme.spacing.sm,
    textAlign: "center",
  },
  subtitle: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.md,
    marginBottom: theme.spacing.xl,
    textAlign: "center",
  },
  form: {
    marginTop: theme.spacing.md,
  },
  inputContainer: {
    marginBottom: theme.spacing.md,
  },
  label: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.sm,
    fontWeight: "500",
    marginBottom: theme.spacing.xs,
  },
  inputWrapper: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    flexDirection: "row",
    paddingHorizontal: theme.spacing.sm,
  },
  inputIcon: {
    marginRight: theme.spacing.sm,
  },
  input: {
    color: theme.colors.text,
    flex: 1,
    fontSize: theme.fontSizes.md,
    paddingVertical: theme.spacing.md,
  },
  forgotPasswordContainer: {
    alignItems: "flex-end",
    marginBottom: theme.spacing.lg,
  },
  forgotPasswordText: {
    color: theme.colors.primary,
    fontSize: theme.fontSizes.sm,
  },
  registerContainer: {
    alignItems: "center",
    marginTop: theme.spacing.lg,
  },
  registerText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
  },
  registerLink: {
    color: theme.colors.primary,
    fontWeight: "500",
  },
})

export default CleanerLoginScreen
