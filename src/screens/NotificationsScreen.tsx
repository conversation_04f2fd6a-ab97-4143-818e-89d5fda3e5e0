"use client"

import { useState, useMemo, useCallback } from "react"
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Platform,
  StatusBar,
  RefreshControl,
  ActivityIndicator
} from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useTheme } from "../context/ThemeContext"
import { useNotifications } from "../context/NotificationContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Header from "../components/layout/Header"

type NotificationsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

// Define notification types
type NotificationType = "booking" | "payment" | "system" | "promo"

interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  time: string
  read: boolean
  actionable: boolean
  actionText?: string
  actionRoute?: keyof RootStackParamList
  actionParams?: any
}

const NotificationsScreen = () => {
  const navigation = useNavigation<NotificationsScreenNavigationProp>()
  const theme = useTheme()
  const insets = useSafeAreaInsets()

  // Get screen dimensions for responsive layout
  const { width, height } = Dimensions.get("window")
  const isSmallScreen = height < 700

  // Get notifications from context
  const {
    notifications,
    isLoading,
    isRefreshing,
    isOffline,
    markAsRead: markNotificationAsRead,
    markAllAsRead: markAllNotificationsAsRead,
    handleRefresh
  } = useNotifications();

  // Convert notification data to UI format
  const formattedNotifications: Notification[] = useMemo(() => {
    return notifications.map(notification => {
      // Determine notification type based on the notification.type from backend
      let type: NotificationType = "system";
      if (notification.type.includes("BOOKING")) {
        type = "booking";
      } else if (notification.type.includes("PAYMENT")) {
        type = "payment";
      } else if (notification.type.includes("PROMO")) {
        type = "promo";
      }

      // Format time string
      const createdAt = new Date(notification.createdAt);
      const now = new Date();
      const diffMs = now.getTime() - createdAt.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMins / 60);
      const diffDays = Math.floor(diffHours / 24);

      let timeString = "";
      if (diffMins < 60) {
        timeString = `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
      } else if (diffHours < 24) {
        timeString = `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
      } else if (diffDays < 7) {
        timeString = `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
      } else {
        timeString = createdAt.toLocaleDateString();
      }

      return {
        id: notification.id,
        type,
        title: notification.title,
        message: notification.message,
        time: timeString,
        read: notification.isRead,
        actionable: !!notification.actionRoute,
        actionText: "View Details",
        actionRoute: notification.actionRoute as keyof RootStackParamList,
        actionParams: notification.actionParams
      };
    });
  }, [notifications]);

  // Mark notification as read
  const handleMarkAsRead = async (id: string) => {
    await markNotificationAsRead(id);
  }

  // Handle notification action
  const handleAction = async (notification: Notification) => {
    await handleMarkAsRead(notification.id);
    if (notification.actionable && notification.actionRoute) {
      // Type assertion to handle the navigation properly
      navigation.navigate(notification.actionRoute as any, notification.actionParams);
    }
  }

  // Get icon based on notification type
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case "booking":
        return "calendar"
      case "payment":
        return "dollar-sign"
      case "system":
        return "info"
      case "promo":
        return "gift"
      default:
        return "bell"
    }
  }

  // Get icon color based on notification type
  const getNotificationColor = (type: NotificationType) => {
    switch (type) {
      case "booking":
        return theme.colors.primary
      case "payment":
        return theme.colors.success
      case "system":
        return theme.colors.accent
      case "promo":
        return theme.colors.warning
      default:
        return theme.colors.text
    }
  }

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    await markAllNotificationsAsRead();
  }

  // Render notification item
  const renderNotificationItem = ({ item }: { item: Notification }) => {
    const iconName = getNotificationIcon(item.type)
    const iconColor = getNotificationColor(item.type)

    return (
      <TouchableOpacity
        style={[
          styles.notificationItem,
          !item.read && styles.unreadNotification,
        ]}
        onPress={() => handleMarkAsRead(item.id)}
        activeOpacity={0.7}
      >
        <View style={[styles.iconContainer, { backgroundColor: `${iconColor}20` }]}>
          <Feather name={iconName} size={20} color={iconColor} />
        </View>
        <View style={styles.notificationContent}>
          <View style={styles.notificationItemHeader}>
            <Text style={styles.notificationTitle}>{item.title}</Text>
            <Text style={styles.notificationTime}>{item.time}</Text>
          </View>
          <Text style={styles.notificationMessage}>{item.message}</Text>
          {item.actionable && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleAction(item)}
            >
              <Text style={styles.actionButtonText}>{item.actionText}</Text>
            </TouchableOpacity>
          )}
        </View>
        {!item.read && <View style={styles.unreadIndicator} />}
      </TouchableOpacity>
    )
  }

  // Memoize styles to prevent recreation on each render
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    statusBarSpace: {
      height: insets.top,
      backgroundColor: theme.colors.primary,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    notificationHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
      paddingBottom: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: `${theme.colors.border}80`,
    },
    headerTitleContainer: {
      flex: 1,
    },
    headerTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      color: theme.colors.text,
    },
    subHeaderTitle: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      marginTop: 2,
    },
    markAllButton: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      backgroundColor: `${theme.colors.primary}10`,
      borderRadius: theme.borderRadius.sm,
    },
    markAllText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.primary,
      fontWeight: "500",
    },
    notificationsList: {
      paddingBottom: theme.spacing.xl,
    },
    notificationItem: {
      flexDirection: "row",
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.sm,
      borderLeftWidth: 3,
      borderLeftColor: "transparent",
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    unreadNotification: {
      borderLeftColor: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}08`,
    },
    iconContainer: {
      width: isSmallScreen ? 36 : 40,
      height: isSmallScreen ? 36 : 40,
      borderRadius: isSmallScreen ? 18 : 20,
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.md,
    },
    notificationContent: {
      flex: 1,
    },
    notificationItemHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 4,
    },
    notificationTitle: {
      fontSize: isSmallScreen ? theme.fontSizes.sm : theme.fontSizes.md,
      fontWeight: "600",
      flex: 1,
    },
    notificationTime: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      marginLeft: theme.spacing.sm,
    },
    notificationMessage: {
      fontSize: isSmallScreen ? theme.fontSizes.xs : theme.fontSizes.sm,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      lineHeight: isSmallScreen ? 16 : 20,
    },
    actionButton: {
      alignSelf: "flex-start",
      paddingVertical: theme.spacing.xs,
      paddingHorizontal: theme.spacing.sm,
      backgroundColor: `${theme.colors.primary}15`,
      borderRadius: theme.borderRadius.sm,
      borderWidth: 1,
      borderColor: `${theme.colors.primary}20`,
    },
    actionButtonText: {
      fontSize: theme.fontSizes.xs,
      fontWeight: "500",
      color: theme.colors.primary,
    },
    unreadIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.colors.primary,
      position: "absolute",
      top: theme.spacing.md,
      right: theme.spacing.md,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    emptyTitle: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      marginTop: theme.spacing.lg,
      marginBottom: theme.spacing.sm,
      color: theme.colors.text,
    },
    emptyMessage: {
      fontSize: theme.fontSizes.md,
      color: theme.colors.textLight,
      textAlign: "center",
      maxWidth: 250,
    },
  }), [theme, width, height, isSmallScreen, insets])

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Status Bar Space */}
      <View style={styles.statusBarSpace} />

      <Header title="Notifications" showBackButton />

      {isOffline && (
        <View style={{ backgroundColor: theme.colors.notification, padding: theme.spacing.sm, alignItems: 'center' }}>
          <Text style={{ color: theme.colors.background, fontSize: theme.fontSizes.sm, fontWeight: '500' }}>
            You are offline. Using cached data.
          </Text>
        </View>
      )}

        <View style={styles.content}>
          {isLoading ? (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={{ marginTop: theme.spacing.md, color: theme.colors.text }}>
                Loading notifications...
              </Text>
            </View>
          ) : formattedNotifications.length > 0 ? (
            <>
              <View style={styles.notificationHeader}>
                <View style={styles.headerTitleContainer}>
                  <Text style={styles.headerTitle}>
                    {formattedNotifications.filter(n => !n.read).length} New Notifications
                  </Text>
                  <Text style={styles.subHeaderTitle}>Pull down to refresh</Text>
                </View>
                <TouchableOpacity
                  style={styles.markAllButton}
                  onPress={handleMarkAllAsRead}
                  activeOpacity={0.7}
                >
                  <Text style={styles.markAllText}>Mark all as read</Text>
                </TouchableOpacity>
              </View>

              <FlatList
                data={formattedNotifications}
                renderItem={renderNotificationItem}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.notificationsList}
                refreshControl={
                  <RefreshControl
                    refreshing={isRefreshing}
                    onRefresh={handleRefresh}
                    colors={[theme.colors.primary]}
                    tintColor={theme.colors.primary}
                  />
                }
              />
            </>
          ) : (
            <View style={styles.emptyContainer}>
              <Feather name="bell-off" size={64} color={theme.colors.textLight} />
              <Text style={styles.emptyTitle}>No Notifications</Text>
              <Text style={styles.emptyMessage}>
                You don't have any notifications at the moment.
              </Text>
            </View>
          )}
        </View>
    </View>
  )
}

export default NotificationsScreen
