import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  StatusBar,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Switch,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../context/ThemeContext';
import { useLocation } from '../context/LocationContext';
import type { Location } from '../context/LocationContext';
import type { RootStackParamList } from '../navigation/RootNavigator';
import Button from '../components/Button';
import * as ExpoLocation from 'expo-location';

type AddAddressScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;
type AddAddressScreenRouteProp = RouteProp<RootStackParamList, 'AddAddress'>;

type AddressLabelType = 'Home' | 'Work' | 'Other';

interface AddressFormData {
  label: AddressLabelType;
  address: string;
  area: string;
  isPrimary: boolean;
  plusCode: string;
}

const INITIAL_FORM_DATA: AddressFormData = {
  label: 'Home',
  address: '',
  area: '',
  isPrimary: false,
  plusCode: '',
};

// Helper function to generate a friendly location name in neighborhood-city format
const generateFriendlyLocationName = (address: ExpoLocation.LocationGeocodedAddress): string => {
  // For the best format, try to combine district/neighborhood with city
  if (address.district && address.city) {
    return `${address.district}-${address.city}`;
  }

  // If we have a subregion and city, use that combination
  if (address.subregion && address.city && !containsNumbers(address.subregion)) {
    return `${address.subregion}-${address.city}`;
  }

  // If we have a named point of interest and city, combine them
  if (address.name && address.city && !containsNumbers(address.name)) {
    return `${address.name}-${address.city}`;
  }

  // If we have just a district/neighborhood, use that
  if (address.district) {
    return address.district;
  }

  // If we have a named point of interest, use that
  if (address.name && !containsNumbers(address.name)) {
    return address.name;
  }

  // For urban areas, use the subregion name
  if (address.subregion && !containsNumbers(address.subregion)) {
    return address.subregion;
  }

  // Use city name
  if (address.city) {
    return address.city;
  }

  // Use region/state name
  if (address.region) {
    return address.region;
  }

  // If we have a street name without numbers, use that
  if (address.street && !containsNumbers(address.street)) {
    // Extract just the street name without any numbers
    const streetNameOnly = extractStreetName(address.street);
    return streetNameOnly || address.street;
  }

  // Fallback to country
  if (address.country) {
    return address.country;
  }

  // Last resort fallback
  return "Current Location";
};

// Helper function to check if a string contains numbers
const containsNumbers = (str: string): boolean => {
  return /\d/.test(str);
};

// Helper function to extract just the street name without numbers
const extractStreetName = (street: string): string => {
  // Remove any numbers and extra spaces
  const nameOnly = street.replace(/\d+/g, '').replace(/^\s+|\s+$/g, '').replace(/\s+/g, ' ');

  // Remove any leading commas, periods, or other punctuation
  const cleanName = nameOnly.replace(/^[,.\s]+/, '');

  return cleanName;
};

const AddAddressScreen = () => {
  const navigation = useNavigation<AddAddressScreenNavigationProp>();
  const route = useRoute<AddAddressScreenRouteProp>();
  const theme = useTheme();
  const { addLocation, updateLocation, currentLocation, setCurrentLocation } = useLocation();

  // Get edit data if passed
  const editAddress = route.params?.address;
  const isEditMode = !!editAddress;

  // Form state
  const [formData, setFormData] = useState<AddressFormData>(
    isEditMode
      ? {
          label: (editAddress.label as AddressLabelType) || 'Home',
          address: editAddress.address || '',
          area: editAddress.area || '',
          isPrimary: editAddress.isPrimary || false,
          plusCode: '',
        }
      : INITIAL_FORM_DATA
  );
  const [useGpsLocation, setUseGpsLocation] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle form input changes
  const handleInputChange = (field: keyof AddressFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle GPS location selection
  const handleUseGpsLocation = async () => {
    try {
      // Request location permission
      const { status } = await ExpoLocation.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          "Permission Denied",
          "We need location permission to use your current location. Please enable it in your device settings.",
          [{ text: "OK" }]
        );
        return;
      }

      // Show loading alert
      Alert.alert(
        "Getting Location",
        "Please wait while we get your current location...",
        [{ text: "Cancel", style: "cancel" }],
        { cancelable: true }
      );

      // Get current location
      const location = await ExpoLocation.getCurrentPositionAsync({
        accuracy: ExpoLocation.Accuracy.Balanced,
      });

      // Get address from coordinates
      const [address] = await ExpoLocation.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      if (address) {
        // Generate a friendly location name
        const locationName = generateFriendlyLocationName(address);

        // Format the full address in a readable way
        const formattedAddress = [
          address.name,
          address.street,
          address.district,
          address.city,
          address.region,
          address.postalCode,
          address.country
        ]
          .filter(Boolean)
          .join(', ');

        // Format the area in neighborhood-city format
        let areaName = '';
        if (address.district && address.city) {
          areaName = `${address.district}-${address.city}`;
        } else if (address.subregion && address.city) {
          areaName = `${address.subregion}-${address.city}`;
        } else if (address.name && address.city && !containsNumbers(address.name)) {
          areaName = `${address.name}-${address.city}`;
        } else {
          areaName = address.city || address.region || '';
        }

        // Update form data
        setUseGpsLocation(true);
        setFormData(prev => ({
          ...prev,
          address: formattedAddress,
          area: areaName,
        }));

        // Dismiss the loading alert by showing a success alert
        Alert.alert(
          "Location Selected",
          `Your location "${locationName}" has been added to the form.`,
          [{ text: "OK" }]
        );
      } else {
        Alert.alert(
          "Location Error",
          "We couldn't determine your address from your GPS location. Please enter it manually.",
          [{ text: "OK" }]
        );
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert(
        "Location Error",
        "There was an error getting your location. Please try again or enter it manually.",
        [{ text: "OK" }]
      );
    }
  };

  // Handle Plus Code lookup
  const handlePlusCodeLookup = () => {
    if (!formData.plusCode.trim()) {
      Alert.alert("Error", "Please enter a Plus Code");
      return;
    }

    // In a real app, this would validate and geocode the Plus Code
    Alert.alert(
      "Plus Code Lookup",
      `This would search for location using Plus Code: ${formData.plusCode}`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Simulate Result",
          onPress: () => {
            setFormData(prev => ({
              ...prev,
              address: `Location from Plus Code: ${formData.plusCode}`,
              area: 'Banjul',
            }));
          }
        }
      ]
    );
  };

  // Handle save address
  const handleSaveAddress = async () => {
    // Validate form
    if (!formData.address.trim()) {
      Alert.alert("Error", "Please enter an address");
      return;
    }

    try {
      setIsSubmitting(true);

      if (isEditMode && editAddress) {
        // Update existing address
        const updatedAddress: Location = {
          ...editAddress,
          label: formData.label,
          address: formData.address,
          area: formData.area,
          isPrimary: formData.isPrimary,
        };

        await updateLocation(updatedAddress);

        // If this was the current location, update it
        if (currentLocation?.id === updatedAddress.id) {
          await setCurrentLocation(updatedAddress);
        }

        Alert.alert(
          "Address Updated",
          "Your address has been updated successfully.",
          [{ text: "OK", onPress: () => navigation.goBack() }]
        );
      } else {
        // Add new address
        const newLocation: Partial<Location> = {
          id: `manual_${Date.now()}`,
          label: formData.label,
          address: formData.address,
          area: formData.area,
          isPrimary: formData.isPrimary,
        };

        await addLocation(newLocation as Location);
        Alert.alert(
          "Address Added",
          "Your new address has been added successfully.",
          [{ text: "OK", onPress: () => navigation.goBack() }]
        );
      }
    } catch (error) {
      Alert.alert("Error", "Failed to save address. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar barStyle="dark-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Feather name="chevron-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          {isEditMode ? 'Edit Address' : 'Add New Address'}
        </Text>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Address Label Selection */}
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Address Label</Text>
          <View style={styles.labelContainer}>
            {(['Home', 'Work', 'Other'] as const).map((label) => (
              <TouchableOpacity
                key={label}
                style={[
                  styles.labelButton,
                  { borderColor: theme.colors.border },
                  formData.label === label && [
                    styles.selectedLabelButton,
                    { backgroundColor: `${theme.colors.primary}20`, borderColor: theme.colors.primary }
                  ]
                ]}
                onPress={() => handleInputChange('label', label)}
              >
                <Feather
                  name={label === 'Home' ? 'home' : label === 'Work' ? 'briefcase' : 'map-pin'}
                  size={16}
                  color={formData.label === label ? theme.colors.primary : theme.colors.textLight}
                  style={styles.labelIcon}
                />
                <Text
                  style={[
                    styles.labelText,
                    { color: formData.label === label ? theme.colors.primary : theme.colors.textLight }
                  ]}
                >
                  {label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* GPS Location */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Use GPS Location</Text>
            <TouchableOpacity
              style={[styles.gpsButton, { borderColor: theme.colors.border }]}
              onPress={handleUseGpsLocation}
            >
              <Feather name="map-pin" size={20} color={theme.colors.primary} style={styles.gpsIcon} />
              <Text style={[styles.gpsText, { color: theme.colors.text }]}>
                {useGpsLocation ? 'GPS Location Added' : 'Choose Current Location'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Plus Code */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Google Plus Code</Text>
            <View style={styles.plusCodeContainer}>
              <TextInput
                style={[styles.plusCodeInput, {
                  borderColor: theme.colors.border,
                  color: theme.colors.text
                }]}
                placeholder="Enter Plus Code"
                placeholderTextColor={theme.colors.textLight}
                value={formData.plusCode}
                onChangeText={(text) => handleInputChange('plusCode', text)}
              />
              <TouchableOpacity
                style={[styles.plusCodeButton, { backgroundColor: theme.colors.primary }]}
                onPress={handlePlusCodeLookup}
              >
                <Text style={styles.plusCodeButtonText}>Lookup</Text>
              </TouchableOpacity>
            </View>
            <TouchableOpacity onPress={() => {
              Alert.alert(
                "Google Plus Code",
                "A Plus Code is a short code for your address that can be used to find locations where street addresses don't exist. Find your Plus Code on Google Maps."
              );
            }}>
              <Text style={[styles.plusCodeHelp, { color: theme.colors.primary }]}>
                What is a Plus Code?
              </Text>
            </TouchableOpacity>
          </View>

          {/* Address Details */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Address Details</Text>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Full Address</Text>
            <TextInput
              style={[styles.textInput, {
                borderColor: theme.colors.border,
                color: theme.colors.text
              }]}
              placeholder="Enter your address"
              placeholderTextColor={theme.colors.textLight}
              value={formData.address}
              onChangeText={(text) => handleInputChange('address', text)}
              multiline
              numberOfLines={3}
            />

            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Area/City</Text>
            <TextInput
              style={[styles.textInput, {
                borderColor: theme.colors.border,
                color: theme.colors.text
              }]}
              placeholder="Enter area or city"
              placeholderTextColor={theme.colors.textLight}
              value={formData.area}
              onChangeText={(text) => handleInputChange('area', text)}
            />
          </View>

          {/* Primary Address Toggle */}
          <View style={styles.primaryContainer}>
            <Text style={[styles.primaryText, { color: theme.colors.text }]}>
              Set as primary address
            </Text>
            <Switch
              value={formData.isPrimary}
              onValueChange={(value) => handleInputChange('isPrimary', value)}
              trackColor={{ false: theme.colors.border, true: `${theme.colors.primary}80` }}
              thumbColor={formData.isPrimary ? theme.colors.primary : '#f4f3f4'}
            />
          </View>

          {/* Save Button */}
          <View style={styles.buttonContainer}>
            <Button
              title={isEditMode ? "Update Address" : "Save Address"}
              onPress={handleSaveAddress}
              loading={isSubmitting}
              disabled={!formData.address.trim() || isSubmitting}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 20, // Fixed margin top
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 16,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  section: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  labelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 10,
  },
  selectedLabelButton: {
    borderWidth: 1,
  },
  labelIcon: {
    marginRight: 5,
  },
  labelText: {
    fontSize: 14,
  },
  gpsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
  },
  gpsIcon: {
    marginRight: 10,
  },
  gpsText: {
    fontSize: 16,
  },
  plusCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  plusCodeInput: {
    flex: 1,
    height: 45,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  plusCodeButton: {
    height: 45,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  plusCodeButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  plusCodeHelp: {
    fontSize: 12,
    textAlign: 'right',
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
    minHeight: 45,
  },
  primaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  primaryText: {
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    marginTop: 8,
  },
});

export default AddAddressScreen;
