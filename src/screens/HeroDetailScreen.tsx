import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../context/ThemeContext';
import { useResponsive } from '../hooks/useResponsive';
import type { RootStackParamList } from '../navigation/RootNavigator';
import { Button, Card } from '../components/common';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { localProviderService } from '../services/localProviderService';
import { localServiceService } from '../services/localServiceService';
import { Service } from '../types/service';
import { Provider } from '../types/provider';

// Define Hero type to extend Provider
interface Hero extends Provider {
  reviews: number;
  price: number;
}

type HeroDetailScreenRouteProp = RouteProp<RootStackParamList, 'HeroDetail'>;
type HeroDetailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const HeroDetailScreen = () => {
  const navigation = useNavigation<HeroDetailScreenNavigationProp>();
  const route = useRoute<HeroDetailScreenRouteProp>();
  const theme = useTheme();
  const { height } = useResponsive();
  const insets = useSafeAreaInsets();

  const heroId = route.params?.heroId;
  const [hero, setHero] = useState<Hero | null>(null);
  const [loading, setLoading] = useState(true);
  const [services, setServices] = useState<Service[]>([]);

  // Define styles early so they can be used in early returns
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    statusBarSpace: {
      height: insets.top,
      backgroundColor: theme.colors.primary,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 16,
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      zIndex: 10,
    },
    backButton: {
      padding: 8,
    },
    favoriteButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '700',
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      fontWeight: '500',
    },
    scrollView: {
      flex: 1,
    },
    heroBanner: {
      position: 'relative',
      width: '100%',
      height: 280,
      overflow: 'hidden',
    },
    heroImage: {
      width: '100%',
      height: '100%',
    },
    imageGradient: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 160,
    },
    heroImageTextContainer: {
      position: 'absolute',
      bottom: 20,
      left: 20,
      right: 20,
    },
    heroImageName: {
      fontSize: 28,
      fontWeight: '700',
      color: 'white',
      textShadowColor: 'rgba(0, 0, 0, 0.75)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 3,
      marginBottom: 8,
    },
    heroImageRating: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    heroImageRatingText: {
      color: 'white',
      fontSize: 16,
      fontWeight: '700',
      marginLeft: 4,
      textShadowColor: 'rgba(0, 0, 0, 0.75)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 3,
    },
    heroOfWeekBadge: {
      position: 'absolute',
      top: 16,
      right: 16,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    heroOfWeekText: {
      color: '#000',
      fontWeight: '700',
      fontSize: 12,
    },
    heroInfoContainer: {
      padding: 16,
    },
    profileCard: {
      marginTop: -30,
      marginBottom: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    heroNameRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 12,
    },
    heroName: {
      fontSize: 24,
      fontWeight: '700',
      marginBottom: 4,
    },
    heroTagline: {
      fontSize: 16,
    },
    verifiedBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 16,
    },
    verifiedText: {
      fontSize: 12,
      fontWeight: '600',
    },
    ratingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    ratingStars: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    ratingText: {
      fontSize: 16,
      fontWeight: '700',
      marginLeft: 4,
      marginRight: 4,
    },
    reviewsText: {
      fontSize: 14,
    },
    priceBadge: {
      backgroundColor: '#F0F8FF',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    priceText: {
      fontSize: 16,
      fontWeight: '700',
      color: '#0066CC',
    },
    sectionCard: {
      marginBottom: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
    },
    sectionTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '700',
      marginLeft: 8,
    },
    bioText: {
      fontSize: 15,
      lineHeight: 22,
      marginBottom: 16,
      color: '#444',
    },
    experienceRow: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#F5F5F5',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
    },
    experienceText: {
      fontSize: 14,
      fontWeight: '600',
    },
    tagContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    tag: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 16,
      marginRight: 8,
      marginBottom: 8,
    },
    tagText: {
      fontSize: 13,
      fontWeight: '500',
    },
    areaContainer: {
      marginTop: 4,
    },
    areaRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
      paddingVertical: 4,
    },
    areaText: {
      fontSize: 15,
    },
    toolsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 8,
    },
    toolItem: {
      flexDirection: 'row',
      alignItems: 'center',
      width: '50%',
      marginBottom: 12,
    },
    toolIconContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: '#4CAF50',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 8,
    },
    toolText: {
      fontSize: 14,
      flex: 1,
    },
    reviewHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    viewAllText: {
      fontSize: 14,
      fontWeight: '600',
    },
    reviewItem: {
      marginBottom: 16,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    reviewerInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    reviewerAvatar: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: '#E0E0E0',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 10,
    },
    reviewerInitial: {
      fontSize: 16,
      fontWeight: '700',
      color: '#555',
    },
    reviewerName: {
      fontSize: 15,
      fontWeight: '600',
    },
    reviewDate: {
      fontSize: 12,
      marginTop: 2,
    },
    reviewRating: {
      flexDirection: 'row',
      marginVertical: 8,
    },
    reviewComment: {
      fontSize: 14,
      lineHeight: 20,
      color: '#444',
    },
    bookButtonContainer: {
      padding: 16,
      borderTopWidth: 1,
      borderTopColor: '#EEEEEE',
      elevation: 8,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    bookButton: {
      width: '100%',
      borderRadius: 12,
      paddingVertical: 14,
    },
  });

  // Load hero data from API
  useEffect(() => {
    const loadHero = async () => {
      if (!heroId) return;

      try {
        setLoading(true);
        console.log(`Loading hero data for ID: ${heroId}`);

        // Try to get provider data from local storage
        const provider = await localProviderService.getProviderById(heroId);

        if (provider) {
          console.log(`Successfully loaded hero data: ${provider.name}`);

          // Ensure all required fields are present
          const completeProvider: Hero = {
            id: provider.id || heroId,
            name: provider.name || 'Professional Cleaner',
            image: provider.image || 'https://randomuser.me/api/portraits/people/22.jpg',
            rating: provider.rating || 4.5,
            reviews: provider.jobs || 15,
            verified: provider.verified || true,
            price: provider.price || 250
          };

          setHero(completeProvider);
        } else {
          console.error(`Provider data not found for ID: ${heroId}`);

          // Create a professional fallback provider if data is not available
          const fallbackProvider: Hero = {
            id: heroId,
            name: 'Professional Cleaner',
            image: 'https://randomuser.me/api/portraits/people/22.jpg',
            rating: 4.5,
            reviews: 15,
            verified: true,
            price: 250
          };

          setHero(fallbackProvider);
        }
      } catch (error) {
        console.error(`Error loading hero ${heroId}:`, error);

        // Create a professional fallback provider if an error occurs
        const fallbackProvider: Hero = {
          id: heroId,
          name: 'Professional Cleaner',
          image: 'https://randomuser.me/api/portraits/people/22.jpg',
          rating: 4.5,
          reviews: 15,
          verified: true,
          price: 250
        };

        setHero(fallbackProvider);
      } finally {
        setLoading(false);
      }
    };

    loadHero();
  }, [heroId]);

  // Common header component
  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Feather name="chevron-left" size={24} color={theme.colors.text} />
      </TouchableOpacity>
      <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Hero Details</Text>
      <View style={{ width: 24 }} />
    </View>
  );

  // Fetch available services
  useEffect(() => {
    const fetchServices = async () => {
      try {
        // Use the local service service
        const servicesData = await localServiceService.getServices();
        setServices(servicesData);
      } catch (error: unknown) {
        console.error('Error fetching services:', error);
        // Set default services if there's an error
        setServices([
          {
            id: '191f7be4-5880-43b9-bd29-7f30c5c3f5c7',
            name: 'House Cleaning',
            title: 'House Cleaning',
            description: 'Standard cleaning service for homes',
            image: 'https://picsum.photos/id/26/600/300',
            price: 100,
            basePrice: 100,
            duration: '2 hours',
            isActive: true,
            features: ['Dusting', 'Vacuuming', 'Mopping', 'Bathroom cleaning', 'Kitchen cleaning'],
            rating: 4.8,
            reviewCount: 120
          }
        ]);
      }
    };

    fetchServices();
  }, []);

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.statusBarSpace} />
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading hero details...</Text>
        </View>
      </View>
    );
  }

  if (!hero) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.statusBarSpace} />
        {renderHeader()}
        <View style={styles.errorContainer}>
          <Text style={{ color: theme.colors.text }}>Hero not found</Text>
        </View>
      </View>
    );
  }

  // Mock data for hero details
  const heroDetails = {
    tagline: "Reliable & Friendly Cleaner",
    bio: "I have been cleaning homes and offices for over 5 years. I take pride in my work and ensure every corner is spotless. I'm punctual, trustworthy, and dedicated to providing the best cleaning service.",
    experience: 5,
    serviceTypes: ["Home Cleaning", "Office Cleaning", "Deep Cleaning"],
    workingAreas: ["Serrekunda", "Banjul", "Bakau", "Brikama"],
    toolsProvided: ["Vacuum Cleaner", "Mops", "Cleaning Solutions", "Dusters"],
    reviews: [
      { id: 1, user: "Mariama J.", rating: 5, comment: "Excellent service! My house has never been cleaner.", date: "2 weeks ago" },
      { id: 2, user: "Omar S.", rating: 4, comment: "Very professional and thorough. Will book again.", date: "1 month ago" },
      { id: 3, user: "Fatou D.", rating: 5, comment: "Punctual, friendly and did an amazing job with my office.", date: "2 months ago" }
    ],
    status: "Verified Hero",
    isHeroOfWeek: hero.rating >= 4.8
  };

  const handleBookNow = () => {
    // Get the House Cleaning service ID or fallback to the first available service
    let serviceId = '191f7be4-5880-43b9-bd29-7f30c5c3f5c7'; // Default to House Cleaning ID

    if (services && services.length > 0) {
      // Try to find House Cleaning service
      const houseCleaningService = services.find(service =>
        service.name === 'House Cleaning' || service.title === 'House Cleaning'
      );

      // If found, use its ID, otherwise use the first service
      serviceId = houseCleaningService ? houseCleaningService.id : services[0].id;
    }

    // Navigate to booking screen with valid service ID and provider information
    navigation.navigate('Booking', {
      serviceId: serviceId,
      selectedProvider: {
        id: hero.id,
        name: hero.name,
        image: hero.image,
        rating: hero.rating,
        reviews: hero.reviews,
        verified: hero.verified,
        price: hero.price
      }
    });
  };



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar barStyle="dark-content" />

      {/* Status Bar Space */}
      <View style={styles.statusBarSpace} />

      {/* Header with gradient background */}
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Feather name="chevron-left" size={24} color="white" />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: "white" }]}>Hero Profile</Text>
        <TouchableOpacity style={styles.favoriteButton}>
          <Feather name="heart" size={22} color="white" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero Banner with improved styling */}
        <View style={styles.heroBanner}>
          <Image
            source={{ uri: hero.image }}
            style={styles.heroImage}
            resizeMode="cover"
            defaultSource={require('../assets/images/profile.png')}
          />

          {/* Linear gradient overlay for better text visibility */}
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.imageGradient}
          />

          {heroDetails.isHeroOfWeek && (
            <View style={[styles.heroOfWeekBadge, { backgroundColor: theme.colors.warning }]}>
              <Feather name="award" size={12} color="#000" style={{ marginRight: 4 }} />
              <Text style={styles.heroOfWeekText}>Hero of the Week</Text>
            </View>
          )}

          {/* Hero name overlay on image */}
          <View style={styles.heroImageTextContainer}>
            <Text style={styles.heroImageName}>{hero.name}</Text>
            <View style={styles.heroImageRating}>
              <Feather name="star" size={16} color="#FFD700" />
              <Text style={styles.heroImageRatingText}>{hero.rating}</Text>
            </View>
          </View>
        </View>

        {/* Hero Info with improved layout */}
        <View style={styles.heroInfoContainer}>
          {/* Profile card with shadow */}
          <Card style={styles.profileCard}>
            <View style={styles.heroNameRow}>
              <View style={{ flex: 1 }}>
                <Text style={[styles.heroName, { color: theme.colors.text }]}>{hero.name}</Text>
                <Text style={[styles.heroTagline, { color: theme.colors.textLight }]}>{heroDetails.tagline}</Text>
              </View>
              {hero.verified && (
                <View style={[styles.verifiedBadge, { backgroundColor: `${theme.colors.primary}20` }]}>
                  <Feather name="check-circle" size={14} color={theme.colors.primary} style={{ marginRight: 4 }} />
                  <Text style={[styles.verifiedText, { color: theme.colors.primary }]}>{heroDetails.status}</Text>
                </View>
              )}
            </View>

            {/* Rating with improved layout */}
            <View style={styles.ratingContainer}>
              <View style={styles.ratingStars}>
                <Feather name="star" size={18} color="#FFD700" />
                <Text style={[styles.ratingText, { color: theme.colors.text }]}>{hero.rating}</Text>
                <Text style={[styles.reviewsText, { color: theme.colors.textLight }]}>({hero.reviews} reviews)</Text>
              </View>
              <View style={styles.priceBadge}>
                <Text style={styles.priceText}>GMD {hero.price}/hr</Text>
              </View>
            </View>
          </Card>

          {/* About with improved styling */}
          <Card style={styles.sectionCard}>
            <View style={styles.sectionTitleContainer}>
              <Feather name="user" size={18} color={theme.colors.primary} />
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>About Me</Text>
            </View>
            <Text style={[styles.bioText, { color: theme.colors.text }]}>{heroDetails.bio}</Text>
            <View style={styles.experienceRow}>
              <Feather name="clock" size={16} color={theme.colors.primary} style={{ marginRight: 8 }} />
              <Text style={[styles.experienceText, { color: theme.colors.text }]}>
                {heroDetails.experience} years of experience
              </Text>
            </View>
          </Card>

          {/* Services with improved styling */}
          <Card style={styles.sectionCard}>
            <View style={styles.sectionTitleContainer}>
              <Feather name="briefcase" size={18} color={theme.colors.primary} />
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Services Offered</Text>
            </View>
            <View style={styles.tagContainer}>
              {heroDetails.serviceTypes.map((service, index) => (
                <View
                  key={index}
                  style={[styles.tag, { backgroundColor: `${theme.colors.primary}15` }]}
                >
                  <Feather name="check" size={12} color={theme.colors.primary} style={{ marginRight: 4 }} />
                  <Text style={[styles.tagText, { color: theme.colors.primary }]}>{service}</Text>
                </View>
              ))}
            </View>
          </Card>

          {/* Working Areas with improved styling */}
          <Card style={styles.sectionCard}>
            <View style={styles.sectionTitleContainer}>
              <Feather name="map" size={18} color={theme.colors.primary} />
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Working Areas</Text>
            </View>
            <View style={styles.areaContainer}>
              {heroDetails.workingAreas.map((area, index) => (
                <View key={index} style={styles.areaRow}>
                  <Feather name="map-pin" size={16} color={theme.colors.primary} style={{ marginRight: 8 }} />
                  <Text style={[styles.areaText, { color: theme.colors.text }]}>{area}</Text>
                </View>
              ))}
            </View>
          </Card>

          {/* Tools Provided with improved styling */}
          <Card style={styles.sectionCard}>
            <View style={styles.sectionTitleContainer}>
              <Feather name="tool" size={18} color={theme.colors.primary} />
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Tools Provided</Text>
            </View>
            <View style={styles.toolsGrid}>
              {heroDetails.toolsProvided.map((tool, index) => (
                <View key={index} style={styles.toolItem}>
                  <View style={styles.toolIconContainer}>
                    <Feather name="check" size={12} color="white" />
                  </View>
                  <Text style={[styles.toolText, { color: theme.colors.text }]}>{tool}</Text>
                </View>
              ))}
            </View>
          </Card>

          {/* Reviews with improved styling */}
          <Card style={styles.sectionCard}>
            <View style={styles.sectionTitleContainer}>
              <Feather name="message-square" size={18} color={theme.colors.primary} />
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Reviews</Text>
              <TouchableOpacity style={{ marginLeft: 'auto' }}>
                <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>View all</Text>
              </TouchableOpacity>
            </View>

            {heroDetails.reviews.map((review) => (
              <View key={review.id} style={styles.reviewItem}>
                <View style={styles.reviewHeader}>
                  <View style={styles.reviewerInfo}>
                    <View style={styles.reviewerAvatar}>
                      <Text style={styles.reviewerInitial}>{review.user.charAt(0)}</Text>
                    </View>
                    <View>
                      <Text style={[styles.reviewerName, { color: theme.colors.text }]}>{review.user}</Text>
                      <Text style={[styles.reviewDate, { color: theme.colors.textLight }]}>{review.date}</Text>
                    </View>
                  </View>
                  <View style={styles.reviewRating}>
                    {[...Array(5)].map((_, i) => (
                      <Feather
                        key={i}
                        name="star"
                        size={14}
                        color={i < review.rating ? "#FFD700" : theme.colors.border}
                        style={{ marginRight: 2 }}
                      />
                    ))}
                  </View>
                </View>
                <Text style={[styles.reviewComment, { color: theme.colors.text }]}>{review.comment}</Text>
              </View>
            ))}
          </Card>
        </View>
      </ScrollView>

      {/* Book Now Button with improved styling */}
      <View style={[styles.bookButtonContainer, { backgroundColor: theme.colors.background }]}>
        <Button
          title="Book This Hero"
          onPress={handleBookNow}
          style={styles.bookButton}
          icon={<Feather name="calendar" size={18} color="white" style={{ marginRight: 8 }} />}
        />
      </View>
    </View>
  );
};

export default HeroDetailScreen;
