"use client"

import React, { useState, useCallback, useEffect, useRef } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from "react-native"
import { useNavigation, useRoute, type RouteProp } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import { useBooking } from "../context/BookingContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
// We'll dynamically import services to ensure we get the latest version
import { showImagePickerOptions } from "../utils/imagePickerFallback"
import Header from "../components/layout/Header"
import Card from "../components/common/Card"
import Button from "../components/common/Button"
import AuthModal from "../components/forms/AuthModal"
import { ToastService } from "../components/common/ToastManager"

type PaymentOptionScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>
type PaymentOptionScreenRouteProp = RouteProp<RootStackParamList, "PaymentOption">

const PaymentOptionScreen = () => {
  const navigation = useNavigation<PaymentOptionScreenNavigationProp>()
  const route = useRoute<PaymentOptionScreenRouteProp>()
  const theme = useTheme()
  const { user, isAuthenticated, userRole, isAuthModalVisible, showAuthModal, hideAuthModal } = useAuth()
  const { clearBookingData, getAbandonedBookings, markAbandonedBookingAsCompleted, setBookingData, saveBookingData, trackAbandonedBooking, restoreBookingData } = useBooking()

  // Get styles with the current theme
  const styles = getStyles(theme)

  // Track if we just returned from login
  const [returnedFromLogin, setReturnedFromLogin] = useState<boolean>(false)
  // Track if we've already processed the post-login state
  const [processedPostLogin, setProcessedPostLogin] = useState<boolean>(false)
  // Track the previous authentication state
  const prevAuthRef = useRef<boolean>(isAuthenticated)

  const {
    bookingId,
    serviceId,
    serviceTitle,
    date,
    time,
    address,
    duration,
    subtotal,
    fee,
    total,
    providerId,
  } = route.params

  // Detect changes in authentication state
  useEffect(() => {
    // If authentication state changed from false to true
    if (isAuthenticated && !prevAuthRef.current) {
      console.log('User just logged in and returned to PaymentOptionScreen')
      setReturnedFromLogin(true)

      // Restore booking data if needed
      const restoreData = async () => {
        try {
          const restored = await restoreBookingData()
          console.log('Booking data restored after login:', restored)
        } catch (error) {
          console.error('Error restoring booking data:', error)
        }
      }

      restoreData()
    }

    // Update previous auth state
    prevAuthRef.current = isAuthenticated
  }, [isAuthenticated, restoreBookingData])

  // Auto-trigger booking completion when user returns from login
  useEffect(() => {
    // If user just returned from login and is authenticated
    if (returnedFromLogin && isAuthenticated && !processedPostLogin) {
      console.log('Auto-triggering booking completion after login')
      // Short delay to ensure all state is properly updated
      const timer = setTimeout(() => {
        handleCompleteBooking()
      }, 500)

      return () => clearTimeout(timer)
    }
  }, [returnedFromLogin, isAuthenticated, processedPostLogin, handleCompleteBooking])

  // Payment state
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null)
  const [receiptImage, setReceiptImage] = useState<string | null>(null)
  const [receiptUploaded, setReceiptUploaded] = useState<boolean>(false)
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const [paymentError, setPaymentError] = useState<string | null>(null)

  // Handle payment method selection
  const handleSelectPaymentMethod = (method: string) => {
    setSelectedPaymentMethod(method)
  }

  // Calculate end time based on start time and duration
  const calculateEndTime = (startTime: string, duration: string): string => {
    try {
      // Parse the duration to get hours
      const durationHours = parseInt(duration.split(' ')[0], 10);

      // Parse the start time
      const [hourStr, minuteStr] = startTime.split(':');
      const [minute, period] = minuteStr.split(' ');

      let hour = parseInt(hourStr, 10);

      // Convert to 24-hour format
      if (period === 'PM' && hour < 12) {
        hour += 12;
      } else if (period === 'AM' && hour === 12) {
        hour = 0;
      }

      // Add duration hours
      hour = (hour + durationHours) % 24;

      // Convert back to 12-hour format
      const newPeriod = hour >= 12 ? 'PM' : 'AM';
      const newHour = hour % 12 === 0 ? 12 : hour % 12;

      // Format the end time
      return `${newHour}:${minute} ${newPeriod}`;
    } catch (error) {
      console.error('Error calculating end time:', error);
      return startTime; // Return start time as fallback
    }
  };

  // Handle receipt upload
  const handleUploadReceipt = async () => {
    try {
      console.log('Starting image picker...');

      // Show image picker options
      const imageData = await showImagePickerOptions();

      console.log('Image picker result:', imageData ? 'Image selected' : 'No image selected');

      if (!imageData) {
        console.log('User cancelled image selection');
        return; // User cancelled
      }

      // Log image data type and length
      console.log('Image data type:', typeof imageData);
      console.log('Image data length:', imageData.length);
      console.log('Image data starts with:', imageData.substring(0, 30) + '...');

      // Check if the image data is a URI or base64
      if (!imageData.startsWith('data:image')) {
        console.log('Image data is a URI, not base64 data');
        // We'll still use it, but log this information
      }

      // Set the receipt image
      setReceiptImage(imageData);
      setReceiptUploaded(true);

      // Show success message
      Alert.alert(
        "Receipt Uploaded",
        "Your receipt has been uploaded successfully."
      );
    } catch (error) {
      console.error('Error uploading receipt:', error);

      // Get detailed error information
      if (error instanceof Error) {
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }

      Alert.alert(
        "Upload Failed",
        `Failed to upload receipt: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`
      );
    }
  }

  // Handle complete booking
  const handleCompleteBooking = useCallback(async () => {
    try {
      // Reset error state
      setPaymentError(null);

      // Validate payment method selection
      if (!selectedPaymentMethod) {
        Alert.alert("Payment Method Required", "Please select a payment method to continue.")
        return
      }

      // Validate receipt upload for Mobile Money
      if (selectedPaymentMethod === "mobile_money" && !receiptUploaded) {
        Alert.alert("Receipt Required", "Please upload your payment receipt to continue.")
        return
      }

      // Check if user is authenticated or just returned from login
      if (!isAuthenticated) {
        console.log('User not authenticated, saving booking data and showing auth modal');

        // Save booking data for after login with all necessary details
        const bookingData = {
          serviceId,
          serviceName: serviceTitle,
          serviceImage: null, // We don't have this in the route params
          price: subtotal, // Full subtotal for the service
          duration,
          date,
          time,
          address,
          frequency: "ONE_TIME", // Use proper enum value
          providerId,
          autoAssign: providerId === "auto_assign",
          // Include pricing information
          subtotal,
          fee,
          total,
          // Payment method selection
          paymentMethod: selectedPaymentMethod,
          receiptImage: selectedPaymentMethod === "mobile_money" ? receiptImage : null,
          // Add booking ID for reference
          bookingId,
          // Add timestamp for tracking
          lastUpdated: Date.now()
        };

        // Update booking context with the provided data
        setBookingData(bookingData);

        // Save to storage
        const saveResult = await saveBookingData();
        console.log('Booking data saved to storage:', saveResult);

        // Track this as an abandoned booking for analytics
        await trackAbandonedBooking();

        // Show auth modal using the enhanced AuthContext
        showAuthModal();
        return;
      }

      // If we just returned from login and haven't processed it yet, proceed with booking
      if (returnedFromLogin && !processedPostLogin) {
        console.log('User just returned from login, proceeding with booking completion');
        setProcessedPostLogin(true);
        // Continue with the booking process below
      }

      // Show loading state
      setIsSubmitting(true);

      // Create a toast notification to show progress
      ToastService.show({
        type: 'info',
        text1: 'Processing your booking',
        text2: 'Please wait while we confirm your booking...',
        visibilityTime: 3000,
        autoHide: true,
      });

      let paymentId = null;
      let createdBookingId = null;

      try {
        // Step 1: Process payment
        console.log('Attempting to process payment...');

        // Prepare payment data
        const paymentData = {
          bookingId,
          paymentMethod: selectedPaymentMethod,
          amount: total,
          receiptImage: selectedPaymentMethod === "mobile_money" ? receiptImage : undefined
        };

        // Process payment locally (demo mode)
        console.log('Processing payment locally:', paymentData);

        // Simulate payment processing delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Generate mock payment ID
        paymentId = `payment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        console.log('Payment processed successfully (demo):', paymentId);

        // Step 2: Create booking in database with all user details
        const bookingData = {
          serviceId,
          // Ensure we have a valid address ID
          addressId: address && address !== 'undefined' ? address : null,
          date,
          startTime: time,
          endTime: calculateEndTime(time, duration), // Calculate end time based on duration
          notes: "",
          providerId: providerId === "auto_assign" ? undefined : providerId,
          // Use proper enum value for frequency
          frequency: "ONE_TIME",
          // Format payment method for backend
          paymentMethod: selectedPaymentMethod,
          paymentProof: selectedPaymentMethod === "mobile_money" ? receiptImage : null,
          paymentId: paymentId, // Link to the payment
          // Include total amount which is required by the backend
          price: subtotal,
          total: total,
          // Add user details if available (these won't be used by backend but useful for logging)
          customerId: user?.id,
          customerName: user ? `${user.firstName} ${user.lastName}` : undefined,
          customerEmail: user?.email,
          customerPhone: user?.phone
        };

        console.log('Attempting to create booking with user details:', {
          ...bookingData,
          customerId: user?.id,
          customerEmail: user?.email
        });

        // Create booking with local booking service
        const { localBookingService } = await import('../services/localBookingService');

        const booking = await localBookingService.createBooking(
          bookingData,
          user?.id || 'guest',
          user ? `${user.firstName} ${user.lastName}` : 'Guest User',
          user?.phone || '+2207000000'
        );
        console.log('Booking created successfully:', booking.id);
        createdBookingId = booking.id;

        // Step 3: Mark any abandoned bookings as completed
        try {
          // Get all abandoned bookings
          const abandonedBookings = await getAbandonedBookings();

          // Find any abandoned bookings for this service
          const matchingBookings = abandonedBookings.filter(
            booking => booking.bookingData.serviceId === serviceId
          );

          console.log(`Found ${matchingBookings.length} abandoned bookings to mark as completed`);

          // Mark them as completed
          for (const booking of matchingBookings) {
            await markAbandonedBookingAsCompleted(booking.id);
            console.log(`Marked abandoned booking ${booking.id} as completed`);
          }
        } catch (analyticsError) {
          // Don't fail the booking if analytics tracking fails
          console.error('Error updating abandoned booking analytics:', analyticsError);
        }

        // Step 4: Clear any stored booking data
        await clearBookingData();
        console.log('Cleared stored booking data after successful booking');

        // Step 5: Show success message
        ToastService.show({
          type: 'success',
          text1: 'Booking Confirmed!',
          text2: 'Your booking has been successfully created.',
          visibilityTime: 3000,
          autoHide: true,
        });

        // Step 6: Navigate to success screen with a short delay for better UX
        setTimeout(() => {
          // Navigate to success screen with more details
          navigation.navigate("BookingSuccess", {
            bookingId: createdBookingId || bookingId,
            serviceTitle,
            date,
            time,
            providerName: user?.firstName ? `${user.firstName} ${user.lastName}` : 'Your provider',
            paymentMethod: selectedPaymentMethod,
            total,
            customerName: user ? `${user.firstName} ${user.lastName}` : 'Guest',
            customerEmail: user?.email,
            customerPhone: user?.phone,
            address: address
          });
        }, 1000);
      } catch (error: any) {
        console.error('Error in booking process:', error);
        throw error; // Re-throw to be caught by the outer catch block
      }
    } catch (error: any) {
      console.error('Error completing booking:', error);

      // Get detailed error information
      let errorMessage = 'Failed to complete booking. Please try again.';

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response:', {
          data: error.response.data,
          status: error.response.status,
          headers: error.response.headers,
        });
        errorMessage = `Server error: ${error.response.status}. ${error.response.data?.error || error.message || errorMessage}`;
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request);
        errorMessage = 'No response from server. Please check your internet connection.';
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', error.message);
        errorMessage = error.message || errorMessage;
      }

      // Show error toast
      ToastService.show({
        type: 'error',
        text1: 'Booking Failed',
        text2: errorMessage,
        visibilityTime: 4000,
        autoHide: true,
      });

      setPaymentError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }, [
    selectedPaymentMethod,
    receiptUploaded,
    receiptImage,
    bookingId,
    serviceId,
    serviceTitle,
    total,
    subtotal,
    address,
    date,
    time,
    duration,
    providerId,
    navigation,
    clearBookingData,
    getAbandonedBookings,
    markAbandonedBookingAsCompleted,
    isAuthenticated,
    setBookingData,
    saveBookingData,
    trackAbandonedBooking,
    user,
    returnedFromLogin,
    processedPostLogin,
    setProcessedPostLogin
  ])

  return (
    <View style={styles.container}>
      <Header title="Payment Options" showBackButton />

      {/* Auth Required Modal */}
      <AuthModal
        visible={isAuthModalVisible}
        onClose={hideAuthModal}
        returnTo="PaymentOption"
        returnParams={{
          bookingId,
          serviceId,
          serviceTitle,
          date,
          time,
          address,
          duration,
          subtotal,
          fee,
          total,
          providerId
        }}
        message="Please sign in or create an account to complete your booking"
      />

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Booking Summary */}
          <Card style={styles.summaryCard}>
            <Text style={styles.sectionTitle}>Booking Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Service:</Text>
              <Text style={styles.summaryValue}>{serviceTitle}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Date:</Text>
              <Text style={styles.summaryValue}>{date}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Time:</Text>
              <Text style={styles.summaryValue}>{time}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Duration:</Text>
              <Text style={styles.summaryValue}>{duration}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Address:</Text>
              <Text style={styles.summaryValue}>{address}</Text>
            </View>
            <View style={styles.summaryDivider} />
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal:</Text>
              <Text style={styles.summaryValue}>D{subtotal.toFixed(2)}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Service fee:</Text>
              <Text style={styles.summaryValue}>D{fee.toFixed(2)}</Text>
            </View>
            <View style={styles.summaryDivider} />
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total:</Text>
              <Text style={styles.totalValue}>D{total.toFixed(2)}</Text>
            </View>
          </Card>

          {/* Payment Methods */}
          <View style={styles.paymentMethodsContainer}>
            <Text style={styles.sectionTitle}>Select Payment Method</Text>

            {/* Mobile Money Option */}
            <TouchableOpacity
              style={[
                styles.paymentOption,
                selectedPaymentMethod === "mobile_money" && styles.paymentOptionSelected,
              ]}
              onPress={() => handleSelectPaymentMethod("mobile_money")}
            >
              <View style={styles.paymentOptionHeader}>
                <View style={styles.paymentIconContainer}>
                  <Feather name="smartphone" size={24} color={theme.colors.primary} />
                </View>
                <View style={styles.paymentInfo}>
                  <Text style={styles.paymentTitle}>Mobile Money</Text>
                  <Text style={styles.paymentDescription}>
                    Pay now via mobile money transfer
                  </Text>
                </View>
                {selectedPaymentMethod === "mobile_money" && (
                  <Feather name="check-circle" size={24} color={theme.colors.primary} />
                )}
              </View>

              {selectedPaymentMethod === "mobile_money" && (
                <View style={styles.paymentDetails}>
                  <View style={styles.instructionsContainer}>
                    <Text style={styles.instructionsTitle}>Payment Instructions:</Text>
                    <Text style={styles.instructionsText}>
                      1. Send D{total.toFixed(2)} to +220 7777 8888
                    </Text>
                    <Text style={styles.instructionsText}>
                      2. Use booking ID {bookingId} as reference
                    </Text>
                    <Text style={styles.instructionsText}>
                      3. Upload screenshot of payment receipt
                    </Text>
                  </View>

                  <TouchableOpacity
                    style={[
                      styles.uploadButton,
                      receiptUploaded && styles.uploadButtonSuccess,
                    ]}
                    onPress={handleUploadReceipt}
                  >
                    {receiptUploaded ? (
                      <>
                        <Feather name="check" size={20} color={theme.colors.card} />
                        <Text style={styles.uploadButtonTextSuccess}>Receipt Uploaded</Text>
                      </>
                    ) : (
                      <>
                        <Feather name="upload" size={20} color={theme.colors.primary} />
                        <Text style={styles.uploadButtonText}>Upload Receipt</Text>
                      </>
                    )}
                  </TouchableOpacity>

                  {receiptUploaded && receiptImage && (
                    <View style={styles.receiptPreviewContainer}>
                      <Image
                        source={{ uri: receiptImage }}
                        style={styles.receiptPreview}
                        resizeMode="contain"
                        onError={(error) => {
                          console.error('Image loading error:', error.nativeEvent.error);
                          Alert.alert(
                            "Image Error",
                            "There was an error loading the image. Please try uploading again."
                          );
                        }}
                        onLoad={() => console.log('Image loaded successfully')}
                      />
                      <TouchableOpacity
                        style={styles.changeReceiptButton}
                        onPress={handleUploadReceipt}
                      >
                        <Text style={styles.changeReceiptText}>Change</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              )}
            </TouchableOpacity>

            {/* Pay on Work Done Option */}
            <TouchableOpacity
              style={[
                styles.paymentOption,
                selectedPaymentMethod === "pay_later" && styles.paymentOptionSelected,
              ]}
              onPress={() => handleSelectPaymentMethod("pay_later")}
            >
              <View style={styles.paymentOptionHeader}>
                <View style={styles.paymentIconContainer}>
                  <Feather name="clock" size={24} color={theme.colors.primary} />
                </View>
                <View style={styles.paymentInfo}>
                  <Text style={styles.paymentTitle}>Pay on Work Done</Text>
                  <Text style={styles.paymentDescription}>
                    Pay after the service is completed
                  </Text>
                </View>
                {selectedPaymentMethod === "pay_later" && (
                  <Feather name="check-circle" size={24} color={theme.colors.primary} />
                )}
              </View>

              {selectedPaymentMethod === "pay_later" && (
                <View style={styles.paymentDetails}>
                  <View style={styles.infoContainer}>
                    <Feather name="info" size={20} color={theme.colors.warning} />
                    <Text style={styles.infoText}>
                      You'll be responsible for paying the cleaner directly after the service is
                      completed. Please have the exact amount ready.
                    </Text>
                  </View>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action */}
      <View style={styles.bottomContainer}>
        {paymentError && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{paymentError}</Text>
          </View>
        )}
        <Button
          title={isSubmitting ? "Processing..." : "Complete Booking"}
          variant="warning"
          fullWidth
          disabled={
            isSubmitting ||
            !selectedPaymentMethod ||
            (selectedPaymentMethod === "mobile_money" && !receiptUploaded)
          }
          onPress={handleCompleteBooking}
        />
      </View>
    </View>
  )
}

// Create styles using the theme
const getStyles = (theme) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: theme.spacing.md,
  },
  summaryCard: {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  sectionTitle: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.lg,
    fontWeight: "600",
    marginBottom: theme.spacing.md,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  summaryLabel: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
  },
  summaryValue: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "500",
  },
  summaryDivider: {
    backgroundColor: theme.colors.border,
    height: 1,
    marginVertical: theme.spacing.sm,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 4,
  },
  totalLabel: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
  },
  totalValue: {
    color: theme.colors.primary,
    fontSize: theme.fontSizes.md,
    fontWeight: "700",
  },
  paymentMethodsContainer: {
    marginBottom: theme.spacing.md,
  },
  paymentOption: {
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 2,
    marginBottom: theme.spacing.sm,
    padding: theme.spacing.md,
  },
  paymentOptionSelected: {
    backgroundColor: `${theme.colors.primary}10`,
    borderColor: theme.colors.primary,
  },
  paymentOptionHeader: {
    alignItems: "center",
    flexDirection: "row",
  },
  paymentIconContainer: {
    alignItems: "center",
    backgroundColor: `${theme.colors.primary}10`,
    borderRadius: 24,
    height: 48,
    justifyContent: "center",
    marginRight: theme.spacing.sm,
    width: 48,
  },
  paymentInfo: {
    flex: 1,
  },
  paymentTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    marginBottom: 4,
  },
  paymentDescription: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
  },
  paymentDetails: {
    borderTopColor: theme.colors.border,
    borderTopWidth: 1,
    marginTop: theme.spacing.md,
    paddingTop: theme.spacing.md,
  },
  instructionsContainer: {
    marginBottom: theme.spacing.md,
  },
  instructionsTitle: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    marginBottom: theme.spacing.sm,
  },
  instructionsText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
    marginBottom: 4,
  },
  uploadButton: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.primary,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "center",
    padding: theme.spacing.sm,
  },
  uploadButtonSuccess: {
    backgroundColor: theme.colors.secondary,
    borderColor: theme.colors.secondary,
  },
  uploadButtonText: {
    color: theme.colors.primary,
    fontSize: theme.fontSizes.sm,
    fontWeight: "500",
    marginLeft: theme.spacing.sm,
  },
  uploadButtonTextSuccess: {
    color: theme.colors.card,
    fontSize: theme.fontSizes.sm,
    fontWeight: "500",
    marginLeft: theme.spacing.sm,
  },
  receiptPreviewContainer: {
    marginTop: theme.spacing.sm,
    alignItems: "center",
    borderRadius: theme.borderRadius.sm,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  receiptPreview: {
    width: "100%",
    height: 150,
    backgroundColor: theme.colors.card,
  },
  changeReceiptButton: {
    position: "absolute",
    bottom: 8,
    right: 8,
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  changeReceiptText: {
    color: "white",
    fontSize: 12,
    fontWeight: "500",
  },
  errorContainer: {
    backgroundColor: `${theme.colors.error}20`,
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    marginBottom: theme.spacing.sm,
  },
  errorText: {
    color: theme.colors.error,
    fontSize: theme.fontSizes.sm,
  },
  infoContainer: {
    backgroundColor: `${theme.colors.warning}10`,
    borderRadius: theme.borderRadius.sm,
    flexDirection: "row",
    padding: theme.spacing.sm,
  },
  infoText: {
    color: theme.colors.textLight,
    flex: 1,
    fontSize: theme.fontSizes.sm,
    marginLeft: theme.spacing.sm,
  },
  bottomContainer: {
    backgroundColor: theme.colors.card,
    borderTopColor: theme.colors.border,
    borderTopWidth: 1,
    padding: theme.spacing.md,
  },
})

export default PaymentOptionScreen
