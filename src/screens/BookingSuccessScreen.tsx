"use client"

import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { useNavigation, useRoute, type RouteProp } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Button from "../components/common/Button"

type BookingSuccessScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>
type BookingSuccessScreenRouteProp = RouteProp<RootStackParamList, "BookingSuccess">

const BookingSuccessScreen = () => {
  const navigation = useNavigation<BookingSuccessScreenNavigationProp>()
  const route = useRoute<BookingSuccessScreenRouteProp>()
  const theme = useTheme()

  // Get styles with the current theme
  const styles = getStyles(theme)

  // Extract all booking details from route params
  const {
    bookingId,
    serviceTitle,
    date,
    time,
    providerName,
    paymentMethod,
    total,
    customerName,
    customerEmail,
    customerPhone,
    address
  } = route.params

  const handleViewBookings = () => {
    navigation.navigate("BookingHistory")
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.successIconContainer}>
          <Feather name="check-circle" size={80} color={theme.colors.secondary} />
        </View>

        <Text style={styles.title}>Booking Confirmed!</Text>

        <Text style={styles.message}>
          Your booking has been successfully created.
        </Text>

        <View style={styles.bookingIdContainer}>
          <Text style={styles.bookingIdLabel}>Booking Reference:</Text>
          <Text style={styles.bookingId}>{bookingId}</Text>
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Feather name="calendar" size={18} color={theme.colors.primary} style={styles.detailIcon} />
            <View style={styles.detailTextContainer}>
              <Text style={styles.detailLabel}>Service</Text>
              <Text style={styles.detailValue}>{serviceTitle}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Feather name="clock" size={18} color={theme.colors.primary} style={styles.detailIcon} />
            <View style={styles.detailTextContainer}>
              <Text style={styles.detailLabel}>Date & Time</Text>
              <Text style={styles.detailValue}>{date}, {time}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Feather name="map-pin" size={18} color={theme.colors.primary} style={styles.detailIcon} />
            <View style={styles.detailTextContainer}>
              <Text style={styles.detailLabel}>Location</Text>
              <Text style={styles.detailValue}>{address}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Feather name="user" size={18} color={theme.colors.primary} style={styles.detailIcon} />
            <View style={styles.detailTextContainer}>
              <Text style={styles.detailLabel}>Provider</Text>
              <Text style={styles.detailValue}>{providerName}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Feather name="credit-card" size={18} color={theme.colors.primary} style={styles.detailIcon} />
            <View style={styles.detailTextContainer}>
              <Text style={styles.detailLabel}>Payment Method</Text>
              <Text style={styles.detailValue}>{paymentMethod === 'mobile_money' ? 'Mobile Money' : 'Cash on Delivery'}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Feather name="dollar-sign" size={18} color={theme.colors.primary} style={styles.detailIcon} />
            <View style={styles.detailTextContainer}>
              <Text style={styles.detailLabel}>Total Amount</Text>
              <Text style={styles.detailValue}>D{total.toFixed(2)}</Text>
            </View>
          </View>
        </View>

        <Text style={styles.infoText}>
          We've sent a confirmation to {customerEmail || 'your email'} with all the details of your booking.
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title="View My Bookings"
          variant="primary"
          fullWidth
          onPress={handleViewBookings}
        />

        <Button
          title="Return to Home"
          variant="outline"
          fullWidth
          style={styles.homeButton}
          onPress={() => navigation.navigate("Main")}
        />
      </View>
    </View>
  )
}

// Create styles using the theme
const getStyles = (theme) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.card,
    flex: 1,
    justifyContent: "space-between",
    padding: theme.spacing.lg,
  },
  content: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingVertical: theme.spacing.lg,
  },
  successIconContainer: {
    alignItems: "center",
    backgroundColor: `${theme.colors.secondary}10`,
    borderRadius: 60,
    height: 120,
    justifyContent: "center",
    marginBottom: theme.spacing.lg,
    width: 120,
  },
  title: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    marginBottom: theme.spacing.md,
    textAlign: "center",
  },
  message: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.md,
    marginBottom: theme.spacing.lg,
    textAlign: "center",
  },
  bookingIdContainer: {
    alignItems: "center",
    backgroundColor: `${theme.colors.primary}10`,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.md,
    width: "100%",
  },
  bookingIdLabel: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
    marginBottom: 4,
  },
  bookingId: {
    color: theme.colors.primary,
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
  },
  detailsContainer: {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.md,
    width: "100%",
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  detailIcon: {
    marginRight: 12,
  },
  detailTextContainer: {
    flex: 1,
  },
  detailLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.textLight,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.text,
    fontWeight: "500",
  },
  infoText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
    textAlign: "center",
    marginTop: theme.spacing.md,
  },
  buttonContainer: {
    marginTop: theme.spacing.lg,
    width: "100%",
  },
  homeButton: {
    marginTop: theme.spacing.sm,
  },
})

export default BookingSuccessScreen
