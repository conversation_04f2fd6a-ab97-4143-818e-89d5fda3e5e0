import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { RootStackParamList } from '../navigation/RootNavigator';
import Button from '../components/Button';
import Header from '../components/Header';
import { localAuthService } from '../services/localAuthService';

type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

enum ForgotPasswordStep {
  EMAIL_ENTRY,
  OTP_VERIFICATION,
  NEW_PASSWORD,
}

const ForgotPasswordScreen: React.FC = () => {
  const navigation = useNavigation<ForgotPasswordScreenNavigationProp>();
  const theme = useTheme();
  const { forgotPassword, resetPassword } = useAuth();

  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<ForgotPasswordStep>(ForgotPasswordStep.EMAIL_ENTRY);
  const [passwordError, setPasswordError] = useState('');

  // Password validation regex
  const passwordRegex = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*])/;

  const validatePassword = (password: string): boolean => {
    if (password.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return false;
    }
    if (!passwordRegex.test(password)) {
      setPasswordError('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
      return false;
    }
    setPasswordError('');
    return true;
  };

  const handleRequestOTP = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    try {
      setIsLoading(true);

      // Call local auth service to send password reset OTP
      const response = await localAuthService.sendOtp(email);

      if (response.success) {
        setCurrentStep(ForgotPasswordStep.OTP_VERIFICATION);
        Alert.alert(
          'Verification Code Sent',
          'A verification code has been sent to your email address. Please check your inbox for a 4-digit code.'
        );
      } else {
        Alert.alert('Error', response.error || 'Failed to send verification code. Please try again.');
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to send verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (!otp) {
      Alert.alert('Error', 'Please enter the verification code');
      return;
    }

    try {
      setIsLoading(true);

      // Call local auth service to verify the OTP
      const response = await localAuthService.verifyOtp(email, otp);

      if (response.success) {
        // Store the verified OTP for password reset
        await SecureStore.setItemAsync('otp_code', otp);
        // Move to new password step after successful OTP verification
        setCurrentStep(ForgotPasswordStep.NEW_PASSWORD);
      } else {
        Alert.alert(
          'Verification Failed',
          response.error || 'Invalid or expired verification code. Please request a new code.',
          [
            {
              text: 'Request New Code',
              onPress: handleRequestOTP,
              style: 'default'
            },
            {
              text: 'Cancel',
              style: 'cancel'
            }
          ]
        );
      }
    } catch (error: any) {
      console.error('Verify OTP error:', error);
      Alert.alert(
        'Verification Failed',
        'Failed to verify code. Please try again or request a new code.',
        [
          {
            text: 'Request New Code',
            onPress: handleRequestOTP,
            style: 'default'
          },
          {
            text: 'Cancel',
            style: 'cancel'
          }
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async () => {
    if (!newPassword || !confirmPassword) {
      Alert.alert('Error', 'Please enter and confirm your new password');
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    if (!validatePassword(newPassword)) {
      return;
    }

    try {
      setIsLoading(true);

      // Get the stored OTP
      const storedOtp = await SecureStore.getItemAsync('otp_code');
      if (!storedOtp) {
        Alert.alert('Error', 'Verification code expired. Please request a new code.');
        setCurrentStep(ForgotPasswordStep.EMAIL_ENTRY);
        return;
      }

      // In demo mode, always return success for password reset
      const response = { success: true, message: 'Password reset successful in demo mode' };

      if (response.success) {
        // Clear OTP data after successful reset
        await SecureStore.deleteItemAsync('otp_code');
        await SecureStore.deleteItemAsync('otp_email');
        await SecureStore.deleteItemAsync('otp_purpose');
        await SecureStore.deleteItemAsync('otp_expiry');

        Alert.alert(
          'Success',
          'Your password has been reset successfully. You can now log in with your new password.',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('Login' as never),
            },
          ]
        );
      } else {
        Alert.alert('Error', response.error || 'Failed to reset password. Please try again.');
      }
    } catch (error: any) {
      console.error('Reset password error:', error);
      Alert.alert('Error', 'Failed to reset password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // State for OTP input
  const [otpDigits, setOtpDigits] = useState(['', '', '', '']);
  const otpInputRefs = useRef<Array<TextInput | null>>([null, null, null, null]);
  const [canResendOtp, setCanResendOtp] = useState(false);
  const [otpTimeLeft, setOtpTimeLeft] = useState(60);

  // Handle OTP input change
  const handleOtpDigitChange = (text: string, index: number) => {
    if (text.length > 1) {
      text = text[0]; // Only take the first character
    }

    const newOtpDigits = [...otpDigits];
    newOtpDigits[index] = text;
    setOtpDigits(newOtpDigits);

    // Update the main OTP state
    setOtp(newOtpDigits.join(''));

    // Auto-focus next input
    if (text && index < 3) {
      otpInputRefs.current[index + 1]?.focus();
    }
  };

  // Handle OTP resend
  const handleResendOtp = async () => {
    if (!canResendOtp) return;

    try {
      setIsLoading(true);

      // Reset OTP input fields
      setOtpDigits(['', '', '', '']);
      setOtp('');

      // Call local auth service to resend OTP
      const response = await localAuthService.sendOtp(email);

      if (response.success) {
        // Reset timer
        setOtpTimeLeft(60);
        setCanResendOtp(false);

        Alert.alert(
          'Verification Code Sent',
          'A new verification code has been sent to your email.'
        );
      } else {
        Alert.alert('Resend Failed', response.error || 'Failed to resend verification code. Please try again.');
      }
    } catch (error: any) {
      Alert.alert('Resend Failed', error.message || 'Failed to resend verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Timer for OTP resend
  React.useEffect(() => {
    if (currentStep !== ForgotPasswordStep.OTP_VERIFICATION) return;

    if (otpTimeLeft > 0) {
      const timerId = setTimeout(() => {
        setOtpTimeLeft(otpTimeLeft - 1);
      }, 1000);
      return () => clearTimeout(timerId);
    } else {
      setCanResendOtp(true);
    }
  }, [otpTimeLeft, currentStep]);

  const renderEmailStep = () => (
    <View>
      <Text style={styles.stepTitle}>Reset Password</Text>
      <Text style={styles.stepDescription}>
        Enter your email address and we'll send you a verification code to reset your password.
      </Text>
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Email</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your email"
          keyboardType="email-address"
          autoCapitalize="none"
          value={email}
          onChangeText={setEmail}
        />
      </View>
      <Button
        title={isLoading ? 'Sending...' : 'Send Verification Code'}
        onPress={handleRequestOTP}
        variant="primary"
        fullWidth
        loading={isLoading}
      />
      <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
        <Text style={styles.backButtonText}>Back to Login</Text>
      </TouchableOpacity>
    </View>
  );

  const renderOTPStep = () => (
    <View>
      <Text style={styles.stepTitle}>Enter Verification Code</Text>
      <Text style={styles.stepDescription}>
        Please enter the 4-digit verification code sent to {email}
      </Text>

      <View style={styles.otpContainer}>
        {otpDigits.map((digit, index) => (
          <TextInput
            key={index}
            ref={(ref) => {
              if (ref) {
                otpInputRefs.current[index] = ref;
              }
            }}
            style={[
              styles.otpInput,
              digit ? { borderColor: theme.colors.primary } : {}
            ]}
            value={digit}
            onChangeText={text => handleOtpDigitChange(text, index)}
            keyboardType="number-pad"
            maxLength={1}
            onKeyPress={({ nativeEvent }) => {
              if (nativeEvent.key === 'Backspace' && !digit && index > 0) {
                otpInputRefs.current[index - 1]?.focus();
              }
            }}
          />
        ))}
      </View>

      <View style={styles.resendContainer}>
        <Text style={styles.resendText}>
          {canResendOtp
            ? "Didn't receive the code?"
            : `Resend code in ${otpTimeLeft}s`}
        </Text>
        {canResendOtp && (
          <TouchableOpacity onPress={handleResendOtp} disabled={isLoading}>
            <Text style={styles.resendButton}>Resend</Text>
          </TouchableOpacity>
        )}
      </View>

      <Button
        title={isLoading ? "Verifying..." : "Verify Code"}
        onPress={handleVerifyOTP}
        variant="primary"
        fullWidth
        loading={isLoading}
        disabled={isLoading || otp.length !== 4}
      />

      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setCurrentStep(ForgotPasswordStep.EMAIL_ENTRY)}
      >
        <Text style={styles.backButtonText}>Back</Text>
      </TouchableOpacity>
    </View>
  );

  const renderNewPasswordStep = () => (
    <View>
      <Text style={styles.stepTitle}>Create New Password</Text>
      <Text style={styles.stepDescription}>
        Please enter your new password below
      </Text>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>New Password</Text>
        <TextInput
          style={[styles.input, passwordError ? styles.inputError : null]}
          placeholder="Enter new password"
          secureTextEntry
          value={newPassword}
          onChangeText={(text) => {
            setNewPassword(text);
            validatePassword(text);
          }}
        />
        {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
        <Text style={styles.passwordRequirements}>
          Password must:
          {'\n'}- Be at least 8 characters long
          {'\n'}- Contain at least one uppercase letter
          {'\n'}- Contain at least one lowercase letter
          {'\n'}- Contain at least one number
          {'\n'}- Contain at least one special character (!@#$%^&*)
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Confirm Password</Text>
        <TextInput
          style={[styles.input, confirmPassword && newPassword !== confirmPassword ? styles.inputError : null]}
          placeholder="Confirm new password"
          secureTextEntry
          value={confirmPassword}
          onChangeText={setConfirmPassword}
        />
        {confirmPassword && newPassword !== confirmPassword ? (
          <Text style={styles.errorText}>Passwords do not match</Text>
        ) : null}
      </View>

      <Button
        title={isLoading ? "Resetting..." : "Reset Password"}
        onPress={handleResetPassword}
        variant="primary"
        fullWidth
        loading={isLoading}
        disabled={isLoading || !newPassword || !confirmPassword || !!passwordError || newPassword !== confirmPassword}
      />

      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setCurrentStep(ForgotPasswordStep.OTP_VERIFICATION)}
      >
        <Text style={styles.backButtonText}>Back</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case ForgotPasswordStep.EMAIL_ENTRY:
        return renderEmailStep();
      case ForgotPasswordStep.OTP_VERIFICATION:
        return renderOTPStep();
      case ForgotPasswordStep.NEW_PASSWORD:
        return renderNewPasswordStep();
      default:
        return renderEmailStep();
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.lg,
    },
    stepTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    stepDescription: {
      fontSize: theme.fontSizes.md,
      color: theme.colors.textLight,
      marginBottom: theme.spacing.lg,
    },
    inputContainer: {
      marginBottom: theme.spacing.md,
    },
    label: {
      fontSize: theme.fontSizes.sm,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    input: {
      backgroundColor: theme.colors.card,
      borderColor: theme.colors.border,
      borderWidth: 1,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.sm,
      fontSize: theme.fontSizes.md,
    },
    inputError: {
      borderColor: theme.colors.notification,
    },
    errorText: {
      color: theme.colors.notification,
      fontSize: theme.fontSizes.sm,
      marginTop: theme.spacing.xs,
    },
    passwordRequirements: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginTop: theme.spacing.sm,
      lineHeight: 20,
    },
    backButton: {
      marginTop: theme.spacing.md,
      alignItems: 'center',
    },
    backButtonText: {
      color: theme.colors.primary,
      fontSize: theme.fontSizes.md,
      fontWeight: '500',
    },
    otpContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.lg,
      paddingHorizontal: theme.spacing.md,
    },
    otpInput: {
      width: 60,
      height: 60,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.sm,
      textAlign: 'center',
      fontSize: 24,
      fontWeight: '600',
      backgroundColor: theme.colors.card,
      color: theme.colors.text,
    },
    resendContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing.lg,
    },
    resendText: {
      color: theme.colors.textLight,
      fontSize: theme.fontSizes.sm,
      marginRight: theme.spacing.xs,
    },
    resendButton: {
      color: theme.colors.primary,
      fontSize: theme.fontSizes.sm,
      fontWeight: '600',
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <Header title="Forgot Password" showBackButton />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default ForgotPasswordScreen;
