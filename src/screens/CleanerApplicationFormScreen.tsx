"use client"

import React, { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
} from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Button from "../components/common/Button"
// We'll use a simpler approach without external dependencies

type CleanerApplicationFormScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

const CleanerApplicationFormScreen = () => {
  const navigation = useNavigation<CleanerApplicationFormScreenNavigationProp>()
  const theme = useTheme()

  // Form step tracking
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)

  // Step 1: Personal & Contact Details
  const [fullName, setFullName] = useState("")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [whatsappNumber, setWhatsappNumber] = useState("")
  const [gender, setGender] = useState("")
  const [dateOfBirth, setDateOfBirth] = useState("")
  const [address, setAddress] = useState("")
  const [plusCode, setPlusCode] = useState("")
  const [nationalIdNumber, setNationalIdNumber] = useState("")
  const [nationalIdUploaded, setNationalIdUploaded] = useState(false)
  const [email, setEmail] = useState("")

  // Step 2: Services & Work Details
  const [services, setServices] = useState<string[]>([])
  const [hasTools, setHasTools] = useState(false)
  const [ownedTools, setOwnedTools] = useState<string[]>([])
  const [otherTools, setOtherTools] = useState("")
  const [experience, setExperience] = useState("")
  const [employmentStatus, setEmploymentStatus] = useState("")

  // Step 3: Verification & Motivation
  const [policeClearanceUploaded, setPoliceClearanceUploaded] = useState(false)
  const [referenceLetterUploaded, setReferenceLetterUploaded] = useState(false)
  const [hasPoliceCases, setHasPoliceCases] = useState(false)
  const [policeCasesExplanation, setPoliceCasesExplanation] = useState("")
  const [joinReason, setJoinReason] = useState("")
  const [confirmInfoCorrect, setConfirmInfoCorrect] = useState(false)
  const [signature, setSignature] = useState("")

  // Get styles with the current theme
  const styles = getStyles(theme)

  // Constants for form options
  const genderOptions = ["Male", "Female"]

  const experienceOptions = [
    "Less than 1 year",
    "1-2 years",
    "3-5 years",

  ]

  const serviceOptions = [
    "Home Cleaning",
    "Laundry",
    "Ironing",
    "Dishwashing",
    "Deep Cleaning",
    "Outdoor sweeping"
  ]

  const toolOptions = [
    "Mop",
    "Bucket",
    "Hard brush",
    "Soft brush",
    "Gloves",
    "Cleaning cloths",
    "Squeegee",
    "Surface cleaner",
    "Disinfectant or Detergent"
  ]

  const employmentStatusOptions = [
    "Unemployed",
    "Part-time employed",
    "Full-time employed",
    "Self-employed",
    "Student"
  ]



  // Handle manual date input validation
  const handleDateInput = (text: string) => {
    // Allow only digits and hyphens
    const sanitizedText = text.replace(/[^0-9-]/g, '')

    // Basic YYYY-MM-DD format validation
    if (sanitizedText.length <= 10) {
      setDateOfBirth(sanitizedText)
    }
  }

  // Document upload functions
  const pickNationalId = () => {
    // Simulate image selection
    Alert.alert(
      "Upload National ID",
      "In a real app, this would open your device's file picker.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Select Document",
          onPress: () => {
            // Simulate successful document selection
            setNationalIdUploaded(true)
            Alert.alert("Success", "National ID document uploaded successfully.")
          }
        }
      ]
    )
  }

  const pickPoliceClearance = () => {
    // Simulate document selection
    Alert.alert(
      "Upload Police Clearance",
      "In a real app, this would open your device's file picker.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Select Document",
          onPress: () => {
            // Simulate successful document selection
            setPoliceClearanceUploaded(true)
            Alert.alert("Success", "Police clearance document uploaded successfully.")
          }
        }
      ]
    )
  }

  const pickReferenceLetter = () => {
    // Simulate document selection
    Alert.alert(
      "Upload Reference Letter",
      "In a real app, this would open your device's file picker.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Select Document",
          onPress: () => {
            // Simulate successful document selection
            setReferenceLetterUploaded(true)
            Alert.alert("Success", "Reference letter uploaded successfully.")
          }
        }
      ]
    )
  }

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < 3) {
      if (validateCurrentStep()) {
        setCurrentStep(currentStep + 1)
        // Scroll to top when changing steps (only in web environment)
        if (typeof window !== 'undefined' && window.scrollTo) {
          window.scrollTo(0, 0)
        }
      }
    } else {
      handleSubmit()
    }
  }

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
      // Scroll to top when changing steps (only in web environment)
      if (typeof window !== 'undefined' && window.scrollTo) {
        window.scrollTo(0, 0)
      }
    }
  }

  // Validation functions
  const validateCurrentStep = () => {
    switch (currentStep) {
      case 1:
        // Validate Personal & Contact Details
        if (!fullName || !phoneNumber || !gender || !dateOfBirth || !address || !nationalIdNumber || !nationalIdUploaded) {
          Alert.alert("Missing Information", "Please fill in all required fields and upload your National ID.")
          return false
        }

        // Phone validation (simple check)
        if (phoneNumber.length < 7) {
          Alert.alert("Invalid Phone Number", "Please enter a valid phone number.")
          return false
        }

        // If WhatsApp number is provided, validate it
        if (whatsappNumber && whatsappNumber.length < 7) {
          Alert.alert("Invalid WhatsApp Number", "Please enter a valid WhatsApp number.")
          return false
        }

        return true

      case 2:
        // Validate Services & Work Details
        if (services.length === 0 || !experience || !employmentStatus) {
          Alert.alert("Missing Information", "Please select at least one service, your experience, and employment status.")
          return false
        }

        // If has tools is true, validate owned tools
        if (hasTools && ownedTools.length === 0 && !otherTools) {
          Alert.alert("Missing Information", "Please select at least one tool that you own.")
          return false
        }

        return true

      case 3:
        // Validate Verification & Motivation
        if (!policeClearanceUploaded || !joinReason || !confirmInfoCorrect || !signature) {
          Alert.alert("Missing Information", "Please upload your police clearance, provide a reason for joining, confirm the information is correct, and sign the form.")
          return false
        }

        // If has police cases is true, validate explanation
        if (hasPoliceCases && !policeCasesExplanation) {
          Alert.alert("Missing Information", "Please provide an explanation for your police cases.")
          return false
        }

        return true

      default:
        return true
    }
  }

  const handleSubmit = () => {
    // Final validation
    if (!validateCurrentStep()) {
      return
    }

    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      // Log form data (for testing)
      console.log({
        // Step 1: Personal & Contact Details
        fullName,
        phoneNumber,
        whatsappNumber,
        gender,
        dateOfBirth,
        address,
        plusCode,
        nationalIdNumber,
        nationalIdUploaded,
        email,

        // Step 2: Services & Work Details
        services,
        hasTools,
        ownedTools,
        otherTools,
        experience,
        employmentStatus,

        // Step 3: Verification & Motivation
        policeClearanceUploaded,
        referenceLetterUploaded,
        hasPoliceCases,
        policeCasesExplanation,
        joinReason,
        confirmInfoCorrect,
        signature
      })

      setIsSubmitting(false)
      setShowSuccess(true)
    }, 1500)
  }

  // Show success message after submission
  if (showSuccess) {
    return (
      <View style={styles.successContainer}>
        <View style={styles.successIconContainer}>
          <Feather name="check-circle" size={80} color={theme.colors.primary} />
        </View>
        <Text style={styles.successTitle}>Application Submitted!</Text>
        <Text style={styles.successMessage}>
          Your application has been submitted. Our team will review and get in touch if approved. Thank you for your interest in CleanConnect.
        </Text>
        <Button
          title="Return to Role Selection"
          variant="warning"
          onPress={() => navigation.navigate("RoleSelection" as never)}
          style={styles.successButton}
        />
      </View>
    )
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <Text style={styles.title}>Apply as a Cleaner</Text>
      <Text style={styles.subtitle}>
        Complete this form to join CleanConnect as a service provider
      </Text>

      {/* Progress indicator */}
      <View style={styles.progressContainer}>
        {[1, 2, 3].map((step) => (
          <View key={step} style={styles.progressStepWrapper}>
            <View
              style={[
                styles.progressStep,
                currentStep >= step && styles.progressStepActive,
              ]}
            >
              <Text
                style={[
                  styles.progressStepText,
                  currentStep >= step && styles.progressStepTextActive,
                ]}
              >
                {step}
              </Text>
            </View>
            <Text style={styles.progressStepLabel}>
              {step === 1 ? "Personal Details" : step === 2 ? "Work Details" : "Security"}
            </Text>
          </View>
        ))}
        <View style={styles.progressLine} />
      </View>

      <View style={styles.form}>
        {/* Step 1: Personal & Contact Details */}
        {currentStep === 1 && (
          <>
            {/* Full Name */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Full Name <Text style={styles.required}>*</Text></Text>
              <View style={styles.inputWrapper}>
                <Feather name="user" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your full name"
                  placeholderTextColor={theme.colors.textLight}
                  value={fullName}
                  onChangeText={setFullName}
                />
              </View>
            </View>

            {/* Phone Number */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Phone Number <Text style={styles.required}>*</Text></Text>
              <View style={styles.inputWrapper}>
                <Feather name="phone" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your phone number"
                  placeholderTextColor={theme.colors.textLight}
                  value={phoneNumber}
                  onChangeText={setPhoneNumber}
                  keyboardType="phone-pad"
                />
              </View>
            </View>

            {/* WhatsApp Number */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>WhatsApp Number (optional)</Text>
              <View style={styles.inputWrapper}>
                <Feather name="smartphone" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your WhatsApp number"
                  placeholderTextColor={theme.colors.textLight}
                  value={whatsappNumber}
                  onChangeText={setWhatsappNumber}
                  keyboardType="phone-pad"
                />
              </View>
            </View>

            {/* Gender */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Gender <Text style={styles.required}>*</Text></Text>
              <View style={styles.radioGroup}>
                {genderOptions.map((option) => (
                  <TouchableOpacity
                    key={option}
                    style={styles.radioOption}
                    onPress={() => setGender(option)}
                  >
                    <View style={styles.radioButtonContainer}>
                      <View
                        style={[
                          styles.radioButton,
                          gender === option && styles.radioButtonSelected
                        ]}
                      >
                        {gender === option && (
                          <View style={styles.radioButtonInner} />
                        )}
                      </View>
                    </View>
                    <Text style={styles.radioLabel}>{option}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Date of Birth */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Date of Birth <Text style={styles.required}>*</Text></Text>
              <View style={styles.inputWrapper}>
                <Feather name="calendar" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="YYYY-MM-DD"
                  placeholderTextColor={theme.colors.textLight}
                  value={dateOfBirth}
                  onChangeText={handleDateInput}
                />
              </View>
              <Text style={styles.helperText}>Enter date in YYYY-MM-DD format</Text>


            </View>

            {/* Residential Address */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Residential Address <Text style={styles.required}>*</Text></Text>
              <View style={styles.inputWrapper}>
                <Feather name="home" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your residential address"
                  placeholderTextColor={theme.colors.textLight}
                  value={address}
                  onChangeText={setAddress}
                />
              </View>
            </View>

            {/* Google Plus Code */}
            <View style={styles.inputContainer}>
              <View style={styles.labelWithTooltip}>
                <Text style={styles.label}>Google Plus Code</Text>
                <TouchableOpacity
                  onPress={() => Alert.alert(
                    "Google Plus Code",
                    "A Plus Code is a short code for your address that can be used to find locations where street addresses don't exist. Find your Plus Code on Google Maps."
                  )}
                >
                  <Feather name="help-circle" size={16} color={theme.colors.textLight} />
                </TouchableOpacity>
              </View>
              <View style={styles.inputWrapper}>
                <Feather name="map" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your Google Plus Code"
                  placeholderTextColor={theme.colors.textLight}
                  value={plusCode}
                  onChangeText={setPlusCode}
                />
              </View>
              <Text style={styles.helperText}>Example: 8FVC+JF Banjul, The Gambia</Text>
            </View>

            {/* National ID Number */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>National ID Number <Text style={styles.required}>*</Text></Text>
              <View style={styles.inputWrapper}>
                <Feather name="credit-card" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your National ID Number"
                  placeholderTextColor={theme.colors.textLight}
                  value={nationalIdNumber}
                  onChangeText={setNationalIdNumber}
                />
              </View>
            </View>

            {/* Upload National ID */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Upload National ID <Text style={styles.required}>*</Text></Text>
              <TouchableOpacity style={styles.uploadContainer} onPress={pickNationalId}>
                {nationalIdUploaded ? (
                  <View style={styles.uploadedImageContainer}>
                    <Feather name="check-circle" size={24} color={theme.colors.success} />
                    <Text style={styles.uploadedText}>National ID Uploaded</Text>
                  </View>
                ) : (
                  <>
                    <Feather name="upload" size={24} color={theme.colors.textLight} />
                    <Text style={styles.uploadText}>Tap to upload</Text>
                    <Text style={styles.uploadSubtext}>(PDF or Image)</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            {/* Email (optional) */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Email (optional but preferred)</Text>
              <View style={styles.inputWrapper}>
                <Feather name="mail" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your email address"
                  placeholderTextColor={theme.colors.textLight}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
            </View>
          </>
        )}

        {/* Step 2: Services & Work Details */}
        {currentStep === 2 && (
          <>
            {/* Services Offered */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>What services do you offer? <Text style={styles.required}>*</Text></Text>
              <Text style={styles.helperText}>Select all that apply</Text>
              <View style={styles.servicesGrid}>
                {serviceOptions.map((service) => (
                  <TouchableOpacity
                    key={service}
                    style={[
                      styles.serviceOption,
                      services.includes(service) && styles.serviceOptionSelected
                    ]}
                    onPress={() => {
                      if (services.includes(service)) {
                        setServices(services.filter(s => s !== service))
                      } else {
                        setServices([...services, service])
                      }
                    }}
                  >
                    <View style={styles.serviceCheckbox}>
                      {services.includes(service) && (
                        <Feather name="check" size={14} color={theme.colors.primary} />
                      )}
                    </View>
                    <Text style={styles.serviceText}>{service}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Has Tools */}
            <View style={styles.switchContainer}>
              <Text style={styles.label}>Do you have your own cleaning tools?</Text>
              <View style={styles.switchRow}>
                <Switch
                  value={hasTools}
                  onValueChange={setHasTools}
                  trackColor={{ false: theme.colors.border, true: `${theme.colors.primary}80` }}
                  thumbColor={hasTools ? theme.colors.primary : theme.colors.card}
                />
                <Text style={styles.switchLabel}>{hasTools ? "Yes" : "No"}</Text>
              </View>
            </View>

            {/* Owned Tools */}
            {hasTools && (
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Which tools do you have?</Text>
                <Text style={styles.helperText}>Select all that apply</Text>
                <View style={styles.toolsGrid}>
                  {toolOptions.map((tool) => (
                    <TouchableOpacity
                      key={tool}
                      style={[
                        styles.toolOption,
                        ownedTools.includes(tool) && styles.toolOptionSelected
                      ]}
                      onPress={() => {
                        if (ownedTools.includes(tool)) {
                          setOwnedTools(ownedTools.filter(t => t !== tool))
                        } else {
                          setOwnedTools([...ownedTools, tool])
                        }
                      }}
                    >
                      <View style={styles.toolCheckbox}>
                        {ownedTools.includes(tool) && (
                          <Feather name="check" size={14} color={theme.colors.primary} />
                        )}
                      </View>
                      <Text style={styles.toolText}>{tool}</Text>
                    </TouchableOpacity>
                  ))}
                </View>

                {/* Other Tools */}
                <View style={styles.inputWrapper}>
                  <Feather name="plus" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    placeholder="Other tools (please specify)"
                    placeholderTextColor={theme.colors.textLight}
                    value={otherTools}
                    onChangeText={setOtherTools}
                  />
                </View>
              </View>
            )}

            {/* Experience */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Years of Experience <Text style={styles.required}>*</Text></Text>
              <View style={styles.radioGroup}>
                {experienceOptions.map((option) => (
                  <TouchableOpacity
                    key={option}
                    style={styles.radioOption}
                    onPress={() => setExperience(option)}
                  >
                    <View style={styles.radioButtonContainer}>
                      <View
                        style={[
                          styles.radioButton,
                          experience === option && styles.radioButtonSelected
                        ]}
                      >
                        {experience === option && (
                          <View style={styles.radioButtonInner} />
                        )}
                      </View>
                    </View>
                    <Text style={styles.radioLabel}>{option}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Employment Status */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Current Employment Status <Text style={styles.required}>*</Text></Text>
              <View style={styles.radioGroup}>
                {employmentStatusOptions.map((option) => (
                  <TouchableOpacity
                    key={option}
                    style={styles.radioOption}
                    onPress={() => setEmploymentStatus(option)}
                  >
                    <View style={styles.radioButtonContainer}>
                      <View
                        style={[
                          styles.radioButton,
                          employmentStatus === option && styles.radioButtonSelected
                        ]}
                      >
                        {employmentStatus === option && (
                          <View style={styles.radioButtonInner} />
                        )}
                      </View>
                    </View>
                    <Text style={styles.radioLabel}>{option}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>


          </>
        )}

        {/* Step 3: Security & Screening */}
        {currentStep === 3 && (
          <>
            {/* Upload Police Clearance */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Upload Police Clearance Certificate <Text style={styles.required}>*</Text></Text>
              <TouchableOpacity style={styles.uploadContainer} onPress={pickPoliceClearance}>
                {policeClearanceUploaded ? (
                  <View style={styles.uploadedImageContainer}>
                    <Feather name="check-circle" size={24} color={theme.colors.success} />
                    <Text style={styles.uploadedText}>Police Clearance Uploaded</Text>
                  </View>
                ) : (
                  <>
                    <Feather name="upload" size={24} color={theme.colors.textLight} />
                    <Text style={styles.uploadText}>Tap to upload</Text>
                    <Text style={styles.uploadSubtext}>(PDF or Image)</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            {/* Upload Reference Letter */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Upload Reference Letter (optional)</Text>
              <TouchableOpacity style={styles.uploadContainer} onPress={pickReferenceLetter}>
                {referenceLetterUploaded ? (
                  <View style={styles.uploadedImageContainer}>
                    <Feather name="check-circle" size={24} color={theme.colors.success} />
                    <Text style={styles.uploadedText}>Reference Letter Uploaded</Text>
                  </View>
                ) : (
                  <>
                    <Feather name="upload" size={24} color={theme.colors.textLight} />
                    <Text style={styles.uploadText}>Tap to upload</Text>
                    <Text style={styles.uploadSubtext}>(PDF or Image)</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            {/* Police Cases */}
            <View style={styles.switchContainer}>
              <Text style={styles.label}>Have you had any police cases before?</Text>
              <View style={styles.switchRow}>
                <Switch
                  value={hasPoliceCases}
                  onValueChange={setHasPoliceCases}
                  trackColor={{ false: theme.colors.border, true: `${theme.colors.primary}80` }}
                  thumbColor={hasPoliceCases ? theme.colors.primary : theme.colors.card}
                />
                <Text style={styles.switchLabel}>{hasPoliceCases ? "Yes" : "No"}</Text>
              </View>
            </View>

            {/* Police Cases Explanation */}
            {hasPoliceCases && (
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Please explain <Text style={styles.required}>*</Text></Text>
                <View style={styles.textAreaWrapper}>
                  <TextInput
                    style={styles.textArea}
                    placeholder="Provide details about any police cases..."
                    placeholderTextColor={theme.colors.textLight}
                    value={policeCasesExplanation}
                    onChangeText={setPoliceCasesExplanation}
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                  />
                </View>
              </View>
            )}

            {/* Join Reason */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Why do you want to join CleanConnect? <Text style={styles.required}>*</Text></Text>
              <View style={styles.textAreaWrapper}>
                <TextInput
                  style={styles.textArea}
                  placeholder="Share your motivation for joining our platform..."
                  placeholderTextColor={theme.colors.textLight}
                  value={joinReason}
                  onChangeText={setJoinReason}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
            </View>

            {/* Confirm Information */}
            <View style={styles.termsContainer}>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setConfirmInfoCorrect(!confirmInfoCorrect)}
              >
                <View
                  style={[
                    styles.checkbox,
                    confirmInfoCorrect && styles.checkboxChecked,
                  ]}
                >
                  {confirmInfoCorrect && (
                    <Feather name="check" size={14} color={theme.colors.card} />
                  )}
                </View>
              </TouchableOpacity>
              <Text style={styles.termsText}>
                I confirm that all the information provided above is correct and accurate.
              </Text>
            </View>

            {/* Signature */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Full Name as Signature <Text style={styles.required}>*</Text></Text>
              <View style={styles.inputWrapper}>
                <Feather name="edit-2" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Type your full name as signature"
                  placeholderTextColor={theme.colors.textLight}
                  value={signature}
                  onChangeText={setSignature}
                />
              </View>
            </View>
          </>
        )}

        {/* Navigation Buttons */}
        <View style={styles.navigationButtons}>
          {currentStep > 1 && (
            <Button
              title="Previous"
              variant="outline"
              onPress={goToPreviousStep}
              style={styles.navigationButton}
            />
          )}

          <Button
            title={currentStep === 3 ? (isSubmitting ? "Submitting..." : "Submit Application") : "Next"}
            variant="warning"
            onPress={goToNextStep}
            disabled={isSubmitting}
            style={[styles.navigationButton, currentStep === 1 && styles.fullWidthButton]}
          />
        </View>
      </View>
    </ScrollView>
  )
}

// Create styles using the theme
const getStyles = (theme) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1,
  },
  contentContainer: {
    padding: theme.spacing.lg,
    paddingBottom: theme.spacing.xl * 2,
  },
  header: {
    flexDirection: "row",
    marginBottom: theme.spacing.lg,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  title: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.md,
    marginBottom: theme.spacing.xl,
  },
  // Progress indicator styles
  progressContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.md,
    position: "relative",
  },
  progressLine: {
    backgroundColor: theme.colors.border,
    height: 2,
    left: "10%",
    position: "absolute",
    right: "10%",
    top: 15,
    zIndex: -1,
  },
  progressStepWrapper: {
    alignItems: "center",
    width: "33%",
  },
  progressStep: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: 15,
    borderWidth: 2,
    height: 30,
    justifyContent: "center",
    marginBottom: theme.spacing.xs,
    width: 30,
    zIndex: 1,
  },
  progressStepActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  progressStepText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
    fontWeight: "bold",
  },
  progressStepTextActive: {
    color: theme.colors.card,
  },
  progressStepLabel: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.xs,
    textAlign: "center",
  },
  form: {
    marginTop: theme.spacing.md,
  },
  inputContainer: {
    marginBottom: theme.spacing.lg,
  },
  label: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.sm,
    fontWeight: "500",
    marginBottom: theme.spacing.xs,
  },
  labelWithTooltip: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: theme.spacing.xs,
  },
  required: {
    color: theme.colors.error,
  },
  helperText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.xs,
    marginTop: theme.spacing.xs,
  },
  dateInputRow: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
  },
  dateInputWrapper: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    flexDirection: "row",
    flex: 1,
    paddingHorizontal: theme.spacing.sm,
    marginRight: theme.spacing.sm,
  },
  calendarButton: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    height: 48, // Match input field height
    justifyContent: "center",
    width: 48,
    aspectRatio: 1, // Keep it square on all devices
  },
  inputWrapper: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    flexDirection: "row",
    paddingHorizontal: theme.spacing.sm,
  },
  inputIcon: {
    marginRight: theme.spacing.sm,
  },
  input: {
    color: theme.colors.text,
    flex: 1,
    fontSize: theme.fontSizes.md,
    paddingVertical: theme.spacing.md,
  },
  placeholderText: {
    color: theme.colors.textLight,
  },
  datePickerButton: {
    width: "100%",
  },
  modalOverlay: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  datePickerContainer: {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    width: "85%",
    maxWidth: 320,
    minHeight: 350, // Reduced height
    maxHeight: 450, // Reduced max height
    shadowColor: theme.colors.shadow || "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  datePickerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.md,
  },
  datePickerTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "600",
    color: theme.colors.text,
  },
  calendarContainer: {
    marginVertical: theme.spacing.md,
  },
  calendarGrid: {
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
  },
  calendarHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.md,
  },
  calendarMonthYear: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.text,
  },
  monthYearContainer: {
    flexDirection: "column",
    alignItems: "center",
  },
  monthYearRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
  },
  calendarMonth: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.text,
  },
  calendarYear: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.text,
  },
  yearSelector: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
  },
  yearSelectorDropdown: {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.md,
    marginTop: 4,
    marginBottom: 8,
    maxHeight: 150,
    width: "100%",
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  yearScrollView: {
    maxHeight: 150,
    width: "100%",
  },
  yearScrollViewContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
  },
  yearOption: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    alignItems: 'center',
    width: '100%',
  },
  yearOptionSelected: {
    backgroundColor: `${theme.colors.primary}15`,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary,
  },
  yearOptionText: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.md,
    paddingVertical: 2,
  },
  yearOptionTextSelected: {
    color: theme.colors.primary,
    fontWeight: "600",
  },
  weekdayHeader: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: theme.spacing.sm,
  },
  weekdayText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.textLight,
    width: 24,
    textAlign: "center",
  },
  calendarDays: {
    alignItems: "center",
    justifyContent: "center",
    minHeight: 160,
  },
  calendarDaysGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    width: "100%",
  },
  calendarDay: {
    alignItems: "center",
    borderRadius: 16,
    height: 32,
    justifyContent: "center",
    margin: 0,
    width: "14.28%", // 7 days per week (100% / 7)
  },
  calendarDayToday: {
    borderColor: theme.colors.primary,
    borderWidth: 1,
  },
  calendarDaySelected: {
    backgroundColor: theme.colors.primary,
  },
  calendarDayDisabled: {
    opacity: 0.3,
  },
  calendarDayText: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.sm,
  },
  calendarDayTextToday: {
    color: theme.colors.primary,
    fontWeight: "bold",
  },
  calendarDayTextSelected: {
    color: theme.colors.card,
    fontWeight: "bold",
  },
  calendarDayTextDisabled: {
    color: theme.colors.textLight,
  },
  calendarMessage: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.md,
    textAlign: "center",
    marginVertical: theme.spacing.lg,
  },
  datePickerActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: theme.spacing.md,
  },
  datePickerCancel: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    marginRight: theme.spacing.md,
  },
  datePickerCancelText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.md,
  },
  datePickerConfirm: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.sm,
  },
  datePickerConfirmText: {
    color: theme.colors.card,
    fontSize: theme.fontSizes.md,
    fontWeight: "500",
  },
  // Radio button styles for gender selection
  radioGroup: {
    marginTop: theme.spacing.sm,
  },
  radioOption: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: theme.spacing.md,
  },
  radioButtonContainer: {
    marginRight: theme.spacing.md,
  },
  radioButton: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: 12,
    borderWidth: 2,
    height: 24,
    justifyContent: "center",
    width: 24,
  },
  radioButtonSelected: {
    borderColor: theme.colors.primary,
  },
  radioButtonInner: {
    backgroundColor: theme.colors.primary,
    borderRadius: 6,
    height: 12,
    width: 12,
  },
  radioLabel: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.md,
  },
  // Experience options
  experienceOption: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    margin: theme.spacing.xs,
    padding: theme.spacing.sm,
    width: `${50 - (theme.spacing.xs * 4)}%`,
  },
  experienceOptionSelected: {
    backgroundColor: `${theme.colors.primary}10`,
    borderColor: theme.colors.primary,
  },
  experienceOptionText: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.sm,
  },
  experienceOptionTextSelected: {
    color: theme.colors.primary,
    fontWeight: "500",
  },
  // Employment status options
  statusOption: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    margin: theme.spacing.xs,
    padding: theme.spacing.sm,
    width: `${50 - (theme.spacing.xs * 4)}%`,
  },
  statusOptionSelected: {
    backgroundColor: `${theme.colors.primary}10`,
    borderColor: theme.colors.primary,
  },
  statusOptionText: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.sm,
  },
  statusOptionTextSelected: {
    color: theme.colors.primary,
    fontWeight: "500",
  },
  // Services grid
  servicesGrid: {
    marginTop: theme.spacing.sm,
  },
  serviceOption: {
    alignItems: "center",
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    flexDirection: "row",
    marginBottom: theme.spacing.sm,
    padding: theme.spacing.sm,
  },
  serviceOptionSelected: {
    backgroundColor: `${theme.colors.primary}10`,
    borderColor: theme.colors.primary,
  },
  serviceCheckbox: {
    alignItems: "center",
    borderColor: theme.colors.border,
    borderRadius: 4,
    borderWidth: 1,
    height: 20,
    justifyContent: "center",
    marginRight: theme.spacing.sm,
    width: 20,
  },
  serviceText: {
    color: theme.colors.text,
    flex: 1,
    fontSize: theme.fontSizes.sm,
  },
  // Tools grid
  toolsGrid: {
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  toolOption: {
    alignItems: "center",
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    flexDirection: "row",
    marginBottom: theme.spacing.sm,
    padding: theme.spacing.sm,
  },
  toolOptionSelected: {
    backgroundColor: `${theme.colors.primary}10`,
    borderColor: theme.colors.primary,
  },
  toolCheckbox: {
    alignItems: "center",
    borderColor: theme.colors.border,
    borderRadius: 4,
    borderWidth: 1,
    height: 20,
    justifyContent: "center",
    marginRight: theme.spacing.sm,
    width: 20,
  },
  toolText: {
    color: theme.colors.text,
    flex: 1,
    fontSize: theme.fontSizes.sm,
  },
  // Areas grid
  areasGrid: {
    marginTop: theme.spacing.sm,
  },
  areaOption: {
    alignItems: "center",
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    flexDirection: "row",
    marginBottom: theme.spacing.sm,
    padding: theme.spacing.sm,
  },
  areaOptionSelected: {
    backgroundColor: `${theme.colors.primary}10`,
    borderColor: theme.colors.primary,
  },
  areaCheckbox: {
    alignItems: "center",
    borderColor: theme.colors.border,
    borderRadius: 4,
    borderWidth: 1,
    height: 20,
    justifyContent: "center",
    marginRight: theme.spacing.sm,
    width: 20,
  },
  areaText: {
    color: theme.colors.text,
    flex: 1,
    fontSize: theme.fontSizes.sm,
  },
  // Availability
  availabilityContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: theme.spacing.sm,
  },
  dayOption: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    margin: theme.spacing.xs,
    padding: theme.spacing.sm,
    width: `${33.33 - (theme.spacing.xs * 4)}%`,
  },
  dayOptionSelected: {
    backgroundColor: `${theme.colors.primary}10`,
    borderColor: theme.colors.primary,
  },
  dayText: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.sm,
  },
  dayTextSelected: {
    color: theme.colors.primary,
    fontWeight: "500",
  },
  // Switch
  switchContainer: {
    marginBottom: theme.spacing.lg,
  },
  switchRow: {
    alignItems: "center",
    flexDirection: "row",
    marginTop: theme.spacing.sm,
  },
  switchLabel: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.md,
    marginLeft: theme.spacing.sm,
  },
  // Upload
  uploadContainer: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderStyle: "dashed",
    borderWidth: 1,
    height: 150,
    justifyContent: "center",
    width: "100%",
  },
  uploadText: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.md,
    marginTop: theme.spacing.sm,
  },
  uploadSubtext: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
    fontStyle: "italic",
    marginTop: theme.spacing.xs,
  },
  uploadedImageContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  uploadedText: {
    color: theme.colors.success,
    fontSize: theme.fontSizes.md,
    fontWeight: "500",
    marginTop: theme.spacing.sm,
  },
  // Text area
  textAreaWrapper: {
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    padding: theme.spacing.sm,
  },
  textArea: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.md,
    height: 100,
    textAlignVertical: "top",
  },
  // Terms
  termsContainer: {
    alignItems: "flex-start",
    flexDirection: "row",
    marginBottom: theme.spacing.lg,
  },
  checkboxContainer: {
    marginRight: theme.spacing.sm,
    marginTop: 2, // Align with text
  },
  checkbox: {
    alignItems: "center",
    borderColor: theme.colors.border,
    borderRadius: 4,
    borderWidth: 2,
    height: 20,
    justifyContent: "center",
    width: 20,
  },
  checkboxChecked: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  termsText: {
    color: theme.colors.textLight,
    flex: 1,
    fontSize: theme.fontSizes.sm,
    lineHeight: 20,
  },
  // Navigation buttons
  navigationButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: theme.spacing.lg,
  },
  navigationButton: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  fullWidthButton: {
    marginHorizontal: 0,
  },
  // Success screen
  successContainer: {
    alignItems: "center",
    backgroundColor: theme.colors.background,
    flex: 1,
    justifyContent: "center",
    padding: theme.spacing.xl,
  },
  successIconContainer: {
    marginBottom: theme.spacing.lg,
  },
  successTitle: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    marginBottom: theme.spacing.md,
    textAlign: "center",
  },
  successMessage: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.md,
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
    textAlign: "center",
  },
  successButton: {
    marginTop: theme.spacing.lg,
    width: "100%",
  },
})

export default CleanerApplicationFormScreen
