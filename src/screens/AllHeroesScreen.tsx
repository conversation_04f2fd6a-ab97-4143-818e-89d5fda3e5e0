import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  TextInput,
  ScrollView,
  Animated,
  Dimensions,
  Platform,
  ActivityIndicator,
  KeyboardAvoidingView,
  Keyboard,
  RefreshControl,
} from 'react-native';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../context/ThemeContext';
import { useResponsive } from '../hooks/useResponsive';
import type { RootStackParamList } from '../navigation/RootNavigator';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Card } from '../components/common';
import { localProviderService } from '../services/localProviderService';

type AllHeroesScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Define filter options
type FilterOption = {
  id: string;
  label: string;
};

// Define rating filter options
const ratingFilters: FilterOption[] = [
  { id: 'all', label: 'All Ratings' },
  { id: '4.5+', label: '4.5+' },
  { id: '4+', label: '4+' },
  { id: '3.5+', label: '3.5+' },
];

// Define price filters
const priceFilters: FilterOption[] = [
  { id: 'all', label: 'All Prices' },
  { id: 'low', label: 'Low to High' },
  { id: 'high', label: 'High to Low' },
];

// Define service type filters
const serviceTypeFilters: FilterOption[] = [
  { id: 'all', label: 'All Services' },
  { id: 'home', label: 'Home Cleaning' },
  { id: 'office', label: 'Office Cleaning' },
  { id: 'deep', label: 'Deep Cleaning' },
];

const AllHeroesScreen = () => {
  const navigation = useNavigation<AllHeroesScreenNavigationProp>();
  const theme = useTheme();
  const { width, height } = useResponsive();
  const { width: windowWidth } = Dimensions.get('window');
  const insets = useSafeAreaInsets();

  // No longer need dynamic margin top as we'll use SafeAreaInsets

  // Refs
  const scrollY = useRef(new Animated.Value(0)).current;
  const listRef = useRef<FlatList>(null);

  // State for UI
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  // State for filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRatingFilter, setSelectedRatingFilter] = useState('all');
  const [selectedPriceFilter, setSelectedPriceFilter] = useState('all');
  const [selectedServiceFilter, setSelectedServiceFilter] = useState('all');
  const [heroes, setHeroes] = useState<any[]>([]);
  const [filteredHeroes, setFilteredHeroes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Load heroes from API
  useEffect(() => {
    const loadHeroes = async () => {
      try {
        setLoading(true);
        const providers = await localProviderService.getTopRatedProviders(10);
        setHeroes(providers);
        setFilteredHeroes(providers);
      } catch (error) {
        console.error('Error loading heroes:', error);
      } finally {
        setLoading(false);
      }
    };

    loadHeroes();
  }, []);

  // Temporary filter state (for the modal)
  const [tempRatingFilter, setTempRatingFilter] = useState('all');
  const [tempPriceFilter, setTempPriceFilter] = useState('all');
  const [tempServiceFilter, setTempServiceFilter] = useState('all');

  // Calculate number of columns based on screen width
  const numColumns = windowWidth >= 768 ? 2 : 1;

  // Header animation
  const headerHeight = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [Platform.OS === 'ios' ? 120 : 100, Platform.OS === 'ios' ? 80 : 60],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 60],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  const searchBarTranslateY = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, -40],
    extrapolate: 'clamp',
  });

  // Refresh heroes list
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1500);
  }, []);

  // Reset to top when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      listRef.current?.scrollToOffset({ offset: 0, animated: true });
      return () => {};
    }, [])
  );

  // Apply filters when any filter changes
  useEffect(() => {
    setIsLoading(true);

    // Simulate API delay for better UX
    const timer = setTimeout(() => {
      let result = [...heroes];

      // Apply search filter
      if (searchQuery) {
        result = result.filter(hero =>
          hero.name.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // Apply rating filter
      if (selectedRatingFilter !== 'all') {
        const minRating = parseFloat(selectedRatingFilter);
        result = result.filter(hero => hero.rating >= minRating);
      }

      // Apply price filter
      if (selectedPriceFilter !== 'all') {
        if (selectedPriceFilter === 'low') {
          result = [...result].sort((a, b) => a.price - b.price);
        } else if (selectedPriceFilter === 'high') {
          result = [...result].sort((a, b) => b.price - a.price);
        }
      }

      // Apply service filter (in a real app, heroes would have service types)
      if (selectedServiceFilter !== 'all') {
        // In a real app, you would filter by service type
        // For now, we'll just use a random subset for demonstration
        if (selectedServiceFilter === 'home') {
          result = result.filter(hero => hero.id.charCodeAt(0) % 3 === 0);
        } else if (selectedServiceFilter === 'office') {
          result = result.filter(hero => hero.id.charCodeAt(0) % 3 === 1);
        } else if (selectedServiceFilter === 'deep') {
          result = result.filter(hero => hero.id.charCodeAt(0) % 3 === 2);
        }
      }

      setFilteredHeroes(result);
      setIsLoading(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, selectedRatingFilter, selectedPriceFilter, selectedServiceFilter]);

  // Handle hero press - navigate to hero detail
  const handleHeroPress = useCallback((heroId: string) => {
    navigation.navigate('HeroDetail', { heroId });
  }, [navigation]);

  // Toggle filter modal
  const toggleFilterModal = useCallback(() => {
    if (!showFilterModal) {
      // When opening the modal, initialize temp filters with current values
      setTempRatingFilter(selectedRatingFilter);
      setTempPriceFilter(selectedPriceFilter);
      setTempServiceFilter(selectedServiceFilter);
    }
    setShowFilterModal(prev => !prev);
    Keyboard.dismiss();
  }, [showFilterModal, selectedRatingFilter, selectedPriceFilter, selectedServiceFilter]);

  // Apply filters from modal
  const applyFilters = useCallback(() => {
    // Apply temp filters to actual filters
    setSelectedRatingFilter(tempRatingFilter);
    setSelectedPriceFilter(tempPriceFilter);
    setSelectedServiceFilter(tempServiceFilter);
    setShowFilterModal(false);
  }, [tempRatingFilter, tempPriceFilter, tempServiceFilter]);

  // Render filter chip for modal
  const renderFilterChip = useCallback(
    (option: FilterOption, selectedFilter: string, onSelect: (id: string) => void) => (
      <TouchableOpacity
        key={option.id}
        style={[
          styles.filterChip,
          {
            backgroundColor: selectedFilter === option.id
              ? theme.colors.primary
              : theme.colors.card,
            borderColor: selectedFilter === option.id
              ? theme.colors.primary
              : theme.colors.border,
          }
        ]}
        onPress={() => onSelect(option.id)}
        activeOpacity={0.7}
      >
        <Text
          style={[
            styles.filterChipText,
            {
              color: selectedFilter === option.id
                ? 'white'
                : theme.colors.text,
              fontWeight: selectedFilter === option.id ? '600' : '400',
            }
          ]}
        >
          {option.label}
        </Text>
      </TouchableOpacity>
    ),
    [theme.colors]
  );

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    // Clear actual filters
    setSelectedRatingFilter('all');
    setSelectedPriceFilter('all');
    setSelectedServiceFilter('all');
    setSearchQuery('');

    // Also clear temp filters
    setTempRatingFilter('all');
    setTempPriceFilter('all');
    setTempServiceFilter('all');

    Keyboard.dismiss();
  }, []);

  // Filter modal component
  const FilterModal = useCallback(() => {
    if (!showFilterModal) return null;

    return (
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={toggleFilterModal}
      >
        <TouchableOpacity
          activeOpacity={1}
          style={[styles.modalContent, { backgroundColor: theme.colors.card }]}
          onPress={e => e.stopPropagation()}
        >
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Filter Heroes</Text>
            <TouchableOpacity onPress={toggleFilterModal}>
              <Feather name="x" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            <View style={styles.filterSection}>
              <Text style={[styles.filterLabel, { color: theme.colors.text }]}>Rating</Text>
              <View style={styles.filterChips}>
                {ratingFilters.map(option =>
                  renderFilterChip(option, tempRatingFilter, setTempRatingFilter)
                )}
              </View>
            </View>

            <View style={styles.filterSection}>
              <Text style={[styles.filterLabel, { color: theme.colors.text }]}>Price</Text>
              <View style={styles.filterChips}>
                {priceFilters.map(option =>
                  renderFilterChip(option, tempPriceFilter, setTempPriceFilter)
                )}
              </View>
            </View>

            <View style={styles.filterSection}>
              <Text style={[styles.filterLabel, { color: theme.colors.text }]}>Service Type</Text>
              <View style={styles.filterChips}>
                {serviceTypeFilters.map(option =>
                  renderFilterChip(option, tempServiceFilter, setTempServiceFilter)
                )}
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={[styles.modalButton, styles.modalButtonOutline, { borderColor: theme.colors.border }]}
              onPress={clearAllFilters}
            >
              <Text style={[styles.modalButtonText, { color: theme.colors.text }]}>Reset</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, { backgroundColor: theme.colors.primary }]}
              onPress={applyFilters}
            >
              <Text style={[styles.modalButtonText, { color: 'white' }]}>Apply</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
    );
  }, [showFilterModal, theme.colors, tempRatingFilter, tempPriceFilter, tempServiceFilter, renderFilterChip, clearAllFilters, applyFilters, toggleFilterModal]);

  // Render hero item with memo for performance
  const renderHeroItem = useCallback(({ item, index }: { item: any, index: number }) => {
    // Calculate animation delay based on index for staggered appearance
    const animationDelay = index * 100;

    return (
      <Animated.View
        style={{
          opacity: 1,
          transform: [{ translateY: 0 }],
          width: numColumns > 1 ? `${100 / numColumns}%` : '100%',
        }}
      >
        <Card
          key={item.id}
          style={[styles.heroCard, numColumns > 1 && { margin: 8 }]}
        >
          <TouchableOpacity
            onPress={() => handleHeroPress(item.id)}
            style={styles.heroCardTouchable}
            activeOpacity={0.7}
          >
            <View style={styles.heroImageContainer}>
              <Image
                source={{ uri: item.image }}
                style={styles.heroAvatar}
                resizeMode="cover"
              />
              {item.rating >= 4.8 && (
                <View style={[styles.heroBadge, { backgroundColor: theme.colors.warning }]}>
                  <Text style={styles.heroBadgeText}>Top Hero</Text>
                </View>
              )}
            </View>
            <View style={styles.heroContent}>
              <View style={styles.heroNameRow}>
                <Text style={[styles.heroName, { color: theme.colors.text }]}>{item.name}</Text>
                {item.rating > 4.5 && (
                  <View style={[styles.verifiedBadge, { backgroundColor: `${theme.colors.primary}15` }]}>
                    <Feather name="check-circle" size={12} color={theme.colors.primary} style={{ marginRight: 2 }} />
                    <Text style={[styles.verifiedText, { color: theme.colors.primary }]}>Verified</Text>
                  </View>
                )}
              </View>
              <View style={styles.heroDetailsRow}>
                <View style={styles.ratingContainer}>
                  <Feather name="star" size={14} color="#FFD700" style={{ marginRight: 4 }} />
                  <Text style={[styles.ratingText, { color: theme.colors.text }]}>{item.rating}</Text>
                  <Text style={[styles.reviewsText, { color: theme.colors.textLight }]}>({item.reviews})</Text>
                </View>
                <Text style={[styles.heroPrice, { color: theme.colors.text }]}>From GMD {item.price}/hr</Text>
              </View>
            </View>
            <Feather name="chevron-right" size={18} color={theme.colors.textLight} style={styles.heroChevron} />
          </TouchableOpacity>
        </Card>
      </Animated.View>
    );
  }, [theme.colors, numColumns, handleHeroPress]);

  // Render empty state
  const renderEmptyState = useCallback(() => {
    if (isLoading) return null;

    return (
      <View style={styles.emptyStateContainer}>
        <Feather name="search" size={48} color={theme.colors.textLight} />
        <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
          No heroes found
        </Text>
        <Text style={[styles.emptyStateDescription, { color: theme.colors.textLight }]}>
          Try adjusting your filters or search query
        </Text>
        <TouchableOpacity
          style={[styles.emptyStateButton, { backgroundColor: theme.colors.primary }]}
          onPress={clearAllFilters}
        >
          <Text style={styles.emptyStateButtonText}>Clear Filters</Text>
        </TouchableOpacity>
      </View>
    );
  }, [isLoading, theme.colors, clearAllFilters]);

  // Get active filter labels
  const getActiveFilterLabels = useCallback(() => {
    const activeFilters = [];

    if (selectedRatingFilter !== 'all') {
      const ratingFilter = ratingFilters.find(f => f.id === selectedRatingFilter);
      if (ratingFilter) activeFilters.push(ratingFilter.label);
    }

    if (selectedPriceFilter !== 'all') {
      const priceFilter = priceFilters.find(f => f.id === selectedPriceFilter);
      if (priceFilter) activeFilters.push(priceFilter.label);
    }

    if (selectedServiceFilter !== 'all') {
      const serviceFilter = serviceTypeFilters.find(f => f.id === selectedServiceFilter);
      if (serviceFilter) activeFilters.push(serviceFilter.label);
    }

    return activeFilters;
  }, [selectedRatingFilter, selectedPriceFilter, selectedServiceFilter]);

  // Render list header with active filters summary
  const renderListHeader = useCallback(() => {
    const activeFilters = getActiveFilterLabels();
    const hasActiveFilters = activeFilters.length > 0 || searchQuery;

    return (
      <View style={styles.filtersContainer}>
        {/* Results count and active filters */}
        <View style={styles.resultsHeader}>
          <View style={styles.resultsInfo}>
            <Text style={[styles.resultsCount, { color: theme.colors.text }]}>
              {filteredHeroes.length} Heroes found
            </Text>
            {hasActiveFilters && (
              <View style={styles.activeFiltersContainer}>
                {searchQuery && (
                  <View style={[styles.activeFilterChip, { backgroundColor: `${theme.colors.primary}15` }]}>
                    <Text style={[styles.activeFilterText, { color: theme.colors.primary }]}>
                      Search: {searchQuery}
                    </Text>
                    <TouchableOpacity
                      onPress={() => setSearchQuery('')}
                      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    >
                      <Feather name="x" size={12} color={theme.colors.primary} />
                    </TouchableOpacity>
                  </View>
                )}

                {activeFilters.map((filter, index) => (
                  <View
                    key={`filter-${index}`}
                    style={[styles.activeFilterChip, { backgroundColor: `${theme.colors.primary}15` }]}
                  >
                    <Text style={[styles.activeFilterText, { color: theme.colors.primary }]}>
                      {filter}
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </View>

          {hasActiveFilters && (
            <TouchableOpacity
              onPress={clearAllFilters}
              style={styles.clearFiltersButton}
              activeOpacity={0.7}
            >
              <Text style={[styles.clearFiltersText, { color: theme.colors.primary }]}>
                Clear All
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Filter button */}
        <TouchableOpacity
          style={[styles.filterButton2, { borderColor: theme.colors.border }]}
          onPress={toggleFilterModal}
          activeOpacity={0.7}
        >
          <Feather name="sliders" size={16} color={theme.colors.text} style={{ marginRight: 8 }} />
          <Text style={[styles.filterButtonText, { color: theme.colors.text }]}>Filter</Text>
        </TouchableOpacity>
      </View>
    );
  }, [
    theme.colors,
    selectedRatingFilter,
    selectedPriceFilter,
    selectedServiceFilter,
    searchQuery,
    filteredHeroes.length,
    getActiveFilterLabels,
    clearAllFilters,
    toggleFilterModal
  ]);

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar barStyle="dark-content" />

        {/* Status Bar Space */}
        <View style={{ height: insets.top, backgroundColor: theme.colors.primary }} />

        {/* Animated Header */}
        <Animated.View
          style={[
            styles.header,
            {
              height: headerHeight,
              backgroundColor: theme.colors.background,
              borderBottomColor: theme.colors.border,
              paddingTop: Platform.OS === 'ios' ? 10 : 20,
            }
          ]}
        >
          <View style={styles.headerTopRow}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={styles.backButton}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              activeOpacity={0.7}
            >
              <Feather name="chevron-left" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>All CleanConnect Heroes</Text>
            <TouchableOpacity
              onPress={toggleFilterModal}
              style={styles.filterButton}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              activeOpacity={0.7}
            >
              <Feather name="sliders" size={20} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          {/* Animated Search Bar */}
          <Animated.View
            style={[
              styles.searchContainer,
              {
                borderColor: isSearchFocused ? theme.colors.primary : theme.colors.border,
                transform: [{ translateY: searchBarTranslateY }],
                backgroundColor: theme.colors.card,
              }
            ]}
          >
            <Feather name="search" size={20} color={theme.colors.textLight} style={styles.searchIcon} />
            <TextInput
              style={[styles.searchInput, { color: theme.colors.text }]}
              placeholder="Search heroes by name..."
              placeholderTextColor={theme.colors.textLight}
              value={searchQuery}
              onChangeText={setSearchQuery}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              returnKeyType="search"
              clearButtonMode="while-editing"
            />
            {searchQuery ? (
              <TouchableOpacity
                onPress={() => setSearchQuery('')}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Feather name="x" size={20} color={theme.colors.textLight} />
              </TouchableOpacity>
            ) : null}
          </Animated.View>
        </Animated.View>

        {/* Filter Modal */}
        <FilterModal />

        {/* Main Content */}
        <View style={styles.content}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={[styles.loadingText, { color: theme.colors.text }]}>Finding heroes...</Text>
            </View>
          ) : (
            <FlatList
              ref={listRef}
              data={filteredHeroes}
              renderItem={renderHeroItem}
              keyExtractor={item => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.heroesListContent}
              numColumns={numColumns}
              key={`heroes-list-${numColumns}`}
              ListHeaderComponent={renderListHeader}
              ListEmptyComponent={renderEmptyState}
              onScroll={Animated.event(
                [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                { useNativeDriver: false }
              )}
              refreshControl={
                <RefreshControl
                  refreshing={isRefreshing}
                  onRefresh={handleRefresh}
                  colors={[theme.colors.primary]}
                  tintColor={theme.colors.primary}
                />
              }
              initialNumToRender={10}
              maxToRenderPerBatch={10}
              windowSize={10}
              removeClippedSubviews={Platform.OS === 'android'}
            />
          )}
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    zIndex: 10,
  },
  headerTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  backButton: {
    padding: 4,
    borderRadius: 20,
  },
  filterButton: {
    padding: 4,
    borderRadius: 20,
  },
  filterButton2: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginTop: 8,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    flex: 1,
  },
  content: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    padding: 0,
    height: 24,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
  },
  filtersContent: {
    paddingRight: 16,
    paddingBottom: 8,
  },
  filterSection: {
    marginRight: 16,
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    marginBottom: 12,
    fontWeight: '500',
  },
  filterChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterChip: {
    borderWidth: 1,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  filterChipText: {
    fontSize: 12,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  resultsInfo: {
    flex: 1,
    marginRight: 8,
  },
  resultsCount: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  activeFiltersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  activeFilterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  activeFilterText: {
    fontSize: 12,
    marginRight: 4,
  },
  clearFiltersButton: {
    padding: 4,
    marginTop: 2,
  },
  clearFiltersText: {
    fontSize: 14,
    fontWeight: '500',
  },

  // Modal styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    zIndex: 1000,
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 16,
    paddingBottom: Platform.OS === 'ios' ? 40 : 24,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#EEEEEE',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalBody: {
    padding: 16,
    maxHeight: '70%',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 16,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: '#EEEEEE',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  modalButtonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  heroesListContent: {
    paddingBottom: 24,
    paddingHorizontal: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    minHeight: 300,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyStateButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 8,
  },
  emptyStateButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  heroCard: {
    marginBottom: 16,
    padding: 0,
    overflow: 'hidden',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  heroCardTouchable: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  heroImageContainer: {
    position: 'relative',
    width: 70,
    height: 70,
    borderRadius: 35,
    overflow: 'hidden',
    margin: 12,
  },
  heroAvatar: {
    width: '100%',
    height: '100%',
  },
  heroBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  heroBadgeText: {
    fontSize: 8,
    fontWeight: '700',
    color: '#000',
  },
  heroContent: {
    flex: 1,
    padding: 12,
  },
  heroNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  heroName: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  verifiedText: {
    fontSize: 10,
    fontWeight: '600',
  },
  heroDetailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
  },
  reviewsText: {
    fontSize: 12,
    marginLeft: 4,
  },
  heroPrice: {
    fontSize: 12,
    fontWeight: '500',
  },
  heroChevron: {
    marginRight: 12,
  },
});

export default AllHeroesScreen;
