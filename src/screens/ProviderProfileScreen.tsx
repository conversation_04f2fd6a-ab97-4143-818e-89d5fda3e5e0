"use client";

import { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
  Modal,
  ActivityIndicator,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Feather } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import { useAuth } from "../context/AuthContext";
import type { RootStackParamList } from "../navigation/RootNavigator";
import Header from "../components/layout/Header";
import Button from "../components/common/Button";
import Storage from "../utils/storage";
import { UserRole } from "../types/user";
import * as ImagePicker from "expo-image-picker";
import { manipulateAsync, SaveFormat } from "expo-image-manipulator";

type ProviderProfileScreenNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;

const ProviderProfileScreen = () => {
  const navigation = useNavigation<ProviderProfileScreenNavigationProp>();
  const theme = useTheme();
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [weeklyAvailability, setWeeklyAvailability] = useState({
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: false,
    sunday: false,
  });

  // Verify user role on component mount
  useEffect(() => {
    const verifyUserRole = async () => {
      try {
        // Get user role from both storage and AuthContext
        const userRole = await Storage.getItem("userRole");
        const isAuthenticated = await Storage.getItem("isAuthenticated");

        // Check if user is authenticated and has the provider role
        // Check both the storage value and the AuthContext user role
        const isProvider =
          userRole === "provider" ||
          user?.role === UserRole.PROVIDER ||
          (await Storage.getItem("userRoleEnum")) === UserRole.PROVIDER;

        console.log("Provider Profile access check:", {
          storageRole: userRole,
          contextRole: user?.role,
          isAuthenticated: isAuthenticated,
        });

        // If user is not a provider, redirect to login
        // We're being more lenient with the authentication check since we've seen issues with it
        if (!isProvider) {
          Alert.alert(
            "Access Denied",
            "You must be logged in as a cleaner to access this screen.",
            [
              {
                text: "OK",
                onPress: () => {
                  // Clear any existing auth data
                  Storage.removeItem("userRole");
                  Storage.removeItem("isAuthenticated");

                  // Redirect to shared login screen
                  navigation.reset({
                    index: 0,
                    routes: [{ name: "Login" }],
                  });
                },
              },
            ]
          );
        }
      } catch (error) {
        console.error("Error verifying user role:", error);
      }
    };

    verifyUserRole();
  }, []);

  // Update availability when any day is toggled
  const handleDayToggle = (day: string, _index: number) => {
    const dayKey = day.toLowerCase() as keyof typeof weeklyAvailability;
    const newAvailability = { ...weeklyAvailability };
    newAvailability[dayKey] = !newAvailability[dayKey];
    setWeeklyAvailability(newAvailability);

    // Check if any day is available
    const anyDayAvailable = Object.values(newAvailability).some(
      (value) => value
    );

    // Show warning if all days are turned off
    if (!anyDayAvailable) {
      Alert.alert(
        "Warning",
        "Turning off all weekdays may limit your job opportunities."
      );
    }
  };

  // Provider profile data
  const [profile, setProfile] = useState({
    name: "Mariama Jallow",
    gender: "Female",
    email: "<EMAIL>",
    phone: "+220 7123456",
    experience: "3",
    workingArea: "Serrekunda, Bakau, Banjul",
    description:
      "Professional cleaner with experience in residential and commercial cleaning. Specializing in deep cleaning and sanitization services.",
    services: [
      { id: "1", name: "Regular Cleaning", isOffered: true },
      { id: "2", name: "Deep Cleaning", isOffered: true },
      { id: "3", name: "Window Cleaning", isOffered: true },
      { id: "4", name: "Carpet Cleaning", isOffered: false },
      { id: "5", name: "Post-Construction Cleaning", isOffered: false },
    ],
    tools: {
      hasOwnTools: true,
      toolsList: "Vacuum cleaner, mops, cleaning solutions, microfiber cloths",
    },
    rating: 4.8,
    completedJobs: 42,
    profileImage: "https://randomuser.me/api/portraits/women/44.jpg",
  });

  // State for image picker modal
  const [imagePickerVisible, setImagePickerVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Handle profile image selection
  const handleProfileImagePick = async () => {
    try {
      // Request permission to access the media library
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Denied",
          "We need permission to access your photos to update your profile picture."
        );
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setIsUploading(true);

        try {
          // Resize and compress the image
          const manipResult = await manipulateAsync(
            result.assets[0].uri,
            [{ resize: { width: 400, height: 400 } }],
            { format: SaveFormat.JPEG, compress: 0.8 }
          );

          // In a real app, you would upload the image to your server here
          // For now, we'll just update the local state
          setProfile({
            ...profile,
            profileImage: manipResult.uri,
          });

          // Show success message
          Alert.alert("Success", "Profile picture updated successfully!");
        } catch (error) {
          console.error("Error processing image:", error);
          Alert.alert("Error", "Failed to process the selected image.");
        } finally {
          setIsUploading(false);
          setImagePickerVisible(false);
        }
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert("Error", "Failed to pick an image.");
    }
  };

  // Handle camera capture
  const handleCameraCapture = async () => {
    try {
      // Request permission to access the camera
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Denied",
          "We need permission to access your camera to take a profile picture."
        );
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setIsUploading(true);

        try {
          // Resize and compress the image
          const manipResult = await manipulateAsync(
            result.assets[0].uri,
            [{ resize: { width: 400, height: 400 } }],
            { format: SaveFormat.JPEG, compress: 0.8 }
          );

          // In a real app, you would upload the image to your server here
          // For now, we'll just update the local state
          setProfile({
            ...profile,
            profileImage: manipResult.uri,
          });

          // Show success message
          Alert.alert("Success", "Profile picture updated successfully!");
        } catch (error) {
          console.error("Error processing image:", error);
          Alert.alert("Error", "Failed to process the captured image.");
        } finally {
          setIsUploading(false);
          setImagePickerVisible(false);
        }
      }
    } catch (error) {
      console.error("Error capturing image:", error);
      Alert.alert("Error", "Failed to capture an image.");
    }
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Validate fields
      if (!profile.name.trim()) {
        Alert.alert("Error", "Name cannot be empty");
        return;
      }
      if (!profile.phone.trim()) {
        Alert.alert("Error", "Phone number cannot be empty");
        return;
      }
      if (!profile.email.trim()) {
        Alert.alert("Error", "Email cannot be empty");
        return;
      }

      // Save changes
      Alert.alert("Success", "Profile updated successfully!");
    }
    setIsEditing(!isEditing);
  };

  const handleServiceToggle = (serviceId: string) => {
    setProfile({
      ...profile,
      services: profile.services.map((service) =>
        service.id === serviceId
          ? { ...service, isOffered: !service.isOffered }
          : service
      ),
    });
  };

  const handleToolsToggle = () => {
    setProfile({
      ...profile,
      tools: {
        ...profile.tools,
        hasOwnTools: !profile.tools.hasOwnTools,
      },
    });
  };

  const handleLogout = async () => {
    try {
      // Clear authentication data
      await Storage.removeItem("userRole");
      await Storage.removeItem("isAuthenticated");

      // Navigate to onboarding
      navigation.reset({
        index: 0,
        routes: [{ name: "Login" }],
      });
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      flex: 1,
    },
    content: {
      padding: theme.spacing.xs,
      paddingBottom: theme.spacing.lg,
    },
    sectionHeader: {
      alignItems: "center",
      flexDirection: "row",
      marginBottom: theme.spacing.sm,
      marginTop: theme.spacing.lg,
    },
    sectionIcon: {
      alignItems: "center",
      backgroundColor: `${theme.colors.primary}15`,
      borderRadius: theme.borderRadius.sm,
      height: 32,
      justifyContent: "center",
      marginRight: theme.spacing.sm,
      width: 32,
    },
    profileCard: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.lg,
      marginBottom: theme.spacing.lg,
      marginHorizontal: theme.spacing.xs,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 4,
      borderWidth: 1,
      borderColor: `${theme.colors.border}20`,
      overflow: 'hidden',
      width: '95%',
      alignSelf: 'center',
    },
    profileContent: {
      alignItems: "center",
      padding: theme.spacing.md,
      paddingTop: theme.spacing.lg,
      paddingBottom: theme.spacing.lg,
      width: '100%',
    },
    avatarContainer: {
      marginBottom: theme.spacing.md,
      position: "relative",
      alignSelf: 'center',
    },
    avatar: {
      borderColor: theme.colors.primary,
      borderRadius: 50,
      borderWidth: 3,
      height: 100,
      width: 100,
      backgroundColor: theme.colors.card,
    },
    cameraButton: {
      position: "absolute",
      bottom: 0,
      right: 0,
      backgroundColor: theme.colors.primary,
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: "center",
      alignItems: "center",
    },
    name: {
      fontSize: theme.fontSizes.xl,
      fontWeight: "700",
      marginBottom: 4,
    },
    nameInput: {
      fontSize: theme.fontSizes.xl,
      fontWeight: "700",
      textAlign: "center",
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.primary,
      marginBottom: 4,
      paddingBottom: 4,
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    ratingText: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginLeft: 4,
    },
    jobsCompleted: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    sectionTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.sm,
      color: theme.colors.text,
    },
    sectionCard: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.lg,
      marginBottom: theme.spacing.lg,
      marginHorizontal: theme.spacing.xs,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 4,
      borderWidth: 1,
      borderColor: `${theme.colors.border}20`,
      width: '95%',
      alignSelf: 'center',
    },
    sectionContent: {
      padding: theme.spacing.lg,
      width: '100%',
    },
    infoRow: {
      flexDirection: "row",
      marginBottom: theme.spacing.md,
      alignItems: "center",
      paddingBottom: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: `${theme.colors.border}50`,
    },
    infoLabel: {
      width: 120,
      fontSize: theme.fontSizes.sm,
      fontWeight: "600",
      color: theme.colors.textLight,
    },
    infoValue: {
      flex: 1,
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
      fontWeight: "500",
    },
    infoValueContainer: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    editButton: {
      padding: 4,
    },
    infoInput: { 
      borderWidth: 1,
      flex: 1,
      fontSize: theme.fontSizes.sm,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      paddingVertical: 8,
      paddingHorizontal: 12,
      backgroundColor: theme.colors.card,
      color: theme.colors.text,
    },
    editButtonsContainer: {
      flexDirection: "row",
      justifyContent: "flex-end",
      marginTop: theme.spacing.md,
    },
    actionButton: {
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: theme.borderRadius.sm,
      marginLeft: theme.spacing.sm,
    },
    cancelButton: {
      backgroundColor: theme.colors.border,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
    },
    cancelButtonText: {
      color: theme.colors.text,
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
    },
    saveButtonText: {
      color: "white",
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
    },
    descriptionText: {
      fontSize: theme.fontSizes.sm,
      lineHeight: 22,
      marginBottom: theme.spacing.md,
      color: theme.colors.text,
    },
    descriptionInput: {
      fontSize: theme.fontSizes.sm,
      lineHeight: 22,
      marginBottom: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      height: 120,
      textAlignVertical: "top",
      backgroundColor: theme.colors.card,
      color: theme.colors.text,
    },
    serviceItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.xs,
      borderBottomWidth: 1,
      borderBottomColor: `${theme.colors.border}50`,
      marginHorizontal: theme.spacing.xs,
    },
    serviceText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      color: theme.colors.text,
    },
    toolsRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    toolsLabel: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
    },
    toolsList: {
      fontSize: theme.fontSizes.sm,
      marginTop: theme.spacing.xs,
    },
    toolsInput: {
      fontSize: theme.fontSizes.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.sm,
      marginTop: theme.spacing.xs,
    },
    logoutButton: {
      marginTop: theme.spacing.md,
    },
    versionContainer: {
      alignItems: "center",
      marginTop: theme.spacing.lg,
      marginBottom: theme.spacing.xl,
    },
    versionText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
    },
    copyrightText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      marginTop: 4,
    },
    menuItem: {
      borderRadius: theme.borderRadius.md,
      overflow: "hidden",
    },
    menuItemContent: {
      flexDirection: "row",
      alignItems: "center",
      padding: theme.spacing.md,
    },
    menuIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.md,
    },
    menuItemTextContainer: {
      flex: 1,
    },
    menuItemTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
    },
    menuItemDescription: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginTop: 2,
    },
    warningContainer: {
      flexDirection: "row",
      padding: theme.spacing.md,
      alignItems: "flex-start",
    },
    warningIcon: {
      marginRight: theme.spacing.sm,
      marginTop: 2,
    },
    warningText: {
      flex: 1,
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
      lineHeight: 20,
    },
    availabilityHeader: {
      marginBottom: theme.spacing.md,
    },
    availabilityTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
    },
    availabilitySubtitle: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      marginTop: 2,
    },
    daysContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
      marginBottom: theme.spacing.sm,
    },
    dayToggleContainer: {
      width: "30%",
      flexDirection: "column",
      alignItems: "center",
      marginBottom: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.sm,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    dayText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "600",
      marginBottom: theme.spacing.xs,
      color: theme.colors.text,
    },
    dayToggle: {
      transform: [{ scaleX: 0.7 }, { scaleY: 0.7 }],
    },
    availabilityNote: {
      paddingTop: theme.spacing.sm,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    availabilityNoteText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      textAlign: "center",
      fontStyle: "italic",
    },
  });

  // Calculate profile completion percentage
  const calculateProfileCompletion = () => {
    let completedItems = 0;
    const totalItems = 7; // Total number of profile sections to complete

    if (profile.name) completedItems++;
    if (profile.email) completedItems++;
    if (profile.phone) completedItems++;
    if (profile.experience) completedItems++;
    if (profile.workingArea) completedItems++;
    if (profile.description) completedItems++;
    if (profile.services.some((s) => s.isOffered)) completedItems++;

    return Math.round((completedItems / totalItems) * 100);
  };

  return (
    <View style={styles.container}>
      <Header
        title={isEditing ? "Edit Profile" : "Provider Profile"}
        showBackButton
        rightComponent={
          <TouchableOpacity onPress={handleEditToggle} activeOpacity={0.7}>
            <Feather
              name={isEditing ? "check" : "edit-2"}
              size={24}
              color="white"
            />
          </TouchableOpacity>
        }
      />

      {/* Image Picker Modal */}
      <Modal
        visible={imagePickerVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setImagePickerVisible(false)}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: "rgba(0,0,0,0.5)",
            justifyContent: "center",
            alignItems: "center",
          }}
          activeOpacity={1}
          onPress={() => setImagePickerVisible(false)}
        >
          <View
            style={{
              width: "80%",
              backgroundColor: theme.colors.card,
              borderRadius: theme.borderRadius.lg,
              padding: theme.spacing.lg,
              alignItems: "center",
              elevation: 5,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
            }}
          >
            <Text
              style={{
                fontSize: theme.fontSizes.lg,
                fontWeight: "700",
                marginBottom: theme.spacing.md,
                color: theme.colors.text,
                textAlign: "center",
              }}
            >
              Update Profile Picture
            </Text>

            <TouchableOpacity
              style={{
                flexDirection: "row",
                alignItems: "center",
                padding: theme.spacing.md,
                borderWidth: 1,
                borderColor: theme.colors.border,
                borderRadius: theme.borderRadius.md,
                marginBottom: theme.spacing.md,
                width: "100%",
              }}
              onPress={handleCameraCapture}
            >
              <Feather
                name="camera"
                size={24}
                color={theme.colors.primary}
                style={{ marginRight: theme.spacing.md }}
              />
              <Text
                style={{
                  fontSize: theme.fontSizes.md,
                  color: theme.colors.text,
                }}
              >
                Take Photo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                flexDirection: "row",
                alignItems: "center",
                padding: theme.spacing.md,
                borderWidth: 1,
                borderColor: theme.colors.border,
                borderRadius: theme.borderRadius.md,
                marginBottom: theme.spacing.lg,
                width: "100%",
              }}
              onPress={handleProfileImagePick}
            >
              <Feather
                name="image"
                size={24}
                color={theme.colors.primary}
                style={{ marginRight: theme.spacing.md }}
              />
              <Text
                style={{
                  fontSize: theme.fontSizes.md,
                  color: theme.colors.text,
                }}
              >
                Choose from Gallery
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                padding: theme.spacing.sm,
                borderRadius: theme.borderRadius.md,
              }}
              onPress={() => setImagePickerVisible(false)}
            >
              <Text
                style={{
                  fontSize: theme.fontSizes.md,
                  color: theme.colors.notification,
                }}
              >
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.profileCard}>
          <View style={styles.profileContent}>
            <View style={styles.avatarContainer}>
              <View
                style={{
                  width: 110,
                  height: 110,
                  borderRadius: 55,
                  backgroundColor: theme.colors.card,
                  justifyContent: "center",
                  alignItems: "center",
                  shadowColor: theme.colors.text,
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.15,
                  shadowRadius: 8,
                  elevation: 5,
                }}
              >
                <Image
                  source={{ uri: profile.profileImage }}
                  style={styles.avatar}
                  resizeMode="cover"
                />
              </View>

              {isEditing && (
                <TouchableOpacity
                  style={styles.cameraButton}
                  onPress={() => setImagePickerVisible(true)}
                  activeOpacity={0.8}
                >
                  <Feather name="camera" size={18} color="white" />
                </TouchableOpacity>
              )}

              {isUploading && (
                <View
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: "rgba(255,255,255,0.7)",
                    borderRadius: 55,
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <ActivityIndicator
                    size="large"
                    color={theme.colors.primary}
                  />
                </View>
              )}
            </View>

            {isEditing ? (
              <TextInput
                style={styles.nameInput}
                value={profile.name}
                onChangeText={(text) => setProfile({ ...profile, name: text })}
              />
            ) : (
              <Text style={styles.name}>{profile.name}</Text>
            )}

            <View style={styles.ratingContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Feather
                  key={star}
                  name="star"
                  size={16}
                  color={
                    star <= Math.round(profile.rating)
                      ? theme.colors.warning
                      : theme.colors.border
                  }
                />
              ))}
              <Text style={styles.ratingText}>{profile.rating}</Text>
            </View>

            <Text style={styles.jobsCompleted}>
              {profile.completedJobs} jobs completed
            </Text>

            {/* Profile Completion Indicator */}
            <View
              style={{
                alignItems: "center",
                backgroundColor: `${theme.colors.primary}10`,
                borderRadius: theme.borderRadius.md,
                flexDirection: "row",
                marginBottom: theme.spacing.md,
                marginTop: theme.spacing.sm,
                padding: theme.spacing.sm,
                width: "90%",
              }}
            >
              <Feather
                name="user-check"
                size={18}
                color={theme.colors.primary}
              />
              <Text
                style={{
                  color: theme.colors.primary,
                  flex: 1,
                  fontSize: theme.fontSizes.sm,
                  fontWeight: "600",
                  marginLeft: theme.spacing.xs,
                }}
              >
                Profile Completion: {calculateProfileCompletion()}%
              </Text>
            </View>
            <View
              style={{
                backgroundColor: `${theme.colors.primary}20`,
                borderRadius: theme.borderRadius.round,
                height: 6,
                marginTop: -theme.spacing.xs,
                marginBottom: theme.spacing.sm,
                overflow: "hidden",
                width: "90%",
              }}
            >
              <View
                style={{
                  backgroundColor: theme.colors.primary,
                  borderRadius: theme.borderRadius.round,
                  height: "100%",
                  width: `${calculateProfileCompletion()}%`,
                }}
              />
            </View>
          </View>
        </View>

        {/* Personal Information */}
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIcon}>
            <Feather name="user" size={18} color={theme.colors.primary} />
          </View>
          <Text style={styles.sectionTitle}>Personal Information</Text>
        </View>

        <View style={styles.sectionCard}>
          <View style={styles.sectionContent}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Gender</Text>
              {isEditing ? (
                <TextInput
                  style={styles.infoInput}
                  value={profile.gender}
                  onChangeText={(text) =>
                    setProfile({ ...profile, gender: text })
                  }
                />
              ) : (
                <Text style={styles.infoValue}>{profile.gender}</Text>
              )}
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Email</Text>
              {isEditing ? (
                <TextInput
                  style={styles.infoInput}
                  value={profile.email}
                  onChangeText={(text) =>
                    setProfile({ ...profile, email: text })
                  }
                  keyboardType="email-address"
                />
              ) : (
                <Text style={styles.infoValue}>{profile.email}</Text>
              )}
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Phone</Text>
              {isEditing ? (
                <TextInput
                  style={styles.infoInput}
                  value={profile.phone}
                  onChangeText={(text) =>
                    setProfile({ ...profile, phone: text })
                  }
                  keyboardType="phone-pad"
                />
              ) : (
                <Text style={styles.infoValue}>{profile.phone}</Text>
              )}
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Experience</Text>
              {isEditing ? (
                <TextInput
                  style={styles.infoInput}
                  value={profile.experience}
                  onChangeText={(text) =>
                    setProfile({ ...profile, experience: text })
                  }
                  keyboardType="number-pad"
                />
              ) : (
                <Text style={styles.infoValue}>{profile.experience} years</Text>
              )}
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Working Area</Text>
              {isEditing ? (
                <TextInput
                  style={styles.infoInput}
                  value={profile.workingArea}
                  onChangeText={(text) =>
                    setProfile({ ...profile, workingArea: text })
                  }
                />
              ) : (
                <Text style={styles.infoValue}>{profile.workingArea}</Text>
              )}
            </View>

            {isEditing && (
              <View style={styles.editButtonsContainer}>
                <TouchableOpacity
                  style={[styles.actionButton, styles.cancelButton]}
                  onPress={() => setIsEditing(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.actionButton, styles.saveButton]}
                  onPress={handleEditToggle}
                >
                  <Text style={styles.saveButtonText}>Save</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>

        {/* Description */}
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIcon}>
            <Feather name="info" size={18} color={theme.colors.primary} />
          </View>
          <Text style={styles.sectionTitle}>About Me</Text>
        </View>

        <View style={styles.sectionCard}>
          <View style={styles.sectionContent}>
            {isEditing ? (
              <TextInput
                style={styles.descriptionInput}
                value={profile.description}
                onChangeText={(text) =>
                  setProfile({ ...profile, description: text })
                }
                multiline
              />
            ) : (
              <Text style={styles.descriptionText}>{profile.description}</Text>
            )}
          </View>
        </View>

        {/* Services Offered */}
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIcon}>
            <Feather name="briefcase" size={18} color={theme.colors.primary} />
          </View>
          <Text style={styles.sectionTitle}>Services Offered</Text>
        </View>

        <View style={styles.sectionCard}>
          <View style={styles.sectionContent}>
            {profile.services.map((service) => (
              <View key={service.id} style={styles.serviceItem}>
                <Text style={styles.serviceText}>{service.name}</Text>
                {isEditing ? (
                  <TouchableOpacity
                    onPress={() => handleServiceToggle(service.id)}
                  >
                    <Feather
                      name={service.isOffered ? "check-square" : "square"}
                      size={20}
                      color={
                        service.isOffered
                          ? theme.colors.primary
                          : theme.colors.textLight
                      }
                    />
                  </TouchableOpacity>
                ) : (
                  service.isOffered && (
                    <Feather
                      name="check"
                      size={20}
                      color={theme.colors.primary}
                    />
                  )
                )}
              </View>
            ))}
          </View>
        </View>

        {/* Tools & Equipment */}
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIcon}>
            <Feather name="tool" size={18} color={theme.colors.primary} />
          </View>
          <Text style={styles.sectionTitle}>Tools & Equipment</Text>
        </View>

        <View style={styles.sectionCard}>
          <View style={styles.sectionContent}>
            <View style={styles.toolsRow}>
              <Text style={styles.toolsLabel}>
                I have my own cleaning tools
              </Text>
              {isEditing ? (
                <TouchableOpacity onPress={handleToolsToggle}>
                  <Feather
                    name={profile.tools.hasOwnTools ? "check-square" : "square"}
                    size={20}
                    color={
                      profile.tools.hasOwnTools
                        ? theme.colors.primary
                        : theme.colors.textLight
                    }
                  />
                </TouchableOpacity>
              ) : (
                <Feather
                  name={profile.tools.hasOwnTools ? "check" : "x"}
                  size={20}
                  color={
                    profile.tools.hasOwnTools
                      ? theme.colors.primary
                      : theme.colors.notification
                  }
                />
              )}
            </View>

            {profile.tools.hasOwnTools && (
              <>
                <Text style={styles.toolsLabel}>Tools I have:</Text>
                {isEditing ? (
                  <TextInput
                    style={styles.toolsInput}
                    value={profile.tools.toolsList}
                    onChangeText={(text) =>
                      setProfile({
                        ...profile,
                        tools: { ...profile.tools, toolsList: text },
                      })
                    }
                    multiline
                  />
                ) : (
                  <Text style={styles.toolsList}>
                    {profile.tools.toolsList}
                  </Text>
                )}
              </>
            )}
          </View>
        </View>

        {/* Availability Management */}
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIcon}>
            <Feather name="calendar" size={18} color={theme.colors.primary} />
          </View>
          <Text style={styles.sectionTitle}>Availability</Text>
        </View>

        <View style={styles.sectionCard}>
          <View style={styles.sectionContent}>
            <View style={styles.availabilityHeader}>
              <Text style={styles.availabilityTitle}>Weekly Availability</Text>
              <Text style={styles.availabilitySubtitle}>
                Default hours: 9:00 AM - 6:00 PM
              </Text>
            </View>

            {/* Weekly Availability Toggles */}
            <View style={styles.daysContainer}>
              {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map(
                (displayDay, index) => {
                  const days = [
                    "monday",
                    "tuesday",
                    "wednesday",
                    "thursday",
                    "friday",
                    "saturday",
                    "sunday",
                  ];
                  const day = days[index];
                  // day is already lowercase

                  return (
                    <View key={day} style={styles.dayToggleContainer}>
                      <Text style={styles.dayText}>{displayDay}</Text>
                      <Switch
                        style={styles.dayToggle}
                        value={
                          weeklyAvailability[
                            day as keyof typeof weeklyAvailability
                          ]
                        }
                        onValueChange={() => handleDayToggle(day, index)}
                        trackColor={{
                          false: theme.colors.border,
                          true: theme.colors.primary,
                        }}
                        thumbColor="white"
                      />
                    </View>
                  );
                }
              )}
            </View>

            {/* Note about availability */}
            <View style={styles.availabilityNote}>
              <Text style={styles.availabilityNoteText}>
                CleanConnect will only offer you bookings during your available
                days.
              </Text>
            </View>
          </View>
        </View>

        {/* Logout Button */}
        <Button
          title="Log Out"
          variant="outline"
          icon={
            <Feather
              name="log-out"
              size={16}
              color={theme.colors.notification}
            />
          }
          style={styles.logoutButton}
          textStyle={{ color: theme.colors.notification }}
          onPress={handleLogout}
        />

        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>CleanConnect v1.0.0</Text>
          <Text style={styles.copyrightText}>
            © 2024 CleanConnect. All rights reserved.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

export default ProviderProfileScreen;
