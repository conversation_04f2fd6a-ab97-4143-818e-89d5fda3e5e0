"use client"

import React, { useState, useRef, useEffect } from "react"
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
  Keyboard,
} from "react-native"
import { useNavigation, useRoute } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Header from "../components/Header"

type CustomerChatScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

interface Message {
  id: string
  text: string
  sender: "user" | "other"
  timestamp: Date
}

interface QuickReply {
  id: string
  text: string
}

const CustomerChatScreen = () => {
  const navigation = useNavigation<CustomerChatScreenNavigationProp>()
  const route = useRoute()
  const theme = useTheme()
  const flatListRef = useRef<FlatList>(null)
  
  // Get params from route
  const { providerId, providerName, providerImage } = route.params as {
    providerId: string
    providerName: string
    providerImage: string
  }
  
  // State for messages
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "system-1",
      text: "Welcome to the chat. You can communicate with your service provider here.",
      sender: "other",
      timestamp: new Date(Date.now() - 60000 * 10),
    },
  ])
  
  // State for input text
  const [inputText, setInputText] = useState("")
  
  // State for keyboard visibility
  const [isKeyboardVisible, setKeyboardVisible] = useState(false)
  
  // Quick reply options for customers
  const quickReplies: QuickReply[] = [
    { id: "qr1", text: "When will you arrive?" },
    { id: "qr2", text: "Please call me when you arrive" },
    { id: "qr3", text: "I need to reschedule" },
    { id: "qr4", text: "I have special instructions" },
    { id: "qr5", text: "Thank you for your service" },
  ]
  
  // Handle keyboard show/hide events
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true)
        scrollToBottom()
      }
    )
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false)
      }
    )

    return () => {
      keyboardDidShowListener.remove()
      keyboardDidHideListener.remove()
    }
  }, [])
  
  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom()
  }, [messages])
  
  // Function to scroll to bottom of chat
  const scrollToBottom = () => {
    if (flatListRef.current && messages.length > 0) {
      flatListRef.current.scrollToEnd({ animated: true })
    }
  }
  
  // Function to send a message
  const sendMessage = (text: string) => {
    if (text.trim() === "") return
    
    const newMessage: Message = {
      id: Date.now().toString(),
      text,
      sender: "user",
      timestamp: new Date(),
    }
    
    setMessages([...messages, newMessage])
    setInputText("")
    
    // Simulate a reply after a short delay (in a real app, this would be from the server)
    if (Math.random() > 0.7) {
      setTimeout(() => {
        const replies = [
          "I'll be there in about 15 minutes.",
          "I'm on my way now.",
          "I'll call you when I arrive.",
          "No problem, I'll take care of that.",
        ]
        
        const replyMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: replies[Math.floor(Math.random() * replies.length)],
          sender: "other",
          timestamp: new Date(),
        }
        
        setMessages((prevMessages) => [...prevMessages, replyMessage])
      }, 1000 + Math.random() * 2000)
    }
  }
  
  // Function to handle quick reply selection
  const handleQuickReply = (text: string) => {
    sendMessage(text)
  }
  
  // Function to format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }
  
  // Function to format date for message groups
  const formatDate = (date: Date) => {
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    
    if (date.toDateString() === today.toDateString()) {
      return "Today"
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday"
    } else {
      return date.toLocaleDateString()
    }
  }
  
  // Function to render a message item
  const renderMessageItem = ({ item, index }: { item: Message; index: number }) => {
    const isUser = item.sender === "user"
    const showTimestamp = index === messages.length - 1 || 
                          messages[index + 1].sender !== item.sender ||
                          new Date(messages[index + 1].timestamp).getTime() - new Date(item.timestamp).getTime() > 5 * 60000
    
    // Check if we need to show a date separator
    const showDateSeparator = index === 0 || 
                             formatDate(new Date(messages[index - 1].timestamp)) !== formatDate(new Date(item.timestamp))
    
    return (
      <>
        {showDateSeparator && (
          <View style={styles.dateSeparator}>
            <Text style={styles.dateSeparatorText}>{formatDate(new Date(item.timestamp))}</Text>
          </View>
        )}
        <View style={[styles.messageContainer, isUser ? styles.userMessageContainer : styles.otherMessageContainer]}>
          {!isUser && (
            <Image source={{ uri: providerImage }} style={styles.avatar} />
          )}
          <View style={[styles.messageBubble, isUser ? styles.userMessageBubble : styles.otherMessageBubble]}>
            <Text style={[styles.messageText, isUser ? styles.userMessageText : styles.otherMessageText]}>
              {item.text}
            </Text>
            {showTimestamp && (
              <Text style={[styles.messageTime, isUser ? styles.userMessageTime : styles.otherMessageTime]}>
                {formatTime(new Date(item.timestamp))}
              </Text>
            )}
          </View>
        </View>
      </>
    )
  }
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    chatContainer: {
      flex: 1,
      padding: theme.spacing.sm,
    },
    providerInfo: {
      flexDirection: "row",
      alignItems: "center",
      padding: theme.spacing.sm,
      backgroundColor: "white",
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    providerImage: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: theme.spacing.sm,
    },
    providerName: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
    },
    providerStatus: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    messagesList: {
      flex: 1,
      paddingHorizontal: theme.spacing.sm,
    },
    dateSeparator: {
      alignItems: "center",
      marginVertical: theme.spacing.sm,
    },
    dateSeparatorText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      backgroundColor: "rgba(0,0,0,0.05)",
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 4,
      borderRadius: 12,
    },
    messageContainer: {
      flexDirection: "row",
      marginBottom: theme.spacing.sm,
      maxWidth: "80%",
    },
    userMessageContainer: {
      alignSelf: "flex-end",
    },
    otherMessageContainer: {
      alignSelf: "flex-start",
    },
    avatar: {
      width: 30,
      height: 30,
      borderRadius: 15,
      marginRight: 8,
      alignSelf: "flex-end",
    },
    messageBubble: {
      padding: theme.spacing.sm,
      borderRadius: 16,
      maxWidth: "100%",
    },
    userMessageBubble: {
      backgroundColor: theme.colors.primary,
      borderBottomRightRadius: 4,
    },
    otherMessageBubble: {
      backgroundColor: "white",
      borderBottomLeftRadius: 4,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    messageText: {
      fontSize: theme.fontSizes.md,
    },
    userMessageText: {
      color: "white",
    },
    otherMessageText: {
      color: theme.colors.text,
    },
    messageTime: {
      fontSize: theme.fontSizes.xs,
      marginTop: 4,
      alignSelf: "flex-end",
    },
    userMessageTime: {
      color: "rgba(255,255,255,0.8)",
    },
    otherMessageTime: {
      color: theme.colors.textLight,
    },
    inputContainer: {
      flexDirection: "row",
      padding: theme.spacing.sm,
      backgroundColor: "white",
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      alignItems: "center",
    },
    input: {
      flex: 1,
      backgroundColor: theme.colors.background,
      borderRadius: 20,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      marginRight: theme.spacing.sm,
      fontSize: theme.fontSizes.md,
    },
    sendButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary,
      justifyContent: "center",
      alignItems: "center",
    },
    quickRepliesContainer: {
      padding: theme.spacing.sm,
      backgroundColor: "white",
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    quickRepliesTitle: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      marginBottom: theme.spacing.sm,
      color: theme.colors.textLight,
    },
    quickRepliesScroll: {
      flexDirection: "row",
    },
    quickReplyButton: {
      backgroundColor: `${theme.colors.primary}10`,
      borderRadius: 16,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 8,
      marginRight: theme.spacing.sm,
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    quickReplyText: {
      color: theme.colors.primary,
      fontSize: theme.fontSizes.sm,
    },
  })
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
    >
      <Header 
        title="Chat" 
        showBackButton 
      />
      
      <View style={styles.providerInfo}>
        <Image source={{ uri: providerImage }} style={styles.providerImage} />
        <View>
          <Text style={styles.providerName}>{providerName}</Text>
          <Text style={styles.providerStatus}>Online</Text>
        </View>
      </View>
      
      <View style={styles.chatContainer}>
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessageItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingVertical: theme.spacing.md }}
          showsVerticalScrollIndicator={false}
        />
      </View>
      
      {!isKeyboardVisible && (
        <View style={styles.quickRepliesContainer}>
          <Text style={styles.quickRepliesTitle}>Quick Replies</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.quickRepliesScroll}
          >
            {quickReplies.map((reply) => (
              <TouchableOpacity
                key={reply.id}
                style={styles.quickReplyButton}
                onPress={() => handleQuickReply(reply.text)}
              >
                <Text style={styles.quickReplyText}>{reply.text}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
      
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          placeholder="Type a message..."
          value={inputText}
          onChangeText={setInputText}
          multiline
        />
        <TouchableOpacity 
          style={styles.sendButton}
          onPress={() => sendMessage(inputText)}
          disabled={inputText.trim() === ""}
        >
          <Feather name="send" size={20} color="white" />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  )
}

export default CustomerChatScreen
