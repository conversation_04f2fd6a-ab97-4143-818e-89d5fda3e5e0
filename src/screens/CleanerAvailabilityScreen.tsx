"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch } from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Header from "../components/Header"
import Card from "../components/Card"
import Button from "../components/Button"
import Badge from "../components/Badge"

type CleanerAvailabilityScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

const CleanerAvailabilityScreen = () => {
  const navigation = useNavigation<CleanerAvailabilityScreenNavigationProp>()
  const theme = useTheme()
  const [activeTab, setActiveTab] = useState("calendar")
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [availableOnSelectedDate, setAvailableOnSelectedDate] = useState(true)

  // Dummy availability data
  const weeklyAvailability = {
    monday: { available: true, slots: ["9:00 AM - 12:00 PM", "2:00 PM - 6:00 PM"] },
    tuesday: { available: true, slots: ["9:00 AM - 12:00 PM", "2:00 PM - 6:00 PM"] },
    wednesday: { available: true, slots: ["9:00 AM - 12:00 PM", "2:00 PM - 6:00 PM"] },
    thursday: { available: true, slots: ["9:00 AM - 12:00 PM", "2:00 PM - 6:00 PM"] },
    friday: { available: true, slots: ["9:00 AM - 12:00 PM", "2:00 PM - 6:00 PM"] },
    saturday: { available: true, slots: ["10:00 AM - 2:00 PM"] },
    sunday: { available: false, slots: [] },
  }

  // Generate calendar days
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay()
  }

  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear()
    const month = currentMonth.getMonth()

    const daysInMonth = getDaysInMonth(year, month)
    const firstDay = getFirstDayOfMonth(year, month)

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push({ day: 0, date: null })
    }

    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month, i)
      days.push({
        day: i,
        date,
        isToday: new Date().toDateString() === date.toDateString(),
        isSelected: selectedDate && selectedDate.toDateString() === date.toDateString(),
        hasBooking: [5, 12, 18, 25].includes(i), // Dummy booking data
      })
    }

    return days
  }

  const calendarDays = generateCalendarDays()

  const prevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1))
  }

  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1))
  }

  const formatMonth = (date: Date) => {
    return date.toLocaleDateString("en-US", { month: "long", year: "numeric" })
  }

  const handleDateClick = (date: Date) => {
    setSelectedDate(date)
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.md,
    },
    tabsContainer: {
      flexDirection: "row",
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
      backgroundColor: "white",
      overflow: "hidden",
    },
    tab: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      alignItems: "center",
    },
    activeTab: {
      backgroundColor: theme.colors.primary,
    },
    tabText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
    },
    activeTabText: {
      color: "white",
      fontWeight: "600",
    },
    monthNavigation: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    monthText: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "500",
    },
    calendarCard: {
      marginBottom: theme.spacing.md,
    },
    calendarContent: {
      padding: theme.spacing.md,
    },
    weekdaysRow: {
      flexDirection: "row",
      marginBottom: theme.spacing.sm,
    },
    weekdayCell: {
      flex: 1,
      alignItems: "center",
    },
    weekdayText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
    },
    calendarGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
    },
    dayCell: {
      width: `${100 / 7}%`,
      aspectRatio: 1,
      padding: 2,
      justifyContent: "center",
      alignItems: "center",
    },
    dayContent: {
      width: "100%",
      height: "100%",
      justifyContent: "center",
      alignItems: "center",
      borderRadius: theme.borderRadius.sm,
    },
    todayContent: {
      backgroundColor: `${theme.colors.accent}30`,
    },
    selectedContent: {
      backgroundColor: theme.colors.primary,
    },
    dayText: {
      fontSize: theme.fontSizes.sm,
    },
    selectedDayText: {
      color: "white",
      fontWeight: "600",
    },
    bookingIndicator: {
      width: 4,
      height: 4,
      borderRadius: 2,
      backgroundColor: theme.colors.warning,
      position: "absolute",
      bottom: 4,
    },
    selectedBookingIndicator: {
      backgroundColor: "white",
    },
    dateDetailsCard: {
      marginBottom: theme.spacing.md,
    },
    dateDetailsContent: {
      padding: theme.spacing.md,
    },
    dateDetailsHeader: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    availabilityRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    availabilityLabel: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
    },
    timeSlotsTitle: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      marginBottom: theme.spacing.sm,
    },
    timeSlotsGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginHorizontal: -theme.spacing.xs,
    },
    timeSlotContainer: {
      width: "50%",
      padding: theme.spacing.xs,
    },
    timeSlotContent: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.sm,
    },
    timeSlotText: {
      fontSize: theme.fontSizes.sm,
      marginLeft: theme.spacing.sm,
      flex: 1,
    },
    bookingsTitle: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.sm,
    },
    bookingItem: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
    },
    bookingItemHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: theme.spacing.sm,
    },
    bookingItemTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
    },
    bookingItemClient: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    bookingItemTime: {
      flexDirection: "row",
      alignItems: "center",
    },
    bookingItemTimeText: {
      fontSize: theme.fontSizes.sm,
      marginLeft: theme.spacing.xs,
    },
    noBookingsText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    saveButton: {
      marginTop: theme.spacing.md,
    },
    weeklyScheduleCard: {
      marginBottom: theme.spacing.md,
    },
    weeklyScheduleContent: {
      padding: theme.spacing.md,
    },
    weeklyScheduleTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    dayScheduleItem: {
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      paddingBottom: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    lastDayScheduleItem: {
      borderBottomWidth: 0,
      paddingBottom: 0,
      marginBottom: 0,
    },
    dayScheduleHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    dayName: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
      textTransform: "capitalize",
    },
    timeSlotsList: {
      marginLeft: theme.spacing.sm,
    },
    timeSlotItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    timeSlotItemText: {
      fontSize: theme.fontSizes.sm,
      marginLeft: theme.spacing.sm,
    },
    addTimeSlotButton: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: theme.spacing.sm,
      marginLeft: theme.spacing.sm,
    },
    addTimeSlotText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.primary,
      marginLeft: theme.spacing.xs,
    },
  })

  return (
    <View style={styles.container}>
      <Header title="Manage Availability" showBackButton />

      <View style={styles.content}>
        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "calendar" && styles.activeTab]}
            onPress={() => setActiveTab("calendar")}
          >
            <Text style={[styles.tabText, activeTab === "calendar" && styles.activeTabText]}>Calendar</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "weekly" && styles.activeTab]}
            onPress={() => setActiveTab("weekly")}
          >
            <Text style={[styles.tabText, activeTab === "weekly" && styles.activeTabText]}>Weekly Schedule</Text>
          </TouchableOpacity>
        </View>

        <ScrollView showsVerticalScrollIndicator={false}>
          {activeTab === "calendar" ? (
            <>
              {/* Month Navigation */}
              <View style={styles.monthNavigation}>
                <TouchableOpacity onPress={prevMonth}>
                  <Feather name="chevron-left" size={24} color={theme.colors.text} />
                </TouchableOpacity>
                <Text style={styles.monthText}>{formatMonth(currentMonth)}</Text>
                <TouchableOpacity onPress={nextMonth}>
                  <Feather name="chevron-right" size={24} color={theme.colors.text} />
                </TouchableOpacity>
              </View>

              {/* Calendar */}
              <Card style={styles.calendarCard}>
                <View style={styles.calendarContent}>
                  {/* Weekday headers */}
                  <View style={styles.weekdaysRow}>
                    {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                      <View key={day} style={styles.weekdayCell}>
                        <Text style={styles.weekdayText}>{day}</Text>
                      </View>
                    ))}
                  </View>

                  {/* Calendar grid */}
                  <View style={styles.calendarGrid}>
                    {calendarDays.map((day, index) => (
                      <View key={index} style={styles.dayCell}>
                        {day.date ? (
                          <TouchableOpacity
                            style={[
                              styles.dayContent,
                              day.isToday && styles.todayContent,
                              day.isSelected && styles.selectedContent,
                            ]}
                            onPress={() => day.date && handleDateClick(day.date)}
                          >
                            <Text style={[styles.dayText, day.isSelected && styles.selectedDayText]}>{day.day}</Text>
                            {day.hasBooking && (
                              <View
                                style={[styles.bookingIndicator, day.isSelected && styles.selectedBookingIndicator]}
                              />
                            )}
                          </TouchableOpacity>
                        ) : (
                          <View />
                        )}
                      </View>
                    ))}
                  </View>
                </View>
              </Card>

              {/* Selected Date Details */}
              {selectedDate && (
                <Card style={styles.dateDetailsCard}>
                  <View style={styles.dateDetailsContent}>
                    <Text style={styles.dateDetailsHeader}>
                      {selectedDate.toLocaleDateString("en-US", {
                        weekday: "long",
                        month: "long",
                        day: "numeric",
                      })}
                    </Text>

                    <View style={styles.availabilityRow}>
                      <Text style={styles.availabilityLabel}>Available for bookings</Text>
                      <Switch
                        value={availableOnSelectedDate}
                        onValueChange={setAvailableOnSelectedDate}
                        trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                        thumbColor="white"
                      />
                    </View>

                    {availableOnSelectedDate && (
                      <View>
                        <Text style={styles.timeSlotsTitle}>Time Slots</Text>
                        <View style={styles.timeSlotsGrid}>
                          <View style={styles.timeSlotContainer}>
                            <View style={styles.timeSlotContent}>
                              <Switch
                                value={true}
                                trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                                thumbColor="white"
                              />
                              <Text style={styles.timeSlotText}>9:00 AM - 12:00 PM</Text>
                            </View>
                          </View>
                          <View style={styles.timeSlotContainer}>
                            <View style={styles.timeSlotContent}>
                              <Switch
                                value={true}
                                trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                                thumbColor="white"
                              />
                              <Text style={styles.timeSlotText}>2:00 PM - 6:00 PM</Text>
                            </View>
                          </View>
                        </View>
                      </View>
                    )}

                    <Text style={styles.bookingsTitle}>Bookings</Text>
                    {selectedDate.getDate() === 5 ||
                    selectedDate.getDate() === 12 ||
                    selectedDate.getDate() === 18 ||
                    selectedDate.getDate() === 25 ? (
                      <View style={styles.bookingItem}>
                        <View style={styles.bookingItemHeader}>
                          <View>
                            <Text style={styles.bookingItemTitle}>Regular Cleaning</Text>
                            <Text style={styles.bookingItemClient}>John Smith</Text>
                          </View>
                          <Badge variant="success">Confirmed</Badge>
                        </View>
                        <View style={styles.bookingItemTime}>
                          <Feather name="clock" size={16} color={theme.colors.textLight} />
                          <Text style={styles.bookingItemTimeText}>2:00 PM - 4:00 PM</Text>
                        </View>
                      </View>
                    ) : (
                      <Text style={styles.noBookingsText}>No bookings for this date.</Text>
                    )}

                    <Button
                      title="Save Changes"
                      variant="warning"
                      icon={<Feather name="save" size={16} color="black" />}
                      style={styles.saveButton}
                      fullWidth
                    />
                  </View>
                </Card>
              )}
            </>
          ) : (
            <Card style={styles.weeklyScheduleCard}>
              <View style={styles.weeklyScheduleContent}>
                <Text style={styles.weeklyScheduleTitle}>Weekly Availability</Text>

                {Object.entries(weeklyAvailability).map(([day, data], index, array) => (
                  <View
                    key={day}
                    style={[styles.dayScheduleItem, index === array.length - 1 && styles.lastDayScheduleItem]}
                  >
                    <View style={styles.dayScheduleHeader}>
                      <Text style={styles.dayName}>{day}</Text>
                      <Switch
                        value={data.available}
                        trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                        thumbColor="white"
                      />
                    </View>

                    {data.available && (
                      <View style={styles.timeSlotsList}>
                        {data.slots.map((slot, slotIndex) => (
                          <View key={slotIndex} style={styles.timeSlotItem}>
                            <View style={{ flexDirection: "row", alignItems: "center" }}>
                              <Feather name="clock" size={16} color={theme.colors.textLight} />
                              <Text style={styles.timeSlotItemText}>{slot}</Text>
                            </View>
                            <TouchableOpacity>
                              <Feather name="x" size={16} color={theme.colors.notification} />
                            </TouchableOpacity>
                          </View>
                        ))}
                        <TouchableOpacity style={styles.addTimeSlotButton}>
                          <Feather name="clock" size={16} color={theme.colors.primary} />
                          <Text style={styles.addTimeSlotText}>Add Time Slot</Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                ))}

                <Button
                  title="Save Schedule"
                  variant="warning"
                  icon={<Feather name="save" size={16} color="black" />}
                  style={styles.saveButton}
                  fullWidth
                />
              </View>
            </Card>
          )}
        </ScrollView>
      </View>
    </View>
  )
}

export default CleanerAvailabilityScreen
