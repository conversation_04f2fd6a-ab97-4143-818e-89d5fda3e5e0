"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, TextInput } from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Header from "../components/Header"
import Button from "../components/Button"

type PaymentMethodsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

const PaymentMethodsScreen = () => {
  const navigation = useNavigation<PaymentMethodsScreenNavigationProp>()
  const theme = useTheme()
  const [selectedCard, setSelectedCard] = useState("card1")
  const [showAddCardModal, setShowAddCardModal] = useState(false)

  // Dummy payment methods data
  const paymentMethods = [
    {
      id: "card1",
      type: "visa",
      last4: "4242",
      expiry: "05/25",
      name: "<PERSON>",
      isDefault: true,
    },
    {
      id: "card2",
      type: "mastercard",
      last4: "5555",
      expiry: "08/24",
      name: "Jessica Davis",
      isDefault: false,
    },
  ]

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.md,
    },
    title: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    cardOption: {
      marginBottom: theme.spacing.md,
    },
    cardContent: {
      flexDirection: "row",
      alignItems: "center",
      padding: theme.spacing.md,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      backgroundColor: "white",
    },
    cardContentSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}10`,
    },
    cardIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: `${theme.colors.accent}30`,
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.md,
    },
    cardInfo: {
      flex: 1,
    },
    cardType: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
      flexDirection: "row",
      alignItems: "center",
    },
    defaultBadge: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.accent,
      backgroundColor: `${theme.colors.accent}20`,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.sm,
      marginLeft: theme.spacing.sm,
    },
    cardExpiry: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    cardActions: {
      flexDirection: "row",
      alignItems: "center",
    },
    deleteButton: {
      padding: theme.spacing.sm,
    },
    addCardButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      backgroundColor: "white",
    },
    addCardText: {
      fontSize: theme.fontSizes.md,
      color: theme.colors.primary,
      marginLeft: theme.spacing.sm,
    },
    infoSection: {
      marginTop: theme.spacing.xl,
    },
    infoTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
      marginBottom: theme.spacing.sm,
    },
    infoText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginBottom: theme.spacing.md,
      lineHeight: 20,
    },
    acceptedCardsContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    acceptedCardsText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginLeft: theme.spacing.sm,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: "white",
    },
    modalHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    modalTitle: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
    },
    modalContent: {
      padding: theme.spacing.md,
    },
    inputContainer: {
      marginBottom: theme.spacing.md,
    },
    inputLabel: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      marginBottom: theme.spacing.xs,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.sm,
      fontSize: theme.fontSizes.md,
    },
    inputRow: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    inputHalf: {
      width: "48%",
    },
    checkboxContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 4,
      marginRight: theme.spacing.sm,
      justifyContent: "center",
      alignItems: "center",
    },
    checkboxChecked: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    checkboxLabel: {
      fontSize: theme.fontSizes.sm,
    },
  })

  const handleDeleteCard = (cardId: string) => {
    Alert.alert(
      "Remove Payment Method",
      "Are you sure you want to remove this payment method? This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Remove",
          style: "destructive",
          onPress: () => {
            // Handle card deletion logic
            console.log(`Deleting card ${cardId}`)
          },
        },
      ],
    )
  }

  const handleAddCard = () => {
    // Show modal or navigate to add card screen
    setShowAddCardModal(true)
  }

  const renderAddCardModal = () => {
    if (!showAddCardModal) return null

    return (
      <View style={styles.modalContainer}>
        <Header
          title="Add Payment Method"
          showBackButton
          rightIcon={
            <TouchableOpacity onPress={() => setShowAddCardModal(false)}>
              <Feather name="x" size={24} color="white" />
            </TouchableOpacity>
          }
        />

        <ScrollView style={styles.modalContent}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Card Number</Text>
            <TextInput
              style={styles.input}
              placeholder="1234 5678 9012 3456"
              keyboardType="number-pad"
              maxLength={19}
            />
          </View>

          <View style={styles.inputRow}>
            <View style={[styles.inputContainer, styles.inputHalf]}>
              <Text style={styles.inputLabel}>Expiry Date</Text>
              <TextInput style={styles.input} placeholder="MM/YY" keyboardType="number-pad" maxLength={5} />
            </View>

            <View style={[styles.inputContainer, styles.inputHalf]}>
              <Text style={styles.inputLabel}>CVC</Text>
              <TextInput style={styles.input} placeholder="123" keyboardType="number-pad" maxLength={3} />
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Name on Card</Text>
            <TextInput style={styles.input} placeholder="John Smith" />
          </View>

          <TouchableOpacity style={styles.checkboxContainer}>
            <View style={[styles.checkbox, { backgroundColor: theme.colors.primary }]}>
              <Feather name="check" size={14} color="white" />
            </View>
            <Text style={styles.checkboxLabel}>Set as default payment method</Text>
          </TouchableOpacity>

          <Button
            title="Add Card"
            variant="warning"
            onPress={() => {
              // Handle add card logic
              setShowAddCardModal(false)
            }}
          />
        </ScrollView>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {showAddCardModal ? (
        renderAddCardModal()
      ) : (
        <>
          <Header title="Payment Methods" showBackButton />

          <ScrollView style={styles.content}>
            <Text style={styles.title}>Your Payment Methods</Text>

            {/* Payment Methods List */}
            {paymentMethods.map((card) => (
              <TouchableOpacity
                key={card.id}
                style={styles.cardOption}
                onPress={() => setSelectedCard(card.id)}
                activeOpacity={0.7}
              >
                <View style={[styles.cardContent, selectedCard === card.id && styles.cardContentSelected]}>
                  <View style={styles.cardIconContainer}>
                    <Feather
                      name={card.type === "visa" ? "credit-card" : "credit-card"}
                      size={20}
                      color={theme.colors.accent}
                    />
                  </View>
                  <View style={styles.cardInfo}>
                    <View style={{ flexDirection: "row", alignItems: "center" }}>
                      <Text style={styles.cardType}>
                        {card.type.charAt(0).toUpperCase() + card.type.slice(1)} •••• {card.last4}
                      </Text>
                      {card.isDefault && <Text style={styles.defaultBadge}>Default</Text>}
                    </View>
                    <Text style={styles.cardExpiry}>Expires {card.expiry}</Text>
                  </View>
                  <View style={styles.cardActions}>
                    {selectedCard === card.id && <Feather name="check" size={20} color={theme.colors.primary} />}
                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={() => handleDeleteCard(card.id)}
                      hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                    >
                      <Feather name="trash-2" size={20} color={theme.colors.textLight} />
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableOpacity>
            ))}

            {/* Add New Card Button */}
            <TouchableOpacity style={styles.addCardButton} onPress={handleAddCard}>
              <Feather name="plus" size={20} color={theme.colors.primary} />
              <Text style={styles.addCardText}>Add Payment Method</Text>
            </TouchableOpacity>

            {/* Payment Information */}
            <View style={styles.infoSection}>
              <Text style={styles.infoTitle}>Payment Information</Text>
              <Text style={styles.infoText}>
                Your payment information is securely stored and processed. We use industry-standard encryption to
                protect your sensitive data.
              </Text>
              <View style={styles.acceptedCardsContainer}>
                <Feather name="credit-card" size={16} color={theme.colors.textLight} />
                <Text style={styles.acceptedCardsText}>We accept Visa, Mastercard, American Express, and Discover</Text>
              </View>
            </View>
          </ScrollView>
        </>
      )}
    </View>
  )
}

export default PaymentMethodsScreen
