"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, Alert, Modal, TextInput, ActivityIndicator } from "react-native"
import { useNavigation, useRoute, type RouteProp } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import type { Service } from "../types/service"
import { localServiceService } from "../services/localServiceService"
import Header from "../components/Header"
import Card from "../components/Card"
import Button from "../components/Button"

type ServiceDetailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>
type ServiceDetailScreenRouteProp = RouteProp<RootStackParamList, "ServiceDetail">

const ServiceDetailScreen = () => {
  const navigation = useNavigation<ServiceDetailScreenNavigationProp>()
  const route = useRoute<ServiceDetailScreenRouteProp>()
  const theme = useTheme()
  const { isAuthenticated } = useAuth()
  const [isFavorite, setIsFavorite] = useState(false)
  const [activeTab, setActiveTab] = useState("details")
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [rating, setRating] = useState(0)
  const [reviewText, setReviewText] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [service, setService] = useState<Service | null>(null)
  const [error, setError] = useState<string | null>(null)
  const { serviceId } = route.params

  // Fetch service details from the API
  useEffect(() => {
    const fetchServiceDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log(`Fetching service details for ID: ${serviceId}`);

        // Fetch service details from local storage
        const serviceData = await localServiceService.getServiceById(serviceId);

        if (!serviceData) {
          throw new Error('Service data is empty');
        }

        console.log('Service data received:', JSON.stringify(serviceData, null, 2));

        // Normalize the service data to handle both backend and frontend field names
        const normalizedService = {
          ...serviceData,
          // Ensure title is available (use name from backend if title is not present)
          title: serviceData.title || serviceData.name || 'Unnamed Service',
          // Ensure price is available (use basePrice as fallback)
          price: serviceData.price || serviceData.basePrice || 0,
          // Ensure we have an empty array for features if not provided
          features: serviceData.features || [],
          // Ensure we have an empty array for reviews if not provided
          reviews: serviceData.reviews || [],
          // Default values for other fields if not provided
          rating: serviceData.rating || 0,
          reviewCount: serviceData.reviewCount || 0,
          duration: serviceData.duration || (serviceData.estimatedDuration ? `${Math.round(serviceData.estimatedDuration / 60)} hours` : '2 hours'),
          longDescription: serviceData.longDescription || serviceData.description || 'No description available',
          // Ensure image is available
          image: serviceData.image || 'https://picsum.photos/id/26/600/300',
          // Ensure isActive is available
          isActive: serviceData.isActive !== undefined ? serviceData.isActive : true,
        };

        console.log('Normalized service data:', JSON.stringify(normalizedService, null, 2));
        setService(normalizedService);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching service details:', error);
        setError('Failed to load service details from the server. Please check your connection and try again.');
        setIsLoading(false);
      }
    };

    fetchServiceDetails();
  }, [serviceId]);

  // Handle write review button press
  const handleWriteReview = () => {
    if (!isAuthenticated) {
      Alert.alert(
        "Authentication Required",
        "You need to be logged in to write a review.",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Login", onPress: () => navigation.navigate("Login", {}) }
        ]
      );
      return;
    }

    setRating(0);
    setReviewText("");
    setShowReviewModal(true);
  };

  // Handle submit review
  const handleSubmitReview = () => {
    if (rating === 0) {
      Alert.alert("Rating Required", "Please select a rating before submitting.");
      return;
    }

    setIsSubmitting(true);

    // Simulate API call to submit review
    setTimeout(() => {
      setIsSubmitting(false);
      setShowReviewModal(false);

      // Show success message
      Alert.alert(
        "Review Submitted",
        "Thank you for your feedback! Your review has been submitted successfully.",
        [{ text: "OK" }]
      );

      // Set active tab to reviews to show the reviews section
      setActiveTab("reviews");
    }, 1000);
  };

  // Review Modal Component
  const renderReviewModal = () => {
    return (
      <Modal
        visible={showReviewModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowReviewModal(false)}
      >
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0,0,0,0.5)',
          padding: theme.spacing.md,
        }}>
          <View style={{
            width: '100%',
            backgroundColor: 'white',
            borderRadius: theme.borderRadius.md,
            padding: theme.spacing.lg,
            maxHeight: '80%',
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: theme.spacing.md }}>
              <Text style={{ fontSize: theme.fontSizes.lg, fontWeight: '600' }}>Write a Review</Text>
              <TouchableOpacity onPress={() => setShowReviewModal(false)}>
                <Feather name="x" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <Text style={{ marginBottom: theme.spacing.md, color: theme.colors.textLight }}>
              How would you rate this service?
            </Text>

            <View style={{ flexDirection: 'row', marginBottom: theme.spacing.lg }}>
              {[1, 2, 3, 4, 5].map((star) => (
                <TouchableOpacity
                  key={star}
                  onPress={() => setRating(star)}
                  style={{ padding: theme.spacing.xs }}
                >
                  <Feather
                    name="star"
                    size={32}
                    color={star <= rating ? theme.colors.warning : theme.colors.border}
                  />
                </TouchableOpacity>
              ))}
            </View>

            <Text style={{ marginBottom: theme.spacing.sm, color: theme.colors.textLight }}>
              Your review (optional)
            </Text>

            <TextInput
              style={{
                borderWidth: 1,
                borderColor: theme.colors.border,
                borderRadius: theme.borderRadius.md,
                padding: theme.spacing.md,
                height: 120,
                textAlignVertical: 'top',
                marginBottom: theme.spacing.lg,
              }}
              placeholder="Share your experience with this service..."
              multiline
              value={reviewText}
              onChangeText={setReviewText}
            />

            <Button
              title={isSubmitting ? "Submitting..." : "Submit Review"}
              onPress={handleSubmitReview}
              disabled={rating === 0 || isSubmitting}
            />
          </View>
        </View>
      </Modal>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    errorText: {
      fontSize: theme.fontSizes.lg,
      color: theme.colors.text,
    },
    imageContainer: {
      width: "100%",
      height: 250,
    },
    image: {
      width: "100%",
      height: "100%",
    },
    content: {
      padding: theme.spacing.md,
    },
    titlePriceContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: theme.spacing.sm,
    },
    title: {
      fontSize: theme.fontSizes.xl,
      fontWeight: "700",
      color: theme.colors.text,
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    price: {
      fontSize: theme.fontSizes.xl,
      fontWeight: "700",
      color: theme.colors.text,
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    star: {
      color: "#FFD700",
      marginRight: 4,
    },
    ratingText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      marginRight: 4,
    },
    reviewsText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginRight: theme.spacing.sm,
    },
    separator: {
      height: 16,
      width: 1,
      backgroundColor: theme.colors.border,
      marginHorizontal: theme.spacing.sm,
    },
    durationText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginLeft: theme.spacing.xs,
    },
    providerCard: {
      marginBottom: theme.spacing.md,
      padding: theme.spacing.md,
    },
    providerContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    providerImage: {
      width: 56,
      height: 56,
      borderRadius: 28,
      marginRight: theme.spacing.md,
    },
    providerInfo: {
      flex: 1,
    },
    providerNameContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    providerName: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginRight: theme.spacing.sm,
    },
    verifiedBadge: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: `${theme.colors.secondary}30`,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.sm,
    },
    verifiedText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.secondary,
      marginLeft: 2,
    },
    providerRating: {
      flexDirection: "row",
      alignItems: "center",
    },
    providerRatingText: {
      fontSize: theme.fontSizes.sm,
      marginLeft: 4,
      marginRight: 4,
    },
    providerJobsText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    tabsContainer: {
      flexDirection: "row",
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      marginBottom: theme.spacing.md,
    },
    tab: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      alignItems: "center",
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: theme.colors.primary,
    },
    tabText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    activeTabText: {
      color: theme.colors.primary,
      fontWeight: "600",
    },
    sectionTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.sm,
    },
    description: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      lineHeight: 20,
      marginBottom: theme.spacing.md,
    },
    detailsGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
    },
    detailItem: {
      width: "50%",
      flexDirection: "row",
      alignItems: "flex-start",
      marginBottom: theme.spacing.md,
    },
    detailIcon: {
      marginRight: theme.spacing.sm,
      color: theme.colors.textLight,
    },
    detailTextContainer: {
      flex: 1,
    },
    detailLabel: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
    },
    detailValue: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
    },
    featureItem: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    featureIcon: {
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: `${theme.colors.accent}30`,
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.sm,
    },
    featureText: {
      fontSize: theme.fontSizes.sm,
    },
    reviewsHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    reviewsRatingContainer: {
      alignItems: "flex-start",
    },
    reviewsRating: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      marginLeft: 4,
    },
    reviewsCount: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    reviewItem: {
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      paddingBottom: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    reviewHeader: {
      flexDirection: "row",
      marginBottom: theme.spacing.sm,
    },
    reviewAvatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: theme.spacing.sm,
    },
    reviewUserInfo: {
      flex: 1,
    },
    reviewUserName: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
    },
    reviewRatingDate: {
      flexDirection: "row",
      alignItems: "center",
    },
    reviewStars: {
      flexDirection: "row",
    },
    reviewDate: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      marginLeft: theme.spacing.sm,
    },
    reviewText: {
      fontSize: theme.fontSizes.sm,
      lineHeight: 20,
    },
    bottomContainer: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: "white",
    },
  })

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Header title="Service Details" showBackButton />
        <View style={styles.errorContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.errorText, { marginTop: theme.spacing.md }]}>Loading service details...</Text>
        </View>
      </View>
    )
  }

  if (error || !service) {
    return (
      <View style={styles.container}>
        <Header title="Service Details" showBackButton />
        <View style={styles.errorContainer}>
          <Feather name="alert-circle" size={48} color={theme.colors.notification} />
          <Text style={[styles.errorText, { marginTop: theme.spacing.md }]}>
            {error || "Service not found"}
          </Text>
          <Text style={[styles.description, { textAlign: 'center', marginHorizontal: theme.spacing.lg, marginTop: theme.spacing.sm }]}>
            Please make sure the backend server is running on ************:3000 and try again.
          </Text>
          <Text style={[styles.description, { textAlign: 'center', marginHorizontal: theme.spacing.lg, marginTop: theme.spacing.sm, fontSize: theme.fontSizes.xs }]}>
            If you're using an emulator, ensure it can connect to your host machine's IP address.
          </Text>
          <Button
            title="Try Again"
            onPress={() => navigation.replace("ServiceDetail", { serviceId })}
            style={{ marginTop: theme.spacing.lg }}
          />
        </View>
      </View>
    )
  }

  const handleBookNow = () => {
    // Pass the complete service ID to the booking screen
    // The booking screen will fetch the full service details
    navigation.navigate("Booking", {
      serviceId: service.id,
    })
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case "details":
        return (
          <View>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{service.longDescription}</Text>

            <Text style={styles.sectionTitle}>Service Details</Text>
            <View style={styles.detailsGrid}>
              <View style={styles.detailItem}>
                <Feather name="clock" size={20} style={styles.detailIcon} />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Duration</Text>
                  <Text style={styles.detailValue}>{service.duration}</Text>
                </View>
              </View>
              <View style={styles.detailItem}>
                <Feather name="calendar" size={20} style={styles.detailIcon} />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Availability</Text>
                  <Text style={styles.detailValue}>Mon-Sat, 8AM-6PM</Text>
                </View>
              </View>
              <View style={styles.detailItem}>
                <Feather name="dollar-sign" size={20} style={styles.detailIcon} />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Hourly Rate</Text>
                  <Text style={styles.detailValue}>D{service.price}/hour</Text>
                </View>
              </View>
              <View style={styles.detailItem}>
                <Feather name="map-pin" size={20} style={styles.detailIcon} />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Service Area</Text>
                  <Text style={styles.detailValue}>Greater Banjul Area</Text>
                </View>
              </View>
            </View>
          </View>
        )
      case "included":
        return (
          <View>
            <Text style={styles.sectionTitle}>What's Included</Text>
            {service.features && service.features.length > 0 ? (
              service.features.map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <View style={styles.featureIcon}>
                    <Feather name="check" size={12} color={theme.colors.accent} />
                  </View>
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))
            ) : (
              <View style={{ padding: theme.spacing.md, alignItems: 'center' }}>
                <Feather name="info" size={24} color={theme.colors.textLight} style={{ marginBottom: theme.spacing.sm }} />
                <Text style={{ color: theme.colors.textLight }}>No features listed for this service</Text>
              </View>
            )}
          </View>
        )
      case "reviews":
        return (
          <View>
            <View style={styles.reviewsHeader}>
              <View style={styles.reviewsRatingContainer}>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <Feather name="star" size={20} style={styles.star} />
                  <Text style={styles.reviewsRating}>{service.rating}</Text>
                </View>
                <Text style={styles.reviewsCount}>{service.reviewCount} reviews</Text>
              </View>
              <Button title="Write a Review" variant="outline" size="small" onPress={handleWriteReview} />
            </View>

            {service.reviews && service.reviews.length > 0 ? (
              service.reviews.map((review) => (
                <View key={review.id} style={styles.reviewItem}>
                  <View style={styles.reviewHeader}>
                    <Image source={{ uri: review.avatar }} style={styles.reviewAvatar} />
                    <View style={styles.reviewUserInfo}>
                      <Text style={styles.reviewUserName}>{review.user}</Text>
                      <View style={styles.reviewRatingDate}>
                        <View style={styles.reviewStars}>
                          {[...Array(5)].map((_, i) => (
                            <Feather key={i} name="star" size={12} color={i < review.rating ? "#FFD700" : "#E0E0E0"} />
                          ))}
                        </View>
                        <Text style={styles.reviewDate}>{review.date}</Text>
                      </View>
                    </View>
                  </View>
                  <Text style={styles.reviewText}>{review.comment}</Text>
                </View>
              ))
            ) : (
              <View style={{ padding: theme.spacing.md, alignItems: 'center' }}>
                <Feather name="message-square" size={24} color={theme.colors.textLight} style={{ marginBottom: theme.spacing.sm }} />
                <Text style={{ color: theme.colors.textLight }}>No reviews yet</Text>
                <Text style={{ color: theme.colors.textLight, textAlign: 'center', marginTop: theme.spacing.xs }}>Be the first to share your experience!</Text>
              </View>
            )}
          </View>
        )
      default:
        return null
    }
  }

  return (
    <View style={styles.container}>
      {/* Render the review modal */}
      {renderReviewModal()}

      <Header
        title="Service Details"
        showBackButton
        rightIcon={
          <TouchableOpacity onPress={() => setIsFavorite(!isFavorite)}>
            <Feather
              name={isFavorite ? "heart" : "heart"}
              size={24}
              color={isFavorite ? theme.colors.warning : "white"}
              style={{ opacity: isFavorite ? 1 : 0.7 }}
            />
          </TouchableOpacity>
        }
      />

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image source={{ uri: service.image }} style={styles.image} resizeMode="cover" />
        </View>

        <View style={styles.content}>
          {/* Title and Price */}
          <View style={styles.titlePriceContainer}>
            <Text style={styles.title}>{service.title}</Text>
            <Text style={styles.price}>D{service.price}/hr</Text>
          </View>

          {/* Rating and Duration */}
          <View style={styles.ratingContainer}>
            <Feather name="star" size={16} style={styles.star} />
            <Text style={styles.ratingText}>{service.rating}</Text>
            <Text style={styles.reviewsText}>({service.reviewCount} reviews)</Text>
            <View style={styles.separator} />
            <Feather name="clock" size={16} color={theme.colors.textLight} />
            <Text style={styles.durationText}>Est. {service.duration}</Text>
          </View>

          {/* Service Provider section removed */}

          {/* Tabs */}
          <View style={styles.tabsContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === "details" && styles.activeTab]}
              onPress={() => setActiveTab("details")}
            >
              <Text style={[styles.tabText, activeTab === "details" && styles.activeTabText]}>Details</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === "included" && styles.activeTab]}
              onPress={() => setActiveTab("included")}
            >
              <Text style={[styles.tabText, activeTab === "included" && styles.activeTabText]}>What's Included</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === "reviews" && styles.activeTab]}
              onPress={() => setActiveTab("reviews")}
            >
              <Text style={[styles.tabText, activeTab === "reviews" && styles.activeTabText]}>Reviews</Text>
            </TouchableOpacity>
          </View>

          {/* Tab Content */}
          {renderTabContent()}
        </View>
      </ScrollView>

      {/* Bottom Action */}
      <View style={styles.bottomContainer}>
        <Button title="Book Now" variant="warning" fullWidth onPress={handleBookNow} />
      </View>
    </View>
  )
}

export default ServiceDetailScreen
