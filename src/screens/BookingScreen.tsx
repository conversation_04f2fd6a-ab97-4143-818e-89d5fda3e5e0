"use client"

import { useState, useEffect, useCallback } from "react"
import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, Alert } from "react-native"
import { useNavigation, useRoute, type RouteProp } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import { useLocation, type Location } from "../context/LocationContext"
import { useBooking } from "../context/BookingContext"
import { useAuth } from "../context/AuthContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import { services } from "../data/services"
import { localServiceService } from "../services/localServiceService"
import {
  Provider,
  providers,
  getEligibleProviders,
  getRandomProviders,
  autoAssignProvider,
  getPreviouslyHighlyRatedProviders
} from "../data/providers"
import Header from "../components/Header"
import Card from "../components/Card"
import Button from "../components/Button"
import ProviderSelection from "../components/ProviderSelection"
import AuthRequiredModal from "../components/AuthRequiredModal"
import Toast from "../components/Toast"
import Storage from "../utils/storage"

type BookingScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>
type BookingScreenRouteProp = RouteProp<RootStackParamList, "Booking">

const BookingScreen = () => {
  const navigation = useNavigation<BookingScreenNavigationProp>()
  const route = useRoute<BookingScreenRouteProp>()
  const theme = useTheme()
  const { savedLocations, currentLocation } = useLocation()
  const { isAuthenticated } = useAuth()
  const {
    bookingData,
    setBookingData,
    saveBookingData,
    isRestoredBooking,
    setIsRestoredBooking,
    trackAbandonedBooking
  } = useBooking()

  const [selectedService, setSelectedService] = useState<any>(null)
  const [selectedDate, setSelectedDate] = useState<string | null>(null)
  const [selectedTime, setSelectedTime] = useState<string | null>(null)
  const [selectedAddress, setSelectedAddress] = useState<string | null>(currentLocation?.id || null)
  const [selectedFrequency, setSelectedFrequency] = useState<string>("one-time")
  const [showToast, setShowToast] = useState(false)
  const [showWelcomeToast, setShowWelcomeToast] = useState(false)

  // Provider selection state
  const [eligibleProviders, setEligibleProviders] = useState<Provider[]>([])
  const [recommendedProviders, setRecommendedProviders] = useState<Provider[]>([])
  const [selectedProviderId, setSelectedProviderId] = useState<string | null>(null)
  const [autoAssignEnabled, setAutoAssignEnabled] = useState<boolean>(false)
  const [assignedProvider, setAssignedProvider] = useState<Provider | null>(null)
  const [bookingStatus, setBookingStatus] = useState<string>("pending")

  const { serviceId, selectedProvider } = route.params || {}

  // Initialize selected service
  useEffect(() => {
    const fetchServiceDetails = async () => {
      if (serviceId) {
        try {
          // Fetch service details from local storage
          const serviceData = await localServiceService.getServiceById(serviceId);

          if (serviceData) {
            console.log('Service data loaded for booking:', serviceData.id);
            setSelectedService(serviceData);
          } else {
            // Fallback to local data if API fails
            const service = services.find((s) => s.id === serviceId);
            if (service) {
              setSelectedService(service);
            } else {
              setSelectedService(services[0]);
            }
          }
        } catch (error) {
          console.error('Error fetching service for booking:', error);
          // Fallback to local data if API fails
          const service = services.find((s) => s.id === serviceId);
          if (service) {
            setSelectedService(service);
          } else {
            setSelectedService(services[0]);
          }
        }
      } else {
        setSelectedService(services[0]);
      }
    };

    fetchServiceDetails();
  }, [serviceId])

  // Show welcome toast for unauthenticated users
  useEffect(() => {
    // Check if user is not authenticated and this is not a restored booking
    if (!isAuthenticated && !isRestoredBooking) {
      // Check if we've shown this toast before in this session
      const hasShownWelcomeToast = async () => {
        try {
          const shown = await Storage.getItem('welcome_toast_shown')
          if (!shown) {
            // Show the toast after a short delay
            setTimeout(() => {
              setShowWelcomeToast(true)
              // Mark as shown for this session
              Storage.setItem('welcome_toast_shown', 'true')
            }, 1000)
          }
        } catch (error) {
          console.error('Error checking welcome toast status:', error)
        }
      }

      hasShownWelcomeToast()
    }
  }, [isAuthenticated, isRestoredBooking])

  // Load booking data if available
  useEffect(() => {
    // Check if we have restored booking data
    if (isRestoredBooking && bookingData) {
      // Restore form data from booking context
      if (bookingData.serviceId) {
        const savedService = services.find((s) => s.id === bookingData.serviceId)
        if (savedService) setSelectedService(savedService)
      }
      if (bookingData.date) setSelectedDate(bookingData.date)
      if (bookingData.time) setSelectedTime(bookingData.time)
      if (bookingData.address) setSelectedAddress(bookingData.address)
      if (bookingData.frequency) setSelectedFrequency(bookingData.frequency)
      if (bookingData.providerId) setSelectedProviderId(bookingData.providerId)
      if (bookingData.autoAssign) setAutoAssignEnabled(bookingData.autoAssign)

      // Show toast notification
      setShowToast(true)
      // Reset the restored flag
      setIsRestoredBooking(false)
    }
  }, [isRestoredBooking, bookingData]);

  // Handle pre-selected provider from HeroDetailScreen
  useEffect(() => {
    if (selectedProvider) {
      // Add the selected provider to the list of providers if it's not already there
      const providerExists = providers.some(p => p.id === selectedProvider.id);
      if (!providerExists) {
        // Add the provider to the providers array
        const newProvider: Provider = {
          id: selectedProvider.id,
          name: selectedProvider.name,
          image: selectedProvider.image,
          rating: selectedProvider.rating,
          jobs: selectedProvider.reviews || 0,
          verified: selectedProvider.verified || false,
          price: selectedProvider.price || 0,
          availability: ["monday", "tuesday", "wednesday", "thursday", "friday"]
        };

        // This would normally be handled by the backend
        // For now, we're just adding it to the local providers array
        providers.push(newProvider);
      }
    }
  }, [selectedProvider]);

  // Update eligible providers when service, date, or time changes
  useEffect(() => {
    if (selectedService?.id && selectedDate && selectedTime) {
      // Get eligible providers based on service, date, and time
      const eligible = getEligibleProviders(selectedService.id, selectedDate, selectedTime)

      // If we have a pre-selected provider, make sure it's included in the eligible list
      if (selectedProvider && !eligible.some(p => p.id === selectedProvider.id)) {
        // Add the pre-selected provider to the eligible list
        eligible.push({
          id: selectedProvider.id,
          name: selectedProvider.name,
          image: selectedProvider.image,
          rating: selectedProvider.rating,
          jobs: selectedProvider.reviews || 0,
          verified: selectedProvider.verified || false,
          price: selectedProvider.price || 0,
          availability: ["monday", "tuesday", "wednesday", "thursday", "friday"]
        });
      }

      setEligibleProviders(eligible)

      // Get previously highly-rated providers for this user
      // In a real app, this would use the actual user ID
      const highlyRatedProviderIds = getPreviouslyHighlyRatedProviders("user123")

      // Get 4-5 recommended providers with slight weighting
      let recommended = getRandomProviders(eligible, 5, highlyRatedProviderIds)

      // If we have a pre-selected provider, make sure it's first in the recommended list
      if (selectedProvider) {
        // Remove the pre-selected provider if it's already in the list
        recommended = recommended.filter(p => p.id !== selectedProvider.id);

        // Add the pre-selected provider to the beginning of the list
        recommended.unshift({
          id: selectedProvider.id,
          name: selectedProvider.name,
          image: selectedProvider.image,
          rating: selectedProvider.rating,
          jobs: selectedProvider.reviews || 0,
          verified: selectedProvider.verified || false,
          price: selectedProvider.price || 0,
          availability: ["monday", "tuesday", "wednesday", "thursday", "friday"]
        });

        // Set the pre-selected provider as selected
        setSelectedProviderId(selectedProvider.id);
      } else {
        // Reset provider selection when criteria change and no pre-selected provider
        setSelectedProviderId(null);
      }

      setRecommendedProviders(recommended)
      setAutoAssignEnabled(false)
      setAssignedProvider(null)
      setBookingStatus("pending")
    } else {
      // Reset provider-related state if criteria are incomplete
      setEligibleProviders([])
      setRecommendedProviders([])

      // Keep the pre-selected provider if available
      if (selectedProvider) {
        setSelectedProviderId(selectedProvider.id);
      } else {
        setSelectedProviderId(null);
      }

      setAutoAssignEnabled(false)
      setAssignedProvider(null)
    }
  }, [selectedService?.id, selectedDate, selectedTime, selectedProvider])

  // Handle provider selection
  const handleSelectProvider = useCallback((providerId: string | null) => {
    setSelectedProviderId(providerId)
    setAutoAssignEnabled(false)
    setAssignedProvider(null)
  }, [])

  // Toggle auto-assign mode
  const handleToggleAutoAssign = useCallback(() => {
    setAutoAssignEnabled(prev => !prev)
    setSelectedProviderId(null)
    setAssignedProvider(null)
  }, [])

  // Calculate total price with frequency discounts
  const calculateTotal = useCallback(() => {
    if (!selectedService) return { subtotal: 0, fee: 0, total: 0, discount: 0, discountRate: 0 }

    const hours = Number.parseInt(selectedService.duration.split(" ")[0])
    let subtotal = selectedService.price * hours
    let discountRate = 0

    // Apply discount based on frequency
    if (selectedFrequency === "weekly") {
      discountRate = 0.20 // 20% off for weekly
    } else if (selectedFrequency === "bi-weekly") {
      discountRate = 0.15 // 15% off for bi-weekly
    } else if (selectedFrequency === "monthly") {
      discountRate = 0.10 // 10% off for monthly
    }

    const discount = Math.round(subtotal * discountRate)
    const discountedSubtotal = subtotal - discount
    const fee = Math.round(discountedSubtotal * 0.1) // 10% service fee
    const total = discountedSubtotal + fee

    return {
      subtotal,
      discountedSubtotal,
      fee,
      total,
      discount,
      discountRate
    }
  }, [selectedService, selectedFrequency])

  const { subtotal, discountedSubtotal, fee, total, discount, discountRate } = calculateTotal()

  // Save current booking data to context
  const saveCurrentBookingData = useCallback(() => {
    setBookingData({
      serviceId: selectedService?.id,
      serviceName: selectedService?.title,
      serviceImage: selectedService?.image,
      price: selectedService?.price,
      duration: selectedService?.duration,
      date: selectedDate,
      time: selectedTime,
      address: selectedAddress,
      frequency: selectedFrequency,
      providerId: selectedProviderId,
      autoAssign: autoAssignEnabled,
      subtotal: subtotal,
      discountedSubtotal: discountedSubtotal,
      discount: discount,
      discountRate: discountRate,
      fee: fee,
      total: total
    })
  }, [
    selectedService,
    selectedDate,
    selectedTime,
    selectedAddress,
    selectedFrequency,
    selectedProviderId,
    autoAssignEnabled,
    subtotal,
    discountedSubtotal,
    discount,
    discountRate,
    fee,
    total,
    setBookingData
  ])

  // Handle booking confirmation
  const handleConfirmBooking = useCallback(() => {
    // Validate all required fields first
    if (!selectedService || !selectedDate || !selectedTime) {
      Alert.alert("Missing Information", "Please select a service, date, and time.")
      return
    }

    // Validate provider selection if date and time are selected
    if (selectedDate && selectedTime && !(autoAssignEnabled || selectedProviderId)) {
      Alert.alert("Provider Selection Required", "Please select a provider or enable auto-assign.")
      return
    }

    // Save current booking data regardless of authentication status
    saveCurrentBookingData()

    // Save booking data to storage
    saveBookingData()

    // We no longer check for authentication here - we'll do it in the PaymentOptionScreen

    // Validate address
    if (!selectedAddress) {
      Alert.alert("Address Required", "Please select an address for your booking.")
      return
    }

    // Check if there are any saved addresses
    if (savedLocations.length === 0) {
      Alert.alert(
        "No Address Found",
        "Please add an address before booking.",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Add Address", onPress: () => navigation.navigate("AddAddress" as any) }
        ]
      )
      return
    }

    // Generate a booking ID
    const bookingId = `BK-${Math.floor(Math.random() * 100000)}`

    // Set booking status to pending_payment
    setBookingStatus("pending_payment")

    // Navigate to payment options screen with booking details
    navigation.navigate("PaymentOption", {
      bookingId,
      serviceId: selectedService.id,
      serviceTitle: selectedService.title,
      date: selectedDate,
      time: selectedTime,
      address: selectedAddress,
      duration: selectedService.duration,
      subtotal,
      fee,
      total,
      providerId: selectedProviderId || (autoAssignEnabled ? "auto_assign" : undefined)
    })
  }, [selectedService, selectedDate, selectedTime, selectedAddress, autoAssignEnabled, selectedProviderId, subtotal, fee, total, navigation, savedLocations.length])

  // Dummy data
  const dates = [
    { day: "Mon", date: "15", month: "May" },
    { day: "Tue", date: "16", month: "May" },
    { day: "Wed", date: "17", month: "May" },
    { day: "Thu", date: "18", month: "May" },
    { day: "Fri", date: "19", month: "May" },
    { day: "Sat", date: "20", month: "May" },
    { day: "Sun", date: "21", month: "May" },
  ]

  const timeSlots = [
    "9:00 AM",
    "10:00 AM",
    "11:00 AM",
    "12:00 PM",
    "1:00 PM",
    "2:00 PM",
    "3:00 PM",
    "4:00 PM",
    "5:00 PM",
  ]

  // Price values are calculated above

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      flex: 1,
    },
    content: {
      padding: theme.spacing.md,
    },
    serviceCard: {
      marginBottom: theme.spacing.lg,
      padding: theme.spacing.md,
    },
    serviceContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    serviceImage: {
      width: 80,
      height: 80,
      borderRadius: theme.borderRadius.md,
      marginRight: theme.spacing.md,
    },
    serviceInfo: {
      flex: 1,
    },
    serviceTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: 4,
    },
    serviceDuration: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    durationText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginLeft: 4,
    },
    servicePrice: {
      fontSize: theme.fontSizes.md,
      fontWeight: "700",
    },
    sectionTitle: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    frequencyOptions: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginBottom: theme.spacing.lg,
      marginHorizontal: -theme.spacing.xs,
    },
    frequencyOption: {
      width: "48%",
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      margin: theme.spacing.xs,
      backgroundColor: "white",
    },
    frequencyOptionSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}10`,
    },
    frequencyOptionText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      textAlign: "center",
      marginBottom: 2,
    },
    frequencyOptionSubtext: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      textAlign: "center",
    },
    frequencyDetailOptions: {
      flexDirection: "row",
      marginBottom: theme.spacing.lg,
    },
    frequencyDetailOption: {
      flex: 1,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.sm,
      marginRight: theme.spacing.sm,
      backgroundColor: "white",
    },
    frequencyDetailOptionSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}10`,
    },
    frequencyDetailText: {
      fontSize: theme.fontSizes.sm,
      textAlign: "center",
    },
    frequencyDetailSubtext: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      textAlign: "center",
    },
    datesContainer: {
      marginBottom: theme.spacing.lg,
    },
    datesList: {
      flexDirection: "row",
    },
    dateOption: {
      width: 70,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.sm,
      marginRight: theme.spacing.sm,
      alignItems: "center",
      backgroundColor: "white",
    },
    dateOptionSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}10`,
    },
    dayText: {
      fontSize: theme.fontSizes.sm,
    },
    dateText: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
    },
    monthText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
    },
    timeSlotsContainer: {
      marginBottom: theme.spacing.lg,
    },
    timeSlotsGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginHorizontal: -theme.spacing.xs,
    },
    timeSlot: {
      width: "33%",
      paddingHorizontal: theme.spacing.xs,
      marginBottom: theme.spacing.sm,
    },
    timeSlotButton: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.sm,
      alignItems: "center",
      backgroundColor: "white",
    },
    timeSlotButtonSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}10`,
    },
    timeSlotText: {
      fontSize: theme.fontSizes.sm,
    },
    addressesContainer: {
      marginBottom: theme.spacing.lg,
    },
    addressOption: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.sm,
      backgroundColor: "white",
    },
    addressOptionSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}10`,
    },
    addressHeader: {
      flexDirection: "row",
      alignItems: "center",
    },
    addressIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: `${theme.colors.primary}20`,
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.sm,
    },
    addressInfo: {
      flex: 1,
    },
    addressName: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
    },
    addressText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    addAddressButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.sm,
      backgroundColor: "white",
    },
    addAddressText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.primary,
      marginLeft: theme.spacing.sm,
    },
    summaryCard: {
      marginBottom: theme.spacing.lg,
      padding: theme.spacing.md,
    },
    summaryTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    summaryRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: theme.spacing.sm,
    },
    summaryLabel: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    summaryValue: {
      fontSize: theme.fontSizes.sm,
    },
    summaryDivider: {
      height: 1,
      backgroundColor: theme.colors.border,
      marginVertical: theme.spacing.sm,
    },
    totalRow: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    totalLabel: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
    },
    totalValue: {
      fontSize: theme.fontSizes.md,
      fontWeight: "700",
    },
    bottomContainer: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: "white",
    },
  })

  if (!selectedService) {
    return (
      <View style={styles.container}>
        <Header title="Book Cleaning Service" showBackButton />
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <Text>Loading service information...</Text>
        </View>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <Header title="Book Cleaning Service" showBackButton />

      {/* Auth Required Modal removed - now handled in PaymentOptionScreen */}

      {/* Toast notification for restored booking */}
      <Toast
        visible={showToast}
        message="Your previous booking details have been restored"
        type="success"
        onClose={() => setShowToast(false)}
      />

      {/* Welcome toast for unauthenticated users */}
      <Toast
        visible={showWelcomeToast}
        message="Complete your booking details! You'll only need to login at the payment step."
        type="info"
        duration={6000}
        onClose={() => setShowWelcomeToast(false)}
      />

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Service Summary */}
          <Card style={styles.serviceCard}>
            <View style={styles.serviceContent}>
              <Image source={{ uri: selectedService.image }} style={styles.serviceImage} resizeMode="cover" />
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceTitle}>{selectedService.title}</Text>
                <View style={styles.serviceDuration}>
                  <Feather name="clock" size={14} color={theme.colors.textLight} />
                  <Text style={styles.durationText}>{selectedService.duration}</Text>
                </View>
                <Text style={styles.servicePrice}>D{selectedService.price}/hr</Text>
              </View>
            </View>
          </Card>

          {/* Booking Frequency */}
          <View style={{ marginBottom: theme.spacing.lg }}>
            <Text style={styles.sectionTitle}>How often do you need cleaning?</Text>
            <View style={styles.frequencyOptions}>
              <TouchableOpacity
                style={[styles.frequencyOption, selectedFrequency === "one-time" && styles.frequencyOptionSelected]}
                onPress={() => setSelectedFrequency("one-time")}
              >
                <Text style={styles.frequencyOptionText}>One-time</Text>
                <Text style={styles.frequencyOptionSubtext}>Single cleaning</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.frequencyOption, selectedFrequency === "weekly" && styles.frequencyOptionSelected]}
                onPress={() => setSelectedFrequency("weekly")}
              >
                <Text style={styles.frequencyOptionText}>Weekly</Text>
                <Text style={styles.frequencyOptionSubtext}>Save 20%</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.frequencyOption, selectedFrequency === "bi-weekly" && styles.frequencyOptionSelected]}
                onPress={() => setSelectedFrequency("bi-weekly")}
              >
                <Text style={styles.frequencyOptionText}>Bi-weekly</Text>
                <Text style={styles.frequencyOptionSubtext}>Save 15%</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.frequencyOption, selectedFrequency === "monthly" && styles.frequencyOptionSelected]}
                onPress={() => setSelectedFrequency("monthly")}
              >
                <Text style={styles.frequencyOptionText}>Monthly</Text>
                <Text style={styles.frequencyOptionSubtext}>Save 10%</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Date Selection */}
          <View style={styles.datesContainer}>
            <Text style={styles.sectionTitle}>Select date</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.datesList}>
                {dates.map((date) => (
                  <TouchableOpacity
                    key={date.date}
                    style={[
                      styles.dateOption,
                      selectedDate === `${date.day}, ${date.date} ${date.month}` && styles.dateOptionSelected,
                    ]}
                    onPress={() => setSelectedDate(`${date.day}, ${date.date} ${date.month}`)}
                  >
                    <Text style={styles.dayText}>{date.day}</Text>
                    <Text style={styles.dateText}>{date.date}</Text>
                    <Text style={styles.monthText}>{date.month}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>

          {/* Time Selection */}
          <View style={styles.timeSlotsContainer}>
            <Text style={styles.sectionTitle}>Select time</Text>
            <View style={styles.timeSlotsGrid}>
              {timeSlots.map((time) => (
                <View key={time} style={styles.timeSlot}>
                  <TouchableOpacity
                    style={[styles.timeSlotButton, selectedTime === time && styles.timeSlotButtonSelected]}
                    onPress={() => setSelectedTime(time)}
                  >
                    <Text style={styles.timeSlotText}>{time}</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>

          {/* Address Selection */}
          <View style={styles.addressesContainer}>
            <Text style={styles.sectionTitle}>Select address</Text>
            {savedLocations.map((location) => (
              <TouchableOpacity
                key={location.id}
                style={[styles.addressOption, selectedAddress === location.id && styles.addressOptionSelected]}
                onPress={() => setSelectedAddress(location.id)}
              >
                <View style={styles.addressHeader}>
                  <View style={styles.addressIconContainer}>
                    <Feather name="home" size={20} color={theme.colors.primary} />
                  </View>
                  <View style={styles.addressInfo}>
                    <Text style={styles.addressName}>{location.label}</Text>
                    <Text style={styles.addressText}>{location.address}</Text>
                  </View>
                  {selectedAddress === location.id && <Feather name="check" size={20} color={theme.colors.primary} />}
                </View>
              </TouchableOpacity>
            ))}
            <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
              <TouchableOpacity
                style={styles.addAddressButton}
                onPress={() => navigation.navigate("AddAddress" as any)}
              >
                <Feather name="map-pin" size={20} color={theme.colors.primary} />
                <Text style={styles.addAddressText}>Add New Address</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Provider Selection - Only show if date and time are selected */}
          {selectedDate && selectedTime && (
            <View style={{ marginBottom: theme.spacing.lg }}>
              <Text style={styles.sectionTitle}>Choose your provider</Text>
              <ProviderSelection
                providers={recommendedProviders}
                selectedProviderId={selectedProviderId}
                onSelectProvider={handleSelectProvider}
                autoAssignEnabled={autoAssignEnabled}
                onToggleAutoAssign={handleToggleAutoAssign}
              />
            </View>
          )}

          {/* Booking Summary */}
          <Card style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>Booking Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Service:</Text>
              <Text style={styles.summaryValue}>{selectedService.title}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Date:</Text>
              <Text style={styles.summaryValue}>{selectedDate || "Not selected"}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Time:</Text>
              <Text style={styles.summaryValue}>{selectedTime || "Not selected"}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Duration:</Text>
              <Text style={styles.summaryValue}>{selectedService.duration}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Address:</Text>
              <Text style={styles.summaryValue}>{selectedAddress || "Not selected"}</Text>
            </View>
            <View style={styles.summaryDivider} />
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal:</Text>
              <Text style={styles.summaryValue}>D{subtotal.toFixed(2)}</Text>
            </View>

            {discount > 0 && (
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Discount ({(discountRate * 100).toFixed(0)}%):</Text>
                <Text style={[styles.summaryValue, { color: theme.colors.success }]}>-D{discount.toFixed(2)}</Text>
              </View>
            )}

            {discount > 0 && (
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Discounted subtotal:</Text>
                <Text style={styles.summaryValue}>D{discountedSubtotal.toFixed(2)}</Text>
              </View>
            )}

            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Service fee:</Text>
              <Text style={styles.summaryValue}>D{fee.toFixed(2)}</Text>
            </View>
            <View style={styles.summaryDivider} />
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total:</Text>
              <Text style={styles.totalValue}>D{total.toFixed(2)}</Text>
            </View>
          </Card>
        </View>
      </ScrollView>

      {/* Bottom Action */}
      <View style={styles.bottomContainer}>
        <Button
          title={bookingStatus === "assigning" ? "Finding Provider..." : "Confirm Booking"}
          variant="warning"
          fullWidth
          disabled={
            !selectedDate ||
            !selectedTime ||
            !selectedAddress ||
            bookingStatus === "assigning" ||
            (selectedDate && selectedTime && !(autoAssignEnabled || selectedProviderId))
          }
          onPress={handleConfirmBooking}
        />
      </View>
    </View>
  )
}

export default BookingScreen
