import React, { useState, useCallback, useMemo, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  FlatList,
  SafeAreaView,
  StatusBar,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Feather } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import type { RootStackParamList } from "../navigation/RootNavigator";
import Button from "../components/Button";
import * as authStorage from "../utils/authStorage";

type OnboardingScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Define onboarding screens content
const onboardingScreens = [
  {
    id: '1',
    title: 'Professional Home Services',
    description: 'CleanConnect offers a wide range of professional cleaning and home services tailored to your needs.',
    image: 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    icon: 'home',
  },
  {
    id: '2',
    title: 'Real-time Provider Connection',
    description: 'Connect with service providers in real-time and track their arrival at your doorstep.',
    image: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    icon: 'clock',
  },
  {
    id: '3',
    title: 'Safe & Trusted Providers',
    description: 'All our service providers are vetted, background-checked, and trained to deliver quality service.',
    image: 'https://images.unsplash.com/photo-**********-e15b29be8c8f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    icon: 'shield',
  },
];

/**
 * OnboardingScreen Component
 *
 * Handles the onboarding flow for new users with a 3-screen walkthrough
 * explaining the app's features and benefits.
 */
const OnboardingScreen = () => {
  const navigation = useNavigation<OnboardingScreenNavigationProp>();
  const theme = useTheme();
  const flatListRef = useRef<FlatList>(null);

  // State management
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Get screen dimensions for responsive layout
  const { height, width } = Dimensions.get("window");

  // Note: Onboarding status check is now handled in RootNavigator
  // This screen should only be shown for first-time users

  // Calculate dynamic margin top based on screen height
  const dynamicTopMargin = Math.round(height * 0.05); // 5% of screen height

  // Handle skip button press
  const handleSkip = useCallback(async () => {
    try {
      // Mark onboarding as completed
      await authStorage.saveOnboardingStatus(true);
      console.log('Onboarding marked as completed (skipped)');

      // Navigate to role selection screen
      navigation.reset({
        index: 0,
        routes: [{ name: "RoleSelection" }],
      });
    } catch (error) {
      console.error('Error saving onboarding status:', error);
    }
  }, [navigation]);

  // Handle next button press
  const handleNext = useCallback(async () => {
    if (currentIndex < onboardingScreens.length - 1) {
      flatListRef.current?.scrollToIndex({
        index: currentIndex + 1,
        animated: true,
      });
    } else {
      try {
        // On last screen, mark onboarding as completed and navigate to role selection
        await authStorage.saveOnboardingStatus(true);
        console.log('Onboarding marked as completed (finished)');

        navigation.reset({
          index: 0,
          routes: [{ name: "RoleSelection" }],
        });
      } catch (error) {
        console.error('Error saving onboarding status:', error);
      }
    }
  }, [currentIndex, navigation]);

  // Handle get started button press
  const handleGetStarted = useCallback(async () => {
    setIsLoading(true);

    try {
      // Mark onboarding as completed
      await authStorage.saveOnboardingStatus(true);
      console.log('Onboarding marked as completed (get started)');

      // Navigate to role selection screen with a slight delay for better UX
      setTimeout(() => {
        navigation.reset({
          index: 0,
          routes: [{ name: "RoleSelection" }],
        });
        setIsLoading(false);
      }, 500);
    } catch (error) {
      console.error('Error saving onboarding status:', error);
      setIsLoading(false);
    }
  }, [navigation]);

  // Handle scroll event to update current index
  const handleViewableItemsChanged = useCallback(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }, []);

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
  };

  // Render onboarding screen item
  const renderItem = useCallback(({ item }) => {
    return (
      <View style={[styles.slide, { width }]}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: item.image }}
            style={styles.image}
            resizeMode="cover"
          />
          <View style={styles.iconOverlay}>
            <Feather name={item.icon} size={32} color="white" />
          </View>
        </View>
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: theme.colors.text }]}>{item.title}</Text>
          <Text style={[styles.description, { color: theme.colors.textLight }]}>{item.description}</Text>
        </View>
      </View>
    );
  }, [width, theme.colors]);

  // Render pagination dots
  const renderPagination = () => {
    return (
      <View style={styles.paginationContainer}>
        {onboardingScreens.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              { backgroundColor: index === currentIndex ? theme.colors.primary : theme.colors.border }
            ]}
          />
        ))}
      </View>
    );
  };

  // Memoized styles to prevent recreation on each render
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      paddingHorizontal: theme.spacing.md,
      paddingTop: dynamicTopMargin,
    },
    skipButton: {
      padding: theme.spacing.sm,
    },
    skipText: {
      color: theme.colors.textLight,
      fontSize: theme.fontSizes.md,
    },
    slide: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: theme.spacing.lg,
    },
    imageContainer: {
      width: '100%',
      height: height * 0.4,
      borderRadius: theme.borderRadius.lg,
      overflow: 'hidden',
      marginBottom: theme.spacing.xl,
      position: 'relative',
    },
    image: {
      width: '100%',
      height: '100%',
    },
    iconOverlay: {
      position: 'absolute',
      bottom: theme.spacing.md,
      right: theme.spacing.md,
      backgroundColor: 'rgba(0,0,0,0.5)',
      width: 60,
      height: 60,
      borderRadius: 30,
      alignItems: 'center',
      justifyContent: 'center',
    },
    textContainer: {
      alignItems: 'center',
      paddingHorizontal: theme.spacing.md,
    },
    title: {
      fontSize: 24,
      fontWeight: '700',
      marginBottom: theme.spacing.md,
      textAlign: 'center',
    },
    description: {
      fontSize: theme.fontSizes.md,
      textAlign: 'center',
      lineHeight: 24,
    },
    paginationContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginVertical: theme.spacing.xl,
    },
    paginationDot: {
      width: 10,
      height: 10,
      borderRadius: 5,
      marginHorizontal: 5,
    },
    footer: {
      paddingHorizontal: theme.spacing.lg,
      paddingBottom: theme.spacing.xl,
    },
    button: {
      marginBottom: theme.spacing.md,
    },
  }), [theme, dynamicTopMargin, height, width]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Header with Skip button */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
          <Text style={styles.skipText}>Skip</Text>
        </TouchableOpacity>
      </View>

      {/* Onboarding Slides */}
      <FlatList
        ref={flatListRef}
        data={onboardingScreens}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onViewableItemsChanged={handleViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
      />

      {/* Pagination Dots */}
      {renderPagination()}

      {/* Footer with buttons */}
      <View style={styles.footer}>
        {currentIndex === onboardingScreens.length - 1 ? (
          <Button
            title={isLoading ? "Loading..." : "Get Started"}
            variant="warning"
            fullWidth
            onPress={handleGetStarted}
            loading={isLoading}
            style={styles.button}
          />
        ) : (
          <Button
            title="Next"
            variant="warning"
            fullWidth
            onPress={handleNext}
            icon={<Feather name="arrow-right" size={20} color="black" />}
            style={styles.button}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default OnboardingScreen;
