"use client"

import { useState, useEffect, useCallback } from "react"
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Image
} from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Card from "../components/Card"
import Button from "../components/Button"
import { SafeAreaView } from "react-native-safe-area-context"

type JobRequestsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

// Define job request type
interface JobRequest {
  id: string
  service: string
  customer: {
    id: string
    name: string
    image: string
  }
  date: string
  time: string
  duration: string
  location: string
  distance: string
  price: number
  status: "pending" | "accepted" | "rejected"
}

const JobRequestsScreen = () => {
  const navigation = useNavigation<JobRequestsScreenNavigationProp>()
  const theme = useTheme()
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [jobRequests, setJobRequests] = useState<JobRequest[]>([])

  // Fetch job requests (mock data for now)
  useEffect(() => {
    const fetchJobRequests = async () => {
      try {
        // In a real app, this would be an API call
        // GET /bookings/pending-for-provider/:providerId
        
        // Mock data for UI development
        const mockRequests: JobRequest[] = [
          {
            id: "1",
            service: "Home Cleaning",
            customer: {
              id: "101",
              name: "Fatou Jallow",
              image: "https://randomuser.me/api/portraits/women/32.jpg"
            },
            date: "Today",
            time: "2:00 PM - 4:00 PM",
            duration: "2 hours",
            location: "Kotu, near Palma Rima Hotel",
            distance: "3.2 km",
            price: 8000,
            status: "pending"
          },
          {
            id: "2",
            service: "Deep Cleaning",
            customer: {
              id: "102",
              name: "Lamin Ceesay",
              image: "https://randomuser.me/api/portraits/men/45.jpg"
            },
            date: "Tomorrow",
            time: "10:00 AM - 1:00 PM",
            duration: "3 hours",
            location: "Bakau, New Town",
            distance: "5.7 km",
            price: 1200,
            status: "pending"
          },
          {
            id: "3",
            service: "Window Cleaning",
            customer: {
              id: "103",
              name: "Isatou Sanneh",
              image: "https://randomuser.me/api/portraits/women/22.jpg"
            },
            date: "May 20",
            time: "9:00 AM - 11:00 AM",
            duration: "2 hours",
            location: "Serrekunda, Bundung",
            distance: "4.1 km",
            price: 600,
            status: "pending"
          }
        ]
        
        setJobRequests(mockRequests)
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching job requests:", error)
        setIsLoading(false)
      }
    }
    
    fetchJobRequests()
  }, [])

  // Handle refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true)
    // Simulate data fetching
    setTimeout(() => {
      setRefreshing(false)
    }, 1500)
  }, [])

  // Handle job acceptance
  const handleAcceptJob = useCallback((jobId: string) => {
    // In a real app, this would be an API call
    // POST /bookings/:bookingId/accept
    
    setJobRequests(prevRequests => 
      prevRequests.map(request => 
        request.id === jobId 
          ? { ...request, status: "accepted" } 
          : request
      )
    )
    
    // Show success toast
    Alert.alert("Success", "You have accepted the booking")
  }, [])

  // Handle job rejection
  const handleRejectJob = useCallback((jobId: string) => {
    // In a real app, this would be an API call
    // POST /bookings/:bookingId/reject
    
    Alert.alert(
      "Reject Job",
      "Are you sure you want to reject this job request?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Reject",
          style: "destructive",
          onPress: () => {
            setJobRequests(prevRequests => 
              prevRequests.map(request => 
                request.id === jobId 
                  ? { ...request, status: "rejected" } 
                  : request
              )
            )
            
            // Show rejection toast
            Alert.alert("Job Rejected", "You have rejected the booking")
          }
        }
      ]
    )
  }, [])

  // Handle view details
  const handleViewDetails = useCallback((job: JobRequest) => {
    Alert.alert(
      `${job.service} Details`,
      `Customer: ${job.customer.name}\nDate: ${job.date}\nTime: ${job.time}\nLocation: ${job.location}\nDistance: ${job.distance}\nDuration: ${job.duration}\nPrice: D${job.price}`
    )
  }, [])

  // Render job request card
  const renderJobRequestCard = ({ item }: { item: JobRequest }) => (
    <Card style={styles.requestCard}>
      <View style={styles.requestContent}>
        <View style={styles.requestHeader}>
          <View style={styles.serviceInfo}>
            <View style={styles.serviceIconContainer}>
              <Feather 
                name={item.service.includes("Deep") ? "droplet" : item.service.includes("Window") ? "square" : "home"} 
                size={20} 
                color={theme.colors.primary} 
              />
            </View>
            <View>
              <Text style={styles.serviceType}>{item.service}</Text>
              <View style={styles.dateTimeContainer}>
                <Feather name="calendar" size={12} color={theme.colors.textLight} style={styles.smallIcon} />
                <Text style={styles.dateTimeText}>{item.date}</Text>
                <Feather name="clock" size={12} color={theme.colors.textLight} style={[styles.smallIcon, { marginLeft: 8 }]} />
                <Text style={styles.dateTimeText}>{item.time}</Text>
              </View>
            </View>
          </View>
          <Text style={styles.priceText}>D{item.price}</Text>
        </View>
        
        <View style={styles.customerSection}>
          <Image source={{ uri: item.customer.image }} style={styles.customerImage} />
          <View style={styles.customerInfo}>
            <Text style={styles.customerName}>{item.customer.name}</Text>
            <View style={styles.locationContainer}>
              <Feather name="map-pin" size={12} color={theme.colors.textLight} style={styles.smallIcon} />
              <Text style={styles.locationText} numberOfLines={1}>{item.location}</Text>
            </View>
          </View>
          <View style={styles.distanceContainer}>
            <Feather name="navigation" size={12} color={theme.colors.primary} style={styles.smallIcon} />
            <Text style={styles.distanceText}>{item.distance}</Text>
          </View>
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.actionButtons}>
          {item.status === "pending" ? (
            <>
              <Button
                title="Accept"
                variant="primary"
                size="small"
                icon={<Feather name="check" size={16} color="white" />}
                style={[styles.actionButton, styles.acceptButton]}
                onPress={() => handleAcceptJob(item.id)}
              />
              <Button
                title="Reject"
                variant="outline"
                size="small"
                icon={<Feather name="x" size={16} color={theme.colors.notification} />}
                style={styles.actionButton}
                textStyle={{ color: theme.colors.notification }}
                onPress={() => handleRejectJob(item.id)}
              />
              <TouchableOpacity 
                style={styles.detailsButton}
                onPress={() => handleViewDetails(item)}
              >
                <Text style={styles.detailsText}>Details</Text>
                <Feather name="chevron-right" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
            </>
          ) : item.status === "accepted" ? (
            <View style={styles.statusContainer}>
              <Feather name="check-circle" size={16} color={theme.colors.success} />
              <Text style={[styles.statusText, { color: theme.colors.success }]}>Accepted</Text>
            </View>
          ) : (
            <View style={styles.statusContainer}>
              <Feather name="x-circle" size={16} color={theme.colors.notification} />
              <Text style={[styles.statusText, { color: theme.colors.notification }]}>Rejected</Text>
            </View>
          )}
        </View>
      </View>
    </Card>
  )

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Feather name="inbox" size={64} color={theme.colors.border} />
      <Text style={styles.emptyTitle}>No Job Requests</Text>
      <Text style={styles.emptyText}>
        You don't have any pending job requests at the moment. Check back later or update your availability to receive more requests.
      </Text>
      <Button
        title="Update Availability"
        variant="primary"
        icon={<Feather name="calendar" size={16} color="white" />}
        onPress={() => navigation.navigate("ManageAvailability")}
        style={{ marginTop: theme.spacing.lg }}
      />
    </View>
  )

  if (isLoading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading job requests...</Text>
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Job Requests</Text>
          <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
            <Feather name="refresh-cw" size={20} color="white" />
          </TouchableOpacity>
        </View>
        
        <FlatList
          data={jobRequests.filter(job => job.status !== "rejected")}
          renderItem={renderJobRequestCard}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        />
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#6FD1FF", // Primary color for header
  },
  container: {
    flex: 1,
    backgroundColor: "#F5F5F5",
  },
  header: {
    backgroundColor: "#6FD1FF",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "white",
  },
  refreshButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },
  listContainer: {
    padding: 16,
    paddingBottom: 24,
  },
  requestCard: {
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: "white",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    overflow: "hidden",
  },
  requestContent: {
    padding: 16,
  },
  requestHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  serviceInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  serviceIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(111, 209, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  serviceType: {
    fontSize: 16,
    fontWeight: "700",
    color: "#333333",
    marginBottom: 4,
  },
  dateTimeContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  dateTimeText: {
    fontSize: 12,
    color: "#666666",
    marginLeft: 4,
  },
  smallIcon: {
    marginRight: 4,
  },
  priceText: {
    fontSize: 18,
    fontWeight: "700",
    color: "#333333",
  },
  customerSection: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  customerImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
  },
  customerInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333333",
    marginBottom: 2,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  locationText: {
    fontSize: 12,
    color: "#666666",
    flex: 1,
  },
  distanceContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(111, 209, 255, 0.1)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  distanceText: {
    fontSize: 12,
    color: "#6FD1FF",
    fontWeight: "600",
  },
  divider: {
    height: 1,
    backgroundColor: "#E1E1E1",
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    flex: 1,
    marginRight: 8,
  },
  acceptButton: {
    backgroundColor: "#52C41A",
  },
  detailsButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
  },
  detailsText: {
    fontSize: 14,
    color: "#6FD1FF",
    marginRight: 4,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  statusText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 8,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#333333",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: "#666666",
    textAlign: "center",
    marginBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
  },
  loadingText: {
    fontSize: 16,
    color: "#333333",
    marginTop: 16,
  },
});

export default JobRequestsScreen
