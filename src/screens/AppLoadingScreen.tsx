import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';

/**
 * AppLoadingScreen Component
 * 
 * Displays a loading screen while the app is initializing
 * and checking onboarding/authentication status
 */
const AppLoadingScreen: React.FC = () => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
    },
    content: {
      alignItems: 'center',
      paddingHorizontal: theme.spacing.lg,
    },
    logo: {
      fontSize: 32,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginBottom: theme.spacing.xl,
    },
    loadingText: {
      fontSize: 16,
      color: theme.colors.textLight,
      marginTop: theme.spacing.md,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={theme.colors.primary} barStyle="light-content" />
      
      <View style={styles.content}>
        <Text style={styles.logo}>CleanConnect</Text>
        
        <ActivityIndicator 
          size="large" 
          color={theme.colors.primary} 
        />
        
        <Text style={styles.loadingText}>
          Initializing app...
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default AppLoadingScreen;
