"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image, TextInput, Alert } from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Header from "../components/Header"
import Card from "../components/Card"
import Button from "../components/Button"
import Storage from "../utils/storage"
import { UserRole } from "../types/user"

type ProviderJobsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

const ProviderJobsScreen = () => {
  const navigation = useNavigation<ProviderJobsScreenNavigationProp>()
  const theme = useTheme()
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState("upcoming")
  const [searchQuery, setSearchQuery] = useState("")

  // Verify user role on component mount
  useEffect(() => {
    const verifyUserRole = async () => {
      try {
        // Get user role from both storage and AuthContext
        const userRole = await Storage.getItem("userRole")
        const isAuthenticated = await Storage.getItem("isAuthenticated")

        // Check if user is authenticated and has the provider role
        // Check both the storage value and the AuthContext user role
        const isProvider =
          (userRole === "provider") ||
          (user?.role === UserRole.PROVIDER) ||
          (await Storage.getItem("userRoleEnum") === UserRole.PROVIDER)

        console.log("Provider Jobs access check:", {
          storageRole: userRole,
          contextRole: user?.role,
          isAuthenticated: isAuthenticated
        })

        // If user is not a provider, redirect to login
        // We're being more lenient with the authentication check since we've seen issues with it
        if (!isProvider) {
          Alert.alert(
            "Access Denied",
            "You must be logged in as a cleaner to access this screen.",
            [
              {
                text: "OK",
                onPress: () => {
                  // Clear any existing auth data
                  Storage.removeItem("userRole")
                  Storage.removeItem("isAuthenticated")

                  // Redirect to shared login screen
                  navigation.reset({
                    index: 0,
                    routes: [{ name: "Login" }],
                  })
                },
              },
            ]
          )
        }
      } catch (error) {
        console.error("Error verifying user role:", error)
      }
    }

    verifyUserRole()
  }, [])

  // Dummy data for provider jobs
  const allJobs = [
    {
      id: "1",
      service: "Deep Cleaning",
      status: "Confirmed",
      date: "Today",
      time: "9:00 AM - 11:00 AM",
      address: "Kotu, near Palma Rima Hotel",
      customer: {
        name: "Fatou Jallow",
        image: "https://randomuser.me/api/portraits/women/67.jpg",
        rating: 4.8,
      },
      price: 1500,
    },
    {
      id: "2",
      service: "Regular Cleaning",
      status: "In Progress",
      date: "Today",
      time: "2:00 PM - 4:00 PM",
      address: "Bakau, New Town",
      customer: {
        name: "Lamin Ceesay",
        image: "https://randomuser.me/api/portraits/men/32.jpg",
        rating: 4.2,
      },
      price: 800,
    },
    {
      id: "3",
      service: "Window Cleaning",
      status: "Confirmed",
      date: "Tomorrow",
      time: "10:00 AM - 12:00 PM",
      address: "Serrekunda, Bundung",
      customer: {
        name: "Isatou Sanneh",
        image: "https://randomuser.me/api/portraits/women/22.jpg",
        rating: 4.5,
      },
      price: 600,
    },
    {
      id: "4",
      service: "Deep Cleaning",
      status: "Completed",
      date: "May 10, 2024",
      time: "9:00 AM - 1:00 PM",
      address: "Banjul, Wellington Street",
      customer: {
        name: "Omar Jobe",
        image: "https://randomuser.me/api/portraits/men/45.jpg",
        rating: 5.0,
      },
      price: 1800,
    },
    {
      id: "5",
      service: "Regular Cleaning",
      status: "Completed",
      date: "May 8, 2024",
      time: "3:00 PM - 5:00 PM",
      address: "Kololi, Tourism Area",
      customer: {
        name: "Mariama Bah",
        image: "https://randomuser.me/api/portraits/women/33.jpg",
        rating: 4.7,
      },
      price: 700,
    },
    {
      id: "6",
      service: "Carpet Cleaning",
      status: "Cancelled",
      date: "May 5, 2024",
      time: "11:00 AM - 1:00 PM",
      address: "Brusubi, Phase 2",
      customer: {
        name: "Modou Jallow",
        image: "https://randomuser.me/api/portraits/men/67.jpg",
        rating: 3.9,
      },
      price: 900,
    },
  ]

  // Filter jobs based on active tab and search query
  const upcomingJobs = allJobs.filter(
    (job) =>
      (job.status === "Confirmed" || job.status === "In Progress") &&
      (job.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
       job.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
       job.address.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const completedJobs = allJobs.filter(
    (job) =>
      job.status === "Completed" &&
      (job.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
       job.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
       job.address.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const cancelledJobs = allJobs.filter(
    (job) =>
      job.status === "Cancelled" &&
      (job.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
       job.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
       job.address.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  // Get jobs based on active tab
  const getFilteredJobs = () => {
    switch (activeTab) {
      case "upcoming":
        return upcomingJobs
      case "completed":
        return completedJobs
      case "cancelled":
        return cancelledJobs
      default:
        return upcomingJobs
    }
  }

  const handleStartJob = (jobId: string) => {
    Alert.alert(
      "Start Job",
      "Are you sure you want to start this job?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Start",
          onPress: () => {
            // In a real app, this would update the job status in the database
            Alert.alert("Success", "Job started successfully!")
          },
        },
      ]
    )
  }

  const handleCompleteJob = (jobId: string) => {
    Alert.alert(
      "Complete Job",
      "Are you sure you want to mark this job as completed?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Complete",
          onPress: () => {
            // In a real app, this would update the job status in the database
            Alert.alert("Success", "Job marked as completed!")
          },
        },
      ]
    )
  }

  const handleReportIssue = (jobId: string) => {
    Alert.alert(
      "Report Issue",
      "What issue would you like to report?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Customer not available",
          onPress: () => {
            // In a real app, this would create an issue report
            Alert.alert("Issue Reported", "Your issue has been reported to support.")
          },
        },
        {
          text: "Unable to access location",
          onPress: () => {
            // In a real app, this would create an issue report
            Alert.alert("Issue Reported", "Your issue has been reported to support.")
          },
        },
        {
          text: "Other issue",
          onPress: () => {
            // In a real app, this would open a form to report a custom issue
            Alert.alert("Issue Reported", "Your issue has been reported to support.")
          },
        },
      ]
    )
  }

  const handleChatPress = (customerId: string, customerName: string, customerImage: string) => {
    // Navigate to the chat screen with the customer
    navigation.navigate("Chat", {
      customerId,
      customerName,
      customerImage,
    })
  }

  const renderJobItem = ({ item }) => (
    <Card style={styles.jobCard}>
      <View style={styles.jobContent}>
        <View style={styles.jobHeader}>
          <View>
            <Text style={styles.jobTitle}>{item.service}</Text>
            <Text style={styles.jobCustomer}>{item.customer.name}</Text>
          </View>
          <View
            style={[
              styles.statusBadge,
              item.status === "Confirmed" && styles.confirmedBadge,
              item.status === "In Progress" && styles.inProgressBadge,
              item.status === "Completed" && styles.completedBadge,
              item.status === "Cancelled" && styles.cancelledBadge,
            ]}
          >
            <Text
              style={[
                styles.statusText,
                item.status === "Confirmed" && styles.confirmedText,
                item.status === "In Progress" && styles.inProgressText,
                item.status === "Completed" && styles.completedText,
                item.status === "Cancelled" && styles.cancelledText,
              ]}
            >
              {item.status}
            </Text>
          </View>
        </View>

        <View style={styles.jobInfo}>
          <View style={styles.infoRow}>
            <Feather name="calendar" size={16} style={styles.infoIcon} />
            <Text style={styles.infoText}>{item.date}</Text>
          </View>
          <View style={styles.infoRow}>
            <Feather name="clock" size={16} style={styles.infoIcon} />
            <Text style={styles.infoText}>{item.time}</Text>
          </View>
          <View style={styles.infoRow}>
            <Feather name="map-pin" size={16} style={styles.infoIcon} />
            <Text style={styles.infoText}>{item.address}</Text>
          </View>
          <View style={styles.infoRow}>
            <Feather name="dollar-sign" size={16} style={styles.infoIcon} />
            <Text style={styles.infoText}>D{item.price}</Text>
          </View>
        </View>

        {(item.status === "Confirmed" || item.status === "In Progress") && (
          <View style={styles.jobActions}>
            {item.status === "Confirmed" && (
              <Button
                title="Start Job"
                variant="primary"
                size="small"
                onPress={() => handleStartJob(item.id)}
                icon={<Feather name="play" size={16} color="white" />}
                style={styles.actionButton}
              />
            )}
            {item.status === "In Progress" && (
              <Button
                title="Mark as Completed"
                variant="success"
                size="small"
                onPress={() => handleCompleteJob(item.id)}
                icon={<Feather name="check" size={16} color="white" />}
                style={styles.actionButton}
              />
            )}
            <Button
              title="Report Issue"
              variant="outline"
              size="small"
              onPress={() => handleReportIssue(item.id)}
              icon={<Feather name="alert-circle" size={16} color={theme.colors.text} />}
              style={styles.actionButton}
            />
          </View>
        )}

        <View style={styles.customerSection}>
          <View style={styles.customerInfo}>
            <Image source={{ uri: item.customer.image }} style={styles.customerImage} />
            <View>
              <Text style={styles.customerName}>{item.customer.name}</Text>
              <View style={styles.ratingContainer}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <Feather
                    key={star}
                    name="star"
                    size={14}
                    color={star <= Math.round(item.customer.rating) ? theme.colors.warning : theme.colors.border}
                    style={styles.starIcon}
                  />
                ))}
                <Text style={styles.ratingText}>{item.customer.rating.toFixed(1)}</Text>
              </View>
            </View>
          </View>
          <TouchableOpacity
            style={styles.chatButton}
            onPress={() => handleChatPress(item.id, item.customer.name, item.customer.image)}
          >
            <Feather name="message-circle" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  )

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Feather name="calendar" size={48} style={styles.emptyIcon} />
      <Text style={styles.emptyTitle}>
        {activeTab === "upcoming"
          ? "No upcoming jobs"
          : activeTab === "completed"
            ? "No completed jobs"
            : "No cancelled jobs"}
      </Text>
      <Text style={styles.emptyText}>
        {activeTab === "upcoming"
          ? "You don't have any upcoming jobs scheduled."
          : activeTab === "completed"
            ? "You haven't completed any jobs yet."
            : "You don't have any cancelled jobs."}
      </Text>
    </View>
  )

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    searchContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: "white",
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      marginBottom: theme.spacing.md,
      height: 50,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    searchIcon: {
      color: theme.colors.textLight,
      marginRight: theme.spacing.sm,
    },
    searchInput: {
      flex: 1,
      height: "100%",
      fontSize: theme.fontSizes.md,
    },
    tabsContainer: {
      flexDirection: "row",
      backgroundColor: "white",
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
      padding: 4,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    tab: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      alignItems: "center",
      borderRadius: theme.borderRadius.sm,
    },
    activeTab: {
      backgroundColor: theme.colors.primary,
    },
    tabText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      color: theme.colors.text,
    },
    activeTabText: {
      color: "white",
    },
    jobCard: {
      marginBottom: theme.spacing.md,
    },
    jobContent: {
      padding: theme.spacing.md,
    },
    jobHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: theme.spacing.sm,
    },
    jobTitle: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      marginBottom: 2,
    },
    jobCustomer: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    statusBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 4,
      borderRadius: theme.borderRadius.sm,
    },
    confirmedBadge: {
      backgroundColor: `${theme.colors.primary}20`,
    },
    inProgressBadge: {
      backgroundColor: `${theme.colors.warning}20`,
    },
    completedBadge: {
      backgroundColor: `${theme.colors.secondary}20`,
    },
    cancelledBadge: {
      backgroundColor: `${theme.colors.notification}20`,
    },
    statusText: {
      fontSize: theme.fontSizes.xs,
      fontWeight: "600",
    },
    confirmedText: {
      color: theme.colors.primary,
    },
    inProgressText: {
      color: theme.colors.warning,
    },
    completedText: {
      color: theme.colors.secondary,
    },
    cancelledText: {
      color: theme.colors.notification,
    },
    jobInfo: {
      marginBottom: theme.spacing.md,
    },
    infoRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
    },
    infoIcon: {
      color: theme.colors.textLight,
      marginRight: theme.spacing.sm,
    },
    infoText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
    },
    jobActions: {
      flexDirection: "row",
      marginBottom: theme.spacing.md,
    },
    actionButton: {
      marginRight: theme.spacing.sm,
    },
    customerSection: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    customerInfo: {
      flexDirection: "row",
      alignItems: "center",
    },
    customerImage: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: theme.spacing.sm,
    },
    customerName: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 2,
    },
    starIcon: {
      marginRight: 2,
    },
    ratingText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      marginLeft: 2,
    },
    chatButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: `${theme.colors.primary}20`,
      justifyContent: "center",
      alignItems: "center",
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    emptyIcon: {
      color: theme.colors.textLight,
      marginBottom: theme.spacing.md,
    },
    emptyTitle: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      marginBottom: theme.spacing.sm,
      textAlign: "center",
    },
    emptyText: {
      fontSize: theme.fontSizes.md,
      color: theme.colors.textLight,
      textAlign: "center",
      marginBottom: theme.spacing.lg,
    },
  })

  return (
    <View style={styles.container}>
      <Header title="Job Management" showBackButton />

      <View style={styles.content}>
        {/* Search */}
        <View style={styles.searchContainer}>
          <Feather name="search" size={20} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search jobs..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "upcoming" && styles.activeTab]}
            onPress={() => setActiveTab("upcoming")}
          >
            <Text style={[styles.tabText, activeTab === "upcoming" && styles.activeTabText]}>Upcoming</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "completed" && styles.activeTab]}
            onPress={() => setActiveTab("completed")}
          >
            <Text style={[styles.tabText, activeTab === "completed" && styles.activeTabText]}>Completed</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "cancelled" && styles.activeTab]}
            onPress={() => setActiveTab("cancelled")}
          >
            <Text style={[styles.tabText, activeTab === "cancelled" && styles.activeTabText]}>Cancelled</Text>
          </TouchableOpacity>
        </View>

        {/* Jobs List */}
        <FlatList
          data={getFilteredJobs()}
          renderItem={renderJobItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyComponent}
          contentContainerStyle={{ flexGrow: 1 }}
        />
      </View>
    </View>
  )
}

export default ProviderJobsScreen
