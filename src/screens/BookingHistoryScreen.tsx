"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image, TextInput } from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import { getBookingHistory } from "../utils/bookingHistoryHelper"
import Header from "../components/layout/Header"
import Card from "../components/common/Card"
import Button from "../components/common/Button"

type BookingHistoryScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

const BookingHistoryScreen = () => {
  const navigation = useNavigation<BookingHistoryScreenNavigationProp>()
  const theme = useTheme()
  const [activeTab, setActiveTab] = useState("upcoming")
  const [searchQuery, setSearchQuery] = useState("")
  const [bookingHistory, setBookingHistory] = useState({ upcoming: [], past: [] })
  const [loading, setLoading] = useState(true)

  // Load booking history
  useEffect(() => {
    const loadBookingHistory = async () => {
      try {
        setLoading(true)
        const history = await getBookingHistory()
        setBookingHistory(history)
      } catch (error) {
        console.error('Error loading booking history:', error)
      } finally {
        setLoading(false)
      }
    }

    loadBookingHistory()
  }, [])

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.md,
      flex: 1,
    },
    searchContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: "white",
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    searchIcon: {
      marginRight: theme.spacing.sm,
      color: theme.colors.textLight,
    },
    searchInput: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      fontSize: theme.fontSizes.sm,
    },
    tabsContainer: {
      flexDirection: "row",
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
      backgroundColor: "white",
      overflow: "hidden",
    },
    tab: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      alignItems: "center",
    },
    activeTab: {
      backgroundColor: theme.colors.primary,
    },
    tabText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
    },
    activeTabText: {
      color: "white",
      fontWeight: "600",
    },
    bookingCard: {
      marginBottom: theme.spacing.md,
    },
    bookingContent: {
      padding: 0,
    },
    bookingDetails: {
      padding: theme.spacing.md,
    },
    bookingHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: theme.spacing.sm,
    },
    bookingTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
    },
    statusBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.sm,
    },
    confirmedBadge: {
      backgroundColor: `${theme.colors.success}20`,
    },
    scheduledBadge: {
      backgroundColor: `${theme.colors.accent}20`,
    },
    completedBadge: {
      backgroundColor: `${theme.colors.success}20`,
    },
    cancelledBadge: {
      backgroundColor: `${theme.colors.warning}20`,
    },
    statusText: {
      fontSize: theme.fontSizes.xs,
      fontWeight: "500",
    },
    confirmedText: {
      color: theme.colors.success,
    },
    scheduledText: {
      color: theme.colors.accent,
    },
    completedText: {
      color: theme.colors.success,
    },
    cancelledText: {
      color: theme.colors.warning,
    },
    bookingInfo: {
      marginBottom: theme.spacing.sm,
    },
    infoRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    infoIcon: {
      marginRight: theme.spacing.sm,
      color: theme.colors.textLight,
    },
    infoText: {
      fontSize: theme.fontSizes.sm,
    },
    bookingFooter: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      padding: theme.spacing.md,
    },
    cleanerContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    cleanerImage: {
      width: 32,
      height: 32,
      borderRadius: 16,
      marginRight: theme.spacing.sm,
    },
    cleanerName: {
      fontSize: theme.fontSizes.sm,
    },
    priceContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    price: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginRight: theme.spacing.sm,
    },
    arrowIcon: {
      color: theme.colors.textLight,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
      backgroundColor: theme.colors.cardBackground,
      borderRadius: theme.borderRadius.lg,
      marginHorizontal: theme.spacing.md,
      marginVertical: theme.spacing.lg,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    emptyIcon: {
      marginBottom: theme.spacing.md,
      color: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}15`,
      padding: theme.spacing.md,
      borderRadius: 50,
      overflow: 'hidden',
    },
    emptyTitle: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "700",
      marginBottom: theme.spacing.sm,
      textAlign: "center",
      color: theme.colors.text,
    },
    emptyText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginBottom: theme.spacing.lg,
      textAlign: "center",
      paddingHorizontal: theme.spacing.lg,
    },
    bookNowButton: {
      minWidth: 200,
      marginBottom: theme.spacing.lg,
    },
    benefitsText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
      textAlign: "center",
      paddingHorizontal: theme.spacing.xl,
      marginBottom: theme.spacing.md,
      fontStyle: "italic",
    },
  })

  // Filter bookings based on search query
  const filterBookings = (bookings) => {
    if (!searchQuery) return bookings

    return bookings.filter(
      (booking) =>
        booking.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.cleaner.name.toLowerCase().includes(searchQuery.toLowerCase()),
    )
  }

  const filteredUpcoming = filterBookings(bookingHistory.upcoming)
  const filteredPast = filterBookings(bookingHistory.past)

  const renderBookingItem = ({ item }) => (
    <TouchableOpacity onPress={() => navigation.navigate("BookingDetails", { bookingId: item.id })}>
      <Card style={styles.bookingCard}>
        <View style={styles.bookingContent}>
          <View style={styles.bookingDetails}>
            <View style={styles.bookingHeader}>
              <Text style={styles.bookingTitle}>{item.service}</Text>
              <View
                style={[
                  styles.statusBadge,
                  item.status === "Confirmed" && styles.confirmedBadge,
                  item.status === "Scheduled" && styles.scheduledBadge,
                  item.status === "Completed" && styles.completedBadge,
                  item.status === "Cancelled" && styles.cancelledBadge,
                ]}
              >
                <Text
                  style={[
                    styles.statusText,
                    item.status === "Confirmed" && styles.confirmedText,
                    item.status === "Scheduled" && styles.scheduledText,
                    item.status === "Completed" && styles.completedText,
                    item.status === "Cancelled" && styles.cancelledText,
                  ]}
                >
                  {item.status}
                </Text>
              </View>
            </View>
            <View style={styles.bookingInfo}>
              <View style={styles.infoRow}>
                <Feather name="calendar" size={16} style={styles.infoIcon} />
                <Text style={styles.infoText}>{item.date}</Text>
              </View>
              <View style={styles.infoRow}>
                <Feather name="clock" size={16} style={styles.infoIcon} />
                <Text style={styles.infoText}>{item.time}</Text>
              </View>
              <View style={styles.infoRow}>
                <Feather name="home" size={16} style={styles.infoIcon} />
                <Text style={styles.infoText}>{item.address}</Text>
              </View>
            </View>
          </View>
          <View style={styles.bookingFooter}>
            <View style={styles.cleanerContainer}>
              <Image source={{ uri: item.cleaner.image }} style={styles.cleanerImage} />
              <Text style={styles.cleanerName}>{item.cleaner.name}</Text>
            </View>
            <View style={styles.priceContainer}>
              <Text style={styles.price}>D{item.price}</Text>
              <Feather name="chevron-right" size={20} style={styles.arrowIcon} />
            </View>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  )

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      {loading ? (
        <>
          <Feather name="loader" size={48} style={[styles.emptyIcon, { color: theme.colors.primary }]} />
          <Text style={styles.emptyTitle}>Loading bookings...</Text>
        </>
      ) : (
        <>
          <Feather name={activeTab === "upcoming" ? "calendar" : "clock"} size={48} style={styles.emptyIcon} />
          <Text style={styles.emptyTitle}>{activeTab === "upcoming" ? "No upcoming bookings" : "No booking history"}</Text>
          <Text style={styles.emptyText}>
            {activeTab === "upcoming"
              ? "You don't have any upcoming cleaning services scheduled. Start by booking your first cleaning service!"
              : "You don't have any past cleaning services. Book your first cleaning service today!"}
          </Text>
          <Button
            title="Book Your First Cleaning"
            variant="primary"
            onPress={() => navigation.navigate("ServiceBrowsing")}
            style={styles.bookNowButton}
          />
          <Text style={styles.benefitsText}>
            Enjoy professional cleaning services tailored to your needs. Our trusted cleaners are ready to make your space shine!
          </Text>
        </>
      )}
    </View>
  )

  return (
    <View style={styles.container}>
      <Header title="My Bookings" showBackButton />

      <View style={styles.content}>
        {/* Search */}
        <View style={styles.searchContainer}>
          <Feather name="search" size={20} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search bookings..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "upcoming" && styles.activeTab]}
            onPress={() => setActiveTab("upcoming")}
          >
            <Text style={[styles.tabText, activeTab === "upcoming" && styles.activeTabText]}>Upcoming</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "past" && styles.activeTab]}
            onPress={() => setActiveTab("past")}
          >
            <Text style={[styles.tabText, activeTab === "past" && styles.activeTabText]}>Past</Text>
          </TouchableOpacity>
        </View>

        {/* Bookings List */}
        <FlatList
          data={activeTab === "upcoming" ? filteredUpcoming : filteredPast}
          renderItem={renderBookingItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyComponent}
          contentContainerStyle={{ flexGrow: 1 }}
        />
      </View>
    </View>
  )
}

export default BookingHistoryScreen
