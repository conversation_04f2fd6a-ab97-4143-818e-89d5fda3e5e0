"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from "react-native"
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import { services } from "../data/services"
import Header from "../components/Header"
import ServiceCard from "../components/ServiceCard"
import ServiceListItem from "../components/ServiceListItem"

type ServiceBrowsingScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>
type ServiceBrowsingScreenRouteProp = RouteProp<RootStackParamList, 'ServiceBrowsing'>

const ServiceBrowsingScreen = () => {
  const navigation = useNavigation<ServiceBrowsingScreenNavigationProp>()
  const route = useRoute<ServiceBrowsingScreenRouteProp>()
  const theme = useTheme()
  const [activeCategory, setActiveCategory] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")

  // Handle search query from route params
  useEffect(() => {
    if (route.params?.searchQuery) {
      // Make sure searchQuery is a string
      const query = typeof route.params.searchQuery === 'string'
        ? route.params.searchQuery
        : ''
      setSearchQuery(query)
    }
  }, [route.params])

  const categories = [
    { id: "all", name: "All" },
    { id: "regular", name: "Regular" },
    { id: "deep", name: "Deep Clean" },
    { id: "movein", name: "Move-in/out" },
    { id: "window", name: "Windows" },
  ]

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.md,
    },
    searchFilterContainer: {
      flexDirection: "row",
      marginBottom: theme.spacing.md,
    },
    searchContainer: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: "white",
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.xs,
      marginRight: theme.spacing.sm,
    },
    searchIcon: {
      marginRight: theme.spacing.sm,
      color: theme.colors.textLight,
    },
    searchInput: {
      flex: 1,
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
    },
    filterButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      borderWidth: 1,
      marginTop: 5,
      borderColor: theme.colors.border,
      backgroundColor: "white",
      justifyContent: "center",
      alignItems: "center",
    },
    categoriesContainer: {
      marginBottom: theme.spacing.md,
    },
    categoriesList: {
      flexDirection: "row",
      paddingBottom: theme.spacing.sm,
    },
    categoryButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.round,
      marginRight: theme.spacing.sm,
      backgroundColor: "transparent",
    },
    categoryButtonActive: {
      backgroundColor: theme.colors.primary,
    },
    categoryText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.text,
    },
    categoryTextActive: {
      color: "white",
    },
    featuredService: {
      marginBottom: theme.spacing.md,
    },
    servicesList: {
      marginTop: theme.spacing.md,
    },
  })

  const handleServicePress = (serviceId: string) => {
    navigation.navigate("ServiceDetail", { serviceId })
  }

  // Process services to ensure reviews is a number (not an array of review objects)
  const processedServices = services.map(service => ({
    ...service,
    // If reviews is an array, use its length as the reviews count
    reviews: Array.isArray(service.reviews) ? service.reviews.length : service.reviewCount || 0
  }))

  const filteredServices = processedServices.filter((service) => {
    // Filter by search query
    if (searchQuery) {
      return (
        service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by category
    if (activeCategory === "all") return true
    if (activeCategory === "regular" && service.title.includes("Regular")) return true
    if (activeCategory === "deep" && service.title.includes("Deep")) return true
    if (activeCategory === "movein" && service.title.includes("Move")) return true
    if (activeCategory === "window" && service.title.includes("Window")) return true

    return false
  })

  const featuredServices = filteredServices.filter((service) => service.featured)
  const regularServices = filteredServices.filter((service) => !service.featured)

  return (
    <View style={styles.container}>
      <Header title="Cleaning Services" showBackButton />

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Search and Filter */}
          <View style={styles.searchFilterContainer}>
            <View style={styles.searchContainer}>
              <Feather name="search" size={20} style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search services..."
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>
            <TouchableOpacity style={styles.filterButton}>
              <Feather name="filter" size={20} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          {/* Categories */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesContainer}>
            <View style={styles.categoriesList}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[styles.categoryButton, activeCategory === category.id && styles.categoryButtonActive]}
                  onPress={() => setActiveCategory(category.id)}
                >
                  <Text style={[styles.categoryText, activeCategory === category.id && styles.categoryTextActive]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>

          {/* Featured Services */}
          {featuredServices.map((service) => (
            <ServiceCard
              key={service.id}
              id={service.id}
              title={service.title}
              description={service.description}
              price={service.price}
              duration={service.duration}
              rating={service.rating}
              reviews={service.reviews}
              image={service.image}
              featured={true}
              onPress={() => handleServicePress(service.id)}
            />
          ))}

          {/* Regular Services */}
          <View style={styles.servicesList}>
            {regularServices.map((service) => (
              <ServiceListItem
                key={service.id}
                id={service.id}
                title={service.title}
                description={service.description}
                price={service.price}
                duration={service.duration}
                rating={service.rating}
                reviews={service.reviews}
                image={service.image}
                onPress={() => handleServicePress(service.id)}
              />
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  )
}

export default ServiceBrowsingScreen
