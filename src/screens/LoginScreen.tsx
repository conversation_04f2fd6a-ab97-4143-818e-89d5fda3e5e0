import { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  TextInput,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Animated,
  Alert,
} from "react-native";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Feather } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import { useAuth } from "../context/AuthContext";
import { useBooking } from "../context/BookingContext";
import type { RootStackParamList } from "../navigation/RootNavigator";
import { Button } from "../components/common";
import { ToastService } from "../components/common";
import Storage from "../utils/storage";
import { localAuthService } from "../services/localAuthService";
import * as SecureStore from 'expo-secure-store';

type LoginScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;
type LoginScreenRouteProp = RouteProp<RootStackParamList, 'Login'>;

const LoginScreen = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const route = useRoute<LoginScreenRouteProp>();
  const theme = useTheme();
  const { setUser, setIsAuthenticated } = useAuth();
  const { restoreBookingData } = useBooking();

  // Form state
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [phone, setPhone] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Form validation
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [formTouched, setFormTouched] = useState(false);
  const [loginError, setLoginError] = useState("");

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);

  // Animation values
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  // Refs
  const passwordInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const phoneInputRef = useRef<TextInput>(null);

  // Start shake animation for error
  const startShakeAnimation = () => {
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();
  };

  // Handle keyboard events
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        // Handle keyboard show if needed
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        // Handle keyboard hide if needed
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Load user role from storage
  useEffect(() => {
    const loadUserRole = async () => {
      try {
        const role = await Storage.getItem("userRole");
        setUserRole(role);

        // Check if remember me was enabled
        const rememberedEmail = await SecureStore.getItemAsync('cached_email');
        const rememberedPhone = await SecureStore.getItemAsync('cached_phone');
        const rememberMeEnabled = await SecureStore.getItemAsync('remember_me');

        if (rememberMeEnabled === 'true') {
          if (role === 'provider' && rememberedEmail) {
            setEmail(rememberedEmail);
            setRememberMe(true);
          } else if (role === 'customer' && rememberedPhone) {
            setPhone(rememberedPhone);
            setRememberMe(true);
          }
        }
      } catch (error) {
        console.error("Error loading user role:", error);
      }
    };

    loadUserRole();
  }, []);

  // Validate email for providers
  const validateEmail = (email: string): boolean => {
    // Skip email validation for customers
    if (userRole === 'customer') {
      setEmailError("");
      return true;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError("Email is required");
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError("Please enter a valid email address");
      return false;
    } else {
      setEmailError("");
      return true;
    }
  };

  // Validate phone for customers
  const validatePhone = (phone: string): boolean => {
    // Skip phone validation for providers
    if (userRole === 'provider') {
      setPhoneError("");
      return true;
    }

    const phoneRegex = /^\d{10}$/;
    if (!phone) {
      setPhoneError("Phone number is required");
      return false;
    } else if (!phoneRegex.test(phone.replace(/\D/g, ''))) {
      setPhoneError("Please enter a valid 10-digit phone number");
      return false;
    } else {
      setPhoneError("");
      return true;
    }
  };

  // Validate password for providers
  const validatePassword = (password: string): boolean => {
    // Skip password validation for customers
    if (userRole === 'customer') {
      setPasswordError("");
      return true;
    }

    if (!password) {
      setPasswordError("Password is required");
      return false;
    } else if (password.length < 6) {
      setPasswordError("Password must be at least 6 characters");
      return false;
    } else {
      setPasswordError("");
      return true;
    }
  };

  const handleLogin = async () => {
    // Set form as touched to show validation errors
    setFormTouched(true);

    // Clear any previous login errors
    setLoginError("");

    try {
      setIsLoading(true);

      // Prepare login data based on user role
      const loginData: any = {
        role: userRole === 'provider' ? 'PROVIDER' : 'CUSTOMER'
      };

      if (userRole === 'provider') {
        // Validate form for providers
        const isEmailValid = validateEmail(email);
        const isPasswordValid = validatePassword(password);

        if (!isEmailValid || !isPasswordValid) {
          // Don't proceed if validation fails
          startShakeAnimation();
          setIsLoading(false);
          return;
        }

        // Provider login with email/password
        loginData.email = email.trim();
        loginData.password = password;

        // Log the credentials being used (password masked for security)
        console.log('Provider login attempt with:', { email, passwordLength: password.length });
      } else {
        // Validate form for customers
        const isPhoneValid = validatePhone(phone);

        if (!isPhoneValid) {
          // Don't proceed if validation fails
          startShakeAnimation();
          setIsLoading(false);
          return;
        }

        // Customer login with phone
        loginData.phone = phone.trim();

        // Log the credentials being used
        console.log('Customer login attempt with phone:', { phone });
      }

      // Dismiss keyboard
      Keyboard.dismiss();

      // Call login through local auth service
      const response = await localAuthService.login(loginData);

      console.log('Login response:', response);

      if (!response.success) {
        // Show specific error message
        const errorMessage = response.error || "Please check your credentials and try again.";
        setLoginError(errorMessage);
        startShakeAnimation();
        setIsLoading(false);
        return;
      }

      // Login successful - proceed with navigation

      // If we have a response with a user, login was successful
      if (response.user) {
        console.log('Login successful!');

        // Store remember me preference
        if (rememberMe) {
          if (userRole === 'provider') {
            await Storage.setItem('cached_email', email.trim());
            await SecureStore.setItemAsync('cached_email', email.trim());
          } else {
            await Storage.setItem('cached_phone', phone.trim());
            await SecureStore.setItemAsync('cached_phone', phone.trim());
          }
          await SecureStore.setItemAsync('remember_me', 'true');
        } else {
          // Clear remembered credentials if remember me is not checked
          await SecureStore.deleteItemAsync('remember_me');
          await SecureStore.deleteItemAsync('cached_email');
          await SecureStore.deleteItemAsync('cached_phone');
        }

        // Set the user and authentication state in the AuthContext
        setUser(response.user);
        setIsAuthenticated(true);

        // Store tokens in secure storage
        if (response.tokens) {
          await SecureStore.setItemAsync('accessToken', response.tokens.accessToken);
          await SecureStore.setItemAsync('refreshToken', response.tokens.refreshToken);
        }

        // Store user data in secure storage
        await SecureStore.setItemAsync('user_data', JSON.stringify(response.user));
        await SecureStore.setItemAsync('isAuthenticated', 'true');

        // Store the user role
        if (response.user.role) {
          await SecureStore.setItemAsync('userRoleEnum', response.user.role);
          await SecureStore.setItemAsync('userRole',
            response.user.role === 'PROVIDER' ? 'provider' : 'customer');
        }

        // Check if we need to return to booking or payment screen
        if (route.params?.returnTo) {
          console.log(`Login successful, returning to ${route.params.returnTo} screen`);

          // Show success toast
          ToastService.show({
            type: 'success',
            text1: 'Login Successful!',
            text2: 'Returning to your booking...',
            visibilityTime: 2000,
            autoHide: true,
          });

          // Restore booking data if we're coming from a booking flow
          if (route.params?.fromBooking) {
            try {
              const restored = await restoreBookingData();
              console.log('Booking data restored:', restored);

              if (!restored) {
                console.warn('Failed to restore booking data, but continuing with navigation');
              }
            } catch (restoreError) {
              console.error('Error restoring booking data:', restoreError);
              // Continue with navigation even if restore fails
            }
          }

          // Short delay for better UX
          setTimeout(() => {
            // Navigate back to the appropriate screen
            if (route.params.returnTo === 'PaymentOption') {
              // For payment screen, we need to pass the return parameters
              console.log('Navigating to PaymentOption with params:', route.params?.returnParams);
              navigation.navigate(route.params.returnTo as any, route.params?.returnParams || {});
            } else {
              // For other screens, just navigate there
              navigation.navigate(route.params.returnTo as any);
            }
          }, 500);

          return;
        }

        // Determine which screen to navigate to based on user role
        const userRoleValue = response.data?.user?.role;
        if (userRoleValue === 'PROVIDER') {
          // Navigate to provider dashboard
          navigation.reset({
            index: 0,
            routes: [{ name: 'Main' as any }],
          });
        } else {
          // Navigate to customer dashboard
          navigation.reset({
            index: 0,
            routes: [{ name: 'Main' as any }],
          });
        }

        return;
      }

      // If we get here, something went wrong but didn't throw an error
      setLoginError("An unexpected error occurred. Please try again.");
      startShakeAnimation();
    } catch (error: any) {
      console.error("Login error:", error);

      // Show user-friendly error message
      setLoginError(error.message || "An unexpected error occurred. Please try again later.");
      startShakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = async (provider: string) => {
    // This is a placeholder for social login functionality
    // In a real app, you would implement OAuth with the respective provider
    Alert.alert(
      "Social Login",
      `${provider} login is not implemented yet.`,
      [{ text: "OK" }]
    );
  };

  const handleSignUp = () => {
    navigation.navigate("RoleSelection");
  };

  const handleForgotPassword = () => {
    navigation.navigate("ForgotPassword");
  };

  // Function to switch to cleaner login
  const switchToCleanerLogin = async () => {
    try {
      await Storage.setItem("userRole", "provider");
      setUserRole("provider");
    } catch (error) {
      console.error("Error switching to cleaner login:", error);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      flexDirection: 'row',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.card,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    scrollContent: {
      flexGrow: 1,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    centeredContent: {
      alignItems: "center",
      marginTop: theme.spacing.xl,
      marginBottom: theme.spacing.xl,
    },
    formContainer: {
      width: '100%',
      maxWidth: 400,
      alignSelf: 'center',
    },
    logo: {
      height: 120,
      width: 120,
      marginBottom: theme.spacing.lg,
      borderRadius: 60,
    },
    title: {
      fontSize: 28,
      fontWeight: "700",
      marginBottom: theme.spacing.sm,
      textAlign: "center",
    },
    subtitle: {
      fontSize: theme.fontSizes.md,
      marginBottom: theme.spacing.xl,
      maxWidth: 300,
      textAlign: "center",
    },
    inputContainer: {
      marginBottom: theme.spacing.md,
    },
    label: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      marginBottom: theme.spacing.xs,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      overflow: 'hidden',
    },
    inputIcon: {
      paddingHorizontal: theme.spacing.sm,
    },
    textInput: {
      flex: 1,
      fontSize: theme.fontSizes.md,
      padding: theme.spacing.sm,
      height: 48,
    },
    passwordToggle: {
      padding: theme.spacing.sm,
    },
    errorText: {
      color: theme.colors.error || '#ff3b30',
      fontSize: theme.fontSizes.xs,
      marginTop: 4,
    },
    loginErrorContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: "#FFEBEE",
      padding: 12,
      borderRadius: 8,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: "#FFCDD2",
    },
    errorIcon: {
      marginRight: 8,
    },
    loginErrorText: {
      fontSize: theme.fontSizes.sm,
      flex: 1,
    },
    rememberMeContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    checkboxContainer: {
      marginRight: theme.spacing.sm,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderRadius: 4,
      borderWidth: 2,
      alignItems: "center",
      justifyContent: "center",
    },
    rememberMeText: {
      fontSize: theme.fontSizes.sm,
    },
    forgotPasswordContainer: {
      alignItems: "flex-end",
      marginBottom: theme.spacing.md,
    },
    forgotPasswordText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
    },
    offlineContainer: {
      marginTop: theme.spacing.md,
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      flexDirection: 'row',
      alignItems: 'center',
    },
    offlineIcon: {
      marginRight: theme.spacing.sm,
    },
    offlineText: {
      fontSize: theme.fontSizes.sm,
      flex: 1,
    },
    dividerContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginVertical: theme.spacing.lg,
    },
    divider: {
      flex: 1,
      height: 1,
    },
    dividerText: {
      fontSize: theme.fontSizes.sm,
      marginHorizontal: theme.spacing.sm,
    },
    socialButtonsContainer: {
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: theme.spacing.lg,
    },
    socialButton: {
      width: 48,
      height: 48,
      borderRadius: theme.borderRadius.md,
      alignItems: "center",
      justifyContent: "center",
      marginHorizontal: theme.spacing.xs,
    },
    signUpText: {
      fontSize: theme.fontSizes.sm,
      textAlign: "center",
      marginTop: theme.spacing.md,
    },
    signUpLink: {
      fontWeight: "500",
    },
    cleanerLoginContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      marginTop: theme.spacing.lg,
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
    },
    cleanerIcon: {
      marginRight: theme.spacing.sm,
    },
    cleanerLoginText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
    },
  });

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar backgroundColor={theme.colors.background} barStyle="dark-content" />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          accessibilityLabel="Back button"
        >
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
        <View style={styles.content}>
          <View style={styles.centeredContent}>
            <Image
              source={{ uri: "https://picsum.photos/id/24/200/200" }}
              style={styles.logo}
            />
            <Text style={[styles.title, { color: theme.colors.text }]}>
              {userRole === "provider" ? "Cleaner Login" : "Welcome Back"}
            </Text>
            <Text style={[styles.subtitle, { color: theme.colors.textLight }]}>
              Sign in to {userRole === "provider" ? "your cleaner account" : "continue using CleanConnect"}
            </Text>
          </View>

          <Animated.View
            style={[
              styles.formContainer,
              { transform: [{ translateX: shakeAnimation }] }
            ]}
          >
            {/* Login error message */}
            {loginError ? (
              <View style={styles.loginErrorContainer}>
                <Feather name="alert-circle" size={18} color={theme.colors.error} style={styles.errorIcon} />
                <Text style={[styles.loginErrorText, { color: theme.colors.error }]}>
                  {loginError}
                </Text>
              </View>
            ) : null}

            {userRole === 'provider' ? (
              <>
                {/* Email Input for Providers */}
                <View style={styles.inputContainer}>
                  <Text style={[styles.label, { color: theme.colors.text }]}>Email</Text>
                  <View style={[styles.inputWrapper, {
                    backgroundColor: theme.colors.card,
                    borderColor: formTouched && emailError ? theme.colors.error : theme.colors.border,
                  }]}>
                    <Feather name="mail" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                    <TextInput
                      ref={emailInputRef}
                      style={[styles.textInput, { color: theme.colors.text }]}
                      placeholder="Enter your email"
                      placeholderTextColor={theme.colors.textLight}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      value={email}
                      onChangeText={(text) => {
                        setEmail(text);
                        if (formTouched) validateEmail(text);
                        // Clear login error when user types
                        if (loginError) setLoginError("");
                      }}
                      onBlur={() => {
                        setFormTouched(true);
                        validateEmail(email);
                      }}
                      returnKeyType="next"
                      onSubmitEditing={() => passwordInputRef.current?.focus()}
                      accessibilityLabel="Email input field"
                    />
                  </View>
                  {formTouched && emailError ? (
                    <Text style={styles.errorText}>{emailError}</Text>
                  ) : null}
                </View>

                {/* Password Input for Providers */}
                <View style={styles.inputContainer}>
                  <Text style={[styles.label, { color: theme.colors.text }]}>Password</Text>
                  <View style={[styles.inputWrapper, {
                    backgroundColor: theme.colors.card,
                    borderColor: formTouched && passwordError ? theme.colors.error : theme.colors.border,
                  }]}>
                    <Feather name="lock" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                    <TextInput
                      ref={passwordInputRef}
                      style={[styles.textInput, { color: theme.colors.text }]}
                      placeholder="Enter your password"
                      placeholderTextColor={theme.colors.textLight}
                      secureTextEntry={!showPassword}
                      value={password}
                      onChangeText={(text) => {
                        setPassword(text);
                        if (formTouched) validatePassword(text);
                        // Clear login error when user types
                        if (loginError) setLoginError("");
                      }}
                      onBlur={() => {
                        setFormTouched(true);
                        validatePassword(password);
                      }}
                      returnKeyType="done"
                      onSubmitEditing={handleLogin}
                      accessibilityLabel="Password input field"
                    />
                    <TouchableOpacity
                      style={styles.passwordToggle}
                      onPress={() => setShowPassword(!showPassword)}
                      accessibilityLabel={showPassword ? "Hide password" : "Show password"}
                    >
                      <Feather
                        name={showPassword ? "eye-off" : "eye"}
                        size={20}
                        color={theme.colors.textLight}
                      />
                    </TouchableOpacity>
                  </View>
                  {formTouched && passwordError ? (
                    <Text style={styles.errorText}>{passwordError}</Text>
                  ) : null}
                </View>
              </>
            ) : (
              <>
                {/* Phone Input for Customers */}
                <View style={styles.inputContainer}>
                  <Text style={[styles.label, { color: theme.colors.text }]}>Phone Number</Text>
                  <View style={[styles.inputWrapper, {
                    backgroundColor: theme.colors.card,
                    borderColor: formTouched && phoneError ? theme.colors.error : theme.colors.border,
                  }]}>
                    <Feather name="phone" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                    <TextInput
                      ref={phoneInputRef}
                      style={[styles.textInput, { color: theme.colors.text }]}
                      placeholder="Enter your 10-digit phone number"
                      placeholderTextColor={theme.colors.textLight}
                      keyboardType="phone-pad"
                      value={phone}
                      onChangeText={(text) => {
                        setPhone(text);
                        if (formTouched) validatePhone(text);
                        // Clear login error when user types
                        if (loginError) setLoginError("");
                      }}
                      onBlur={() => {
                        setFormTouched(true);
                        validatePhone(phone);
                      }}
                      returnKeyType="done"
                      onSubmitEditing={handleLogin}
                      accessibilityLabel="Phone number input field"
                    />
                  </View>
                  {formTouched && phoneError ? (
                    <Text style={styles.errorText}>{phoneError}</Text>
                  ) : null}
                </View>
              </>
            )}

            <View style={styles.rememberMeContainer}>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setRememberMe(!rememberMe)}
                accessibilityRole="checkbox"
                accessibilityState={{ checked: rememberMe }}
                accessibilityLabel="Remember me checkbox"
              >
                <View
                  style={[
                    styles.checkbox,
                    { borderColor: theme.colors.border },
                    rememberMe && {
                      backgroundColor: theme.colors.primary,
                      borderColor: theme.colors.primary
                    },
                  ]}
                >
                  {rememberMe && (
                    <Feather name="check" size={14} color="white" />
                  )}
                </View>
              </TouchableOpacity>
              <Text style={[styles.rememberMeText, { color: theme.colors.textLight }]}>
                Remember me
              </Text>
            </View>

            <TouchableOpacity
              style={styles.forgotPasswordContainer}
              onPress={handleForgotPassword}
              accessibilityLabel="Forgot password button"
              accessibilityHint="Navigate to password reset screen"
            >
              <Text style={[styles.forgotPasswordText, { color: theme.colors.primary }]}>
                Forgot Password?
              </Text>
            </TouchableOpacity>

            <Button
              title={isLoading ? "Signing in..." : "Sign In"}
              variant="warning"
              onPress={handleLogin}
              fullWidth
              loading={isLoading}
              disabled={isLoading || (formTouched && (
                (userRole === 'provider' && (!!emailError || !!passwordError)) ||
                (userRole === 'customer' && !!phoneError)
              ))}
              accessibilityLabel="Sign in button"
              accessibilityHint="Signs you into your account"
            />
          </Animated.View>



          <View style={styles.dividerContainer}>
            <View style={[styles.divider, { backgroundColor: theme.colors.border }]} />
            <Text style={[styles.dividerText, { color: theme.colors.textLight }]}>OR</Text>
            <View style={[styles.divider, { backgroundColor: theme.colors.border }]} />
          </View>

          <View style={styles.socialButtonsContainer}>
            <TouchableOpacity
              style={[styles.socialButton, { backgroundColor: theme.colors.card }]}
              onPress={() => handleSocialLogin("Google")}
              accessibilityLabel="Sign in with Google"
            >
              <Feather name="mail" size={20} color="#DB4437" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.socialButton, { backgroundColor: '#3b5998' }]}
              onPress={() => handleSocialLogin("Facebook")}
              accessibilityLabel="Sign in with Facebook"
            >
              <Feather name="facebook" size={20} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.socialButton, { backgroundColor: '#000000' }]}
              onPress={() => handleSocialLogin("Apple")}
              accessibilityLabel="Sign in with Apple"
            >
              <Feather name="smartphone" size={20} color="white" />
            </TouchableOpacity>
          </View>

          <Text style={[styles.signUpText, { color: theme.colors.textLight }]}>
            Don&apos;t have an account?{" "}
            <Text
              style={[styles.signUpLink, { color: theme.colors.primary }]}
              onPress={handleSignUp}
            >
              Sign up
            </Text>
          </Text>

          {userRole !== "provider" && (
            <TouchableOpacity
              style={[styles.cleanerLoginContainer, { borderColor: theme.colors.border }]}
              onPress={switchToCleanerLogin}
              accessibilityLabel="Switch to cleaner login"
            >
              <Feather name="user" size={16} color={theme.colors.primary} style={styles.cleanerIcon} />
              <Text style={[styles.cleanerLoginText, { color: theme.colors.primary }]}>
                Switch to Cleaner Login
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>


  </SafeAreaView>
  );
};

export default LoginScreen;
