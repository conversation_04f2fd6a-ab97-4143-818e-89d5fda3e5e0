"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, TextInput } from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Header from "../components/layout/Header"
import Card from "../components/common/Card"
import Button from "../components/common/Button"

type RatingsReviewsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

const RatingsReviewsScreen = () => {
  const navigation = useNavigation<RatingsReviewsScreenNavigationProp>()
  const theme = useTheme()
  const [activeTab, setActiveTab] = useState("pending")
  const [rating, setRating] = useState(0)
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [selectedBooking, setSelectedBooking] = useState<any>(null)

  // Dummy reviews data
  const pendingReviews = [
    {
      id: 1,
      service: "Deep Cleaning",
      date: "May 5, 2023",
      cleaner: {
        name: "Mariama Jallow",
        image: "https://randomuser.me/api/portraits/women/44.jpg",
      },
    },
  ]

  const myReviews = [
    {
      id: 1,
      service: "Regular Cleaning",
      date: "April 28, 2023",
      rating: 5,
      comment:
        "Sarah did an excellent job cleaning my apartment. Everything was spotless and she was very professional. Will definitely book again!",
      cleaner: {
        name: "Sarah Williams",
        image: "https://randomuser.me/api/portraits/women/33.jpg",
      },
    },
    {
      id: 2,
      service: "Window Cleaning",
      date: "April 15, 2023",
      rating: 4,
      comment:
        "David did a good job with the windows. They look much better now. Just missed a few spots on the higher windows.",
      cleaner: {
        name: "David Smith",
        image: "https://randomuser.me/api/portraits/men/22.jpg",
      },
    },
  ]

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.md,
    },
    tabsContainer: {
      flexDirection: "row",
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
      backgroundColor: "white",
      overflow: "hidden",
    },
    tab: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      alignItems: "center",
    },
    activeTab: {
      backgroundColor: theme.colors.primary,
    },
    tabText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
    },
    activeTabText: {
      color: "white",
      fontWeight: "600",
    },
    reviewCard: {
      marginBottom: theme.spacing.md,
      padding: theme.spacing.md,
    },
    reviewHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: theme.spacing.md,
    },
    cleanerContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    cleanerImage: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: theme.spacing.sm,
    },
    cleanerInfo: {
      flex: 1,
    },
    cleanerName: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
    },
    serviceInfo: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    ratingContainer: {
      flexDirection: "row",
      marginBottom: theme.spacing.sm,
    },
    star: {
      marginRight: 2,
    },
    reviewText: {
      fontSize: theme.fontSizes.sm,
      lineHeight: 20,
    },
    emptyContainer: {
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    emptyIcon: {
      marginBottom: theme.spacing.md,
      color: theme.colors.textLight,
    },
    emptyTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.sm,
      textAlign: "center",
    },
    emptyText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginBottom: theme.spacing.lg,
      textAlign: "center",
    },
    modalContainer: {
      flex: 1,
      backgroundColor: "white",
    },
    modalContent: {
      padding: theme.spacing.md,
    },
    modalTitle: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      marginBottom: theme.spacing.sm,
    },
    modalSubtitle: {
      fontSize: theme.fontSizes.md,
      color: theme.colors.textLight,
      marginBottom: theme.spacing.lg,
    },
    ratingStarsContainer: {
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: theme.spacing.lg,
    },
    ratingStar: {
      padding: theme.spacing.xs,
    },
    commentInput: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      fontSize: theme.fontSizes.md,
      minHeight: 120,
      textAlignVertical: "top",
      marginBottom: theme.spacing.lg,
    },
  })

  const handleLeaveReview = (booking) => {
    setSelectedBooking(booking)
    setRating(0)
    setShowReviewModal(true)
  }

  const handleSubmitReview = () => {
    // Handle submit review logic
    setShowReviewModal(false)
  }

  const renderReviewModal = () => {
    if (!showReviewModal || !selectedBooking) return null

    return (
      <View style={styles.modalContainer}>
        <Header
          title="Rate Your Experience"
          showBackButton
          rightIcon={
            <TouchableOpacity onPress={() => setShowReviewModal(false)}>
              <Feather name="x" size={24} color="white" />
            </TouchableOpacity>
          }
        />

        <ScrollView style={styles.modalContent}>
          <Text style={styles.modalSubtitle}>How was your cleaning service with {selectedBooking.cleaner.name}?</Text>

          <View style={styles.ratingStarsContainer}>
            {[1, 2, 3, 4, 5].map((star) => (
              <TouchableOpacity key={star} style={styles.ratingStar} onPress={() => setRating(star)}>
                <Feather name="star" size={36} color={star <= rating ? theme.colors.warning : theme.colors.border} />
              </TouchableOpacity>
            ))}
          </View>

          <TextInput style={styles.commentInput} placeholder="Share your experience with this service..." multiline />

          <Button title="Submit Review" variant="warning" disabled={rating === 0} onPress={handleSubmitReview} />
        </ScrollView>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {showReviewModal ? (
        renderReviewModal()
      ) : (
        <>
          <Header title="Ratings & Reviews" showBackButton />

          <View style={styles.content}>
            {/* Tabs */}
            <View style={styles.tabsContainer}>
              <TouchableOpacity
                style={[styles.tab, activeTab === "pending" && styles.activeTab]}
                onPress={() => setActiveTab("pending")}
              >
                <Text style={[styles.tabText, activeTab === "pending" && styles.activeTabText]}>Pending</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, activeTab === "my-reviews" && styles.activeTab]}
                onPress={() => setActiveTab("my-reviews")}
              >
                <Text style={[styles.tabText, activeTab === "my-reviews" && styles.activeTabText]}>My Reviews</Text>
              </TouchableOpacity>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
              {activeTab === "pending" ? (
                pendingReviews.length > 0 ? (
                  pendingReviews.map((review) => (
                    <Card key={review.id} style={styles.reviewCard}>
                      <View style={styles.reviewHeader}>
                        <View style={styles.cleanerContainer}>
                          <Image source={{ uri: review.cleaner.image }} style={styles.cleanerImage} />
                          <View style={styles.cleanerInfo}>
                            <Text style={styles.cleanerName}>{review.cleaner.name}</Text>
                            <Text style={styles.serviceInfo}>
                              {review.service} • {review.date}
                            </Text>
                          </View>
                        </View>
                        <Button
                          title="Leave Review"
                          variant="warning"
                          size="small"
                          onPress={() => handleLeaveReview(review)}
                        />
                      </View>
                    </Card>
                  ))
                ) : (
                  <View style={styles.emptyContainer}>
                    <Feather name="star" size={48} style={styles.emptyIcon} />
                    <Text style={styles.emptyTitle}>No pending reviews</Text>
                    <Text style={styles.emptyText}>You don't have any services to review at the moment.</Text>
                  </View>
                )
              ) : myReviews.length > 0 ? (
                myReviews.map((review) => (
                  <Card key={review.id} style={styles.reviewCard}>
                    <View style={styles.reviewHeader}>
                      <View style={styles.cleanerContainer}>
                        <Image source={{ uri: review.cleaner.image }} style={styles.cleanerImage} />
                        <View style={styles.cleanerInfo}>
                          <Text style={styles.cleanerName}>{review.cleaner.name}</Text>
                          <Text style={styles.serviceInfo}>
                            {review.service} • {review.date}
                          </Text>
                        </View>
                      </View>
                      <TouchableOpacity>
                        <Feather name="edit-2" size={20} color={theme.colors.textLight} />
                      </TouchableOpacity>
                    </View>
                    <View style={styles.ratingContainer}>
                      {[...Array(5)].map((_, i) => (
                        <Feather
                          key={i}
                          name="star"
                          size={16}
                          style={styles.star}
                          color={i < review.rating ? theme.colors.warning : theme.colors.border}
                        />
                      ))}
                    </View>
                    <Text style={styles.reviewText}>{review.comment}</Text>
                  </Card>
                ))
              ) : (
                <View style={styles.emptyContainer}>
                  <Feather name="star" size={48} style={styles.emptyIcon} />
                  <Text style={styles.emptyTitle}>No reviews yet</Text>
                  <Text style={styles.emptyText}>You haven't left any reviews for your cleaning services.</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </>
      )}
    </View>
  )
}

export default RatingsReviewsScreen
