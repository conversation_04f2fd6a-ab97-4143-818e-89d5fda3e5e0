"use client"

import React, { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Button from "../components/Button"
import Storage from "../utils/storage"
import { UserRole } from "../types/user"
import { STORAGE_KEYS } from "../config/constants"

type CleanerRegistrationScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

const CleanerRegistrationScreen = () => {
  const navigation = useNavigation<CleanerRegistrationScreenNavigationProp>()
  const theme = useTheme()
  const { setUserRole, setIsAuthenticated } = useAuth()
  const [fullName, setFullName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [termsAccepted, setTermsAccepted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Get styles with the current theme
  const styles = getStyles(theme)

  const handleRegister = async () => {
    // Validate inputs
    if (!fullName || !email || !phone || !password || !confirmPassword) {
      Alert.alert("Missing Information", "Please fill in all fields.")
      return
    }

    if (password !== confirmPassword) {
      Alert.alert("Password Mismatch", "Passwords do not match. Please try again.")
      return
    }

    if (!termsAccepted) {
      Alert.alert("Terms Required", "Please accept the terms and conditions to continue.")
      return
    }

    setIsLoading(true)

    try {
      // In a real app, this would be an API call to register the user
      // For demo purposes, we'll simulate a successful registration after a short delay
      setTimeout(async () => {
        try {
          // Create a mock user object
          const mockUser = {
            id: 'user-' + Date.now(),
            email: email,
            firstName: fullName.split(' ')[0],
            lastName: fullName.includes(' ') ? fullName.split(' ').slice(1).join(' ') : '',
            role: UserRole.PROVIDER
          };

          // Store the mock user in secure storage
          await Storage.setItem('user_data', JSON.stringify(mockUser));

          // Set authentication status
          await Storage.setItem("isAuthenticated", "true")

          // Update the user role in the AuthContext
          await setUserRole(UserRole.PROVIDER);

          // Set authentication status in the AuthContext
          setIsAuthenticated(true);

          console.log("Provider registration complete. Role set to:", UserRole.PROVIDER);

          // Navigate to provider screen
          navigation.reset({
            index: 0,
            routes: [{ name: "Provider" }],
          });
        } catch (error) {
          console.error("Error during registration:", error);
          Alert.alert("Registration Error", "There was a problem setting up your account.");
        }

        setIsLoading(false);
      }, 1500)
    } catch (error) {
      console.error("Registration error:", error)
      Alert.alert("Registration Failed", "Please try again later.")
      setIsLoading(false)
    }
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.navigate("Login")}
        >
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <Text style={styles.title}>Create Cleaner Account</Text>
      <Text style={styles.subtitle}>
        Join CleanConnect as a service provider and grow your business
      </Text>

      <View style={styles.form}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Full Name</Text>
          <View style={styles.inputWrapper}>
            <Feather name="user" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Enter your full name"
              placeholderTextColor={theme.colors.textLight}
              value={fullName}
              onChangeText={setFullName}
            />
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Email</Text>
          <View style={styles.inputWrapper}>
            <Feather name="mail" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Enter your email"
              placeholderTextColor={theme.colors.textLight}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Phone Number</Text>
          <View style={styles.inputWrapper}>
            <Feather name="phone" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Enter your phone number"
              placeholderTextColor={theme.colors.textLight}
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
            />
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Password</Text>
          <View style={styles.inputWrapper}>
            <Feather name="lock" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Create a password"
              placeholderTextColor={theme.colors.textLight}
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Confirm Password</Text>
          <View style={styles.inputWrapper}>
            <Feather name="lock" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Confirm your password"
              placeholderTextColor={theme.colors.textLight}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
            />
          </View>
        </View>

        <View style={styles.termsContainer}>
          <TouchableOpacity
            style={styles.checkboxContainer}
            onPress={() => setTermsAccepted(!termsAccepted)}
          >
            <View
              style={[
                styles.checkbox,
                termsAccepted && styles.checkboxChecked,
              ]}
            >
              {termsAccepted && (
                <Feather name="check" size={14} color={theme.colors.card} />
              )}
            </View>
          </TouchableOpacity>
          <Text style={styles.termsText}>
            I agree to the{" "}
            <Text style={styles.termsLink}>Terms of Service</Text> and{" "}
            <Text style={styles.termsLink}>Privacy Policy</Text>
          </Text>
        </View>

        <Button
          title={isLoading ? "Creating Account..." : "Create Account"}
          variant="warning"
          fullWidth
          onPress={handleRegister}
          disabled={isLoading}
        />

        <View style={styles.loginContainer}>
          <Text style={styles.loginText}>
            Already have an account?{" "}
            <Text
              style={styles.loginLink}
              onPress={() => navigation.navigate("Login")}
            >
              Sign in
            </Text>
          </Text>
        </View>
      </View>
    </ScrollView>
  )
}

// Create styles using the theme
const getStyles = (theme) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1,
  },
  contentContainer: {
    padding: theme.spacing.lg,
  },
  header: {
    flexDirection: "row",
    marginBottom: theme.spacing.lg,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  title: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.md,
    marginBottom: theme.spacing.xl,
  },
  form: {
    marginTop: theme.spacing.md,
  },
  inputContainer: {
    marginBottom: theme.spacing.md,
  },
  label: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.sm,
    fontWeight: "500",
    marginBottom: theme.spacing.xs,
  },
  inputWrapper: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    flexDirection: "row",
    paddingHorizontal: theme.spacing.sm,
  },
  inputIcon: {
    marginRight: theme.spacing.sm,
  },
  input: {
    color: theme.colors.text,
    flex: 1,
    fontSize: theme.fontSizes.md,
    paddingVertical: theme.spacing.md,
  },
  termsContainer: {
    alignItems: "flex-start",
    flexDirection: "row",
    marginBottom: theme.spacing.lg,
    marginTop: theme.spacing.sm,
  },
  checkboxContainer: {
    marginRight: theme.spacing.sm,
    marginTop: 2, // Align with text
  },
  checkbox: {
    alignItems: "center",
    borderColor: theme.colors.border,
    borderRadius: 4,
    borderWidth: 2,
    height: 20,
    justifyContent: "center",
    width: 20,
  },
  checkboxChecked: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  termsText: {
    color: theme.colors.textLight,
    flex: 1,
    fontSize: theme.fontSizes.sm,
    lineHeight: 20,
  },
  termsLink: {
    color: theme.colors.primary,
    fontWeight: "500",
  },
  loginContainer: {
    alignItems: "center",
    marginTop: theme.spacing.lg,
  },
  loginText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
  },
  loginLink: {
    color: theme.colors.primary,
    fontWeight: "500",
  },
})

export default CleanerRegistrationScreen
