"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Alert,
  Dimensions,
  Platform,
  StatusBar,
  Linking,
  RefreshControl,
  ActivityIndicator
} from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import { Card, Button } from "../components/common"
import Storage from "../utils/storage"
import { UserRole } from "../types/user"
import { SafeAreaView } from "react-native-safe-area-context"

type CleanerDashboardScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

// Define job type
interface Job {
  id: number
  service: string
  client: string
  address: string
  date: string
  time: string
  price: number
  status: string
}

const CleanerDashboardScreen = () => {
  const navigation = useNavigation<CleanerDashboardScreenNavigationProp>()
  const theme = useTheme()
  const [isLoading, setIsLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [showWeeklyGoals, setShowWeeklyGoals] = useState(true)
  const scrollViewRef = useRef<ScrollView>(null)

  // Get screen dimensions for responsive layout
  const { height, width } = Dimensions.get("window")

  // Determine device type and screen size
  const isSmallScreen = height < 700
  const isIOS = Platform.OS === 'ios'
  const isIphoneX = isIOS && (height > 800 || width > 800)

  // Calculate dynamic margin top based on screen height and platform
  const statusBarHeight = isIOS ? (isIphoneX ? 44 : 20) : (StatusBar.currentHeight || 0)
  const dynamicMarginTop = statusBarHeight

  // Import useAuth hook
  const { user } = useAuth()

  // Handle refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true)
    // Simulate data fetching
    setTimeout(() => {
      setRefreshing(false)
    }, 1500)
  }, [])

  // Verify user role on component mount
  useEffect(() => {
    const verifyUserRole = async () => {
      try {
        // Get user role from both storage and AuthContext
        const userRole = await Storage.getItem("userRole")
        const isAuthenticated = await Storage.getItem("isAuthenticated")

        // Check if user is authenticated and has the provider role
        // Check both the storage value and the AuthContext user role
        const isProvider =
          (userRole === "provider") ||
          (user?.role === UserRole.PROVIDER) ||
          (await Storage.getItem("userRoleEnum") === UserRole.PROVIDER)

        // Log detailed information for debugging
        const userRoleEnum = await Storage.getItem("userRoleEnum");
        const userData = await Storage.getItem("user_data");

        console.log("Provider access check:", {
          storageRole: userRole,
          contextRole: user?.role,
          isAuthenticated: isAuthenticated,
          userRoleEnum: userRoleEnum,
          hasUserData: !!userData
        })

        // If user is not a provider, redirect to login
        // We're being more lenient with the authentication check since we've seen issues with it
        if (!isProvider) {
          Alert.alert(
            "Access Denied",
            "You must be logged in as a cleaner to access this screen.",
            [
              {
                text: "OK",
                onPress: () => {
                  // Clear any existing auth data
                  Storage.removeItem("userRole")
                  Storage.removeItem("isAuthenticated")

                  // Redirect to shared login screen
                  navigation.reset({
                    index: 0,
                    routes: [{ name: "Login" }],
                  })
                },
              },
            ]
          )
        }

        setIsLoading(false)
      } catch (error) {
        console.error("Error verifying user role:", error)
        setIsLoading(false)
      }
    }

    verifyUserRole()
  }, [])

  // Dummy data
  const upcomingJobs = [
    {
      id: 1,
      service: "Regular Cleaning",
      client: "Fatou Jallow",
      address: "Kotu, near Palma Rima Hotel",
      date: "Today",
      time: "2:00 PM - 4:00 PM",
      price: 800,
      status: "Confirmed",
    },
    {
      id: 2,
      service: "Deep Cleaning",
      client: "Lamin Ceesay",
      address: "Bakau, New Town",
      date: "Tomorrow",
      time: "10:00 AM - 2:00 PM",
      price: 1500,
      status: "Confirmed",
    },
    {
      id: 3,
      service: "Window Cleaning",
      client: "Isatou Sanneh",
      address: "Serrekunda, Bundung",
      date: "May 18",
      time: "1:00 PM - 3:00 PM",
      price: 600,
      status: "Pending",
    },
  ]

  // Memoize styles to prevent recreation on each render
  const styles = useMemo(() => StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      flex: 1,
    },
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.primary,
    },
    header: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
    },
    userInfo: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      marginRight: theme.spacing.md,
      borderWidth: 2,
      borderColor: 'rgba(255, 255, 255, 0.7)',
    },
    userTextContainer: {
      justifyContent: "center",
    },
    greeting: {
      fontSize: theme.fontSizes.md,
      fontWeight: "700",
      color: "white",
      marginBottom: 2,
    },
    welcomeBack: {
      fontSize: theme.fontSizes.sm,
      color: "rgba(255, 255, 255, 0.9)",
      fontWeight: "500",
    },
    headerActions: {
      flexDirection: "row",
      alignItems: "center",
    },
    notificationButton: {
      position: "relative",
      padding: theme.spacing.sm,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: theme.borderRadius.round,
      marginLeft: theme.spacing.sm,
    },
    notificationBadge: {
      position: "absolute",
      top: 0,
      right: 0,
      backgroundColor: theme.colors.warning,
      width: 18,
      height: 18,
      borderRadius: 9,
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 1.5,
      borderColor: theme.colors.primary,
    },
    notificationCount: {
      color: "black",
      fontSize: 10,
      fontWeight: "700",
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
      paddingTop: theme.spacing.lg,
    },
    dateContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
      backgroundColor: theme.colors.card,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    dateText: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      color: theme.colors.text,
    },
    dateActions: {
      flexDirection: "row",
      alignItems: "center",
    },
    dateButton: {
      padding: theme.spacing.xs,
      marginHorizontal: theme.spacing.xxs,
    },
    statsGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginBottom: theme.spacing.lg,
    },
    statCard: {
      width: "50%",
      padding: theme.spacing.xs,
      height: 110, // Fixed height for consistency
    },
    statContent: {
      flex: 1,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.lg, // Increased border radius
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 3 }, // Increased shadow offset
      shadowOpacity: 0.1, // Increased shadow opacity
      shadowRadius: 6, // Increased shadow radius
      elevation: 4, // Increased elevation
      borderLeftWidth: 4, // Add a colored left border
      borderLeftColor: theme.colors.primary,
      justifyContent: "space-between", // Space content evenly
    },
    statHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    statIconContainer: {
      width: 44, // Slightly larger
      height: 44, // Slightly larger
      borderRadius: 12, // Squared corners for modern look
      backgroundColor: `${theme.colors.primary}10`, // Lighter background
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.sm,
      borderWidth: 1, // Add border
      borderColor: `${theme.colors.primary}30`, // Subtle border
    },
    statInfo: {
      flex: 1,
    },
    statLabel: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginBottom: 4, // Increased spacing
      fontWeight: "500", // Medium weight
    },
    statValue: {
      fontSize: theme.fontSizes.xl, // Larger font
      fontWeight: "700",
      color: theme.colors.text,
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    sectionTitle: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "700",
      color: theme.colors.text,
    },
    sectionAction: {
      flexDirection: "row",
      alignItems: "center",
    },
    sectionActionText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.primary,
      fontWeight: "600",
      marginRight: theme.spacing.xs,
    },
    jobCard: {
      marginBottom: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.card,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
      overflow: "hidden",
    },
    jobContent: {
      padding: theme.spacing.md,
    },
    jobHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: theme.spacing.md,
    },
    jobTitleContainer: {
      flex: 1,
    },
    jobTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "700",
      color: theme.colors.text,
      marginBottom: 4,
    },
    jobClient: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginBottom: theme.spacing.xs,
    },
    statusBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xxs,
      borderRadius: theme.borderRadius.sm,
      flexDirection: "row",
      alignItems: "center",
      marginLeft: theme.spacing.sm,
    },
    confirmedBadge: {
      backgroundColor: `${theme.colors.success}15`,
    },
    pendingBadge: {
      backgroundColor: `${theme.colors.warning}15`,
    },
    inProgressBadge: {
      backgroundColor: `${theme.colors.primary}15`,
    },
    statusText: {
      fontSize: theme.fontSizes.xs,
      fontWeight: "600",
    },
    statusIcon: {
      marginRight: theme.spacing.xxs,
    },
    confirmedText: {
      color: theme.colors.success,
    },
    pendingText: {
      color: theme.colors.warning,
    },
    inProgressText: {
      color: theme.colors.primary,
    },
    jobInfo: {
      marginBottom: theme.spacing.md,
    },
    infoRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    infoIcon: {
      width: 20,
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.sm,
      color: theme.colors.textLight,
    },
    infoText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
      flex: 1,
    },
    infoHighlight: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      color: theme.colors.text,
    },
    jobActions: {
      flexDirection: "row",
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: theme.spacing.sm,
    },
    jobAction: {
      flex: 1,
      marginHorizontal: theme.spacing.xs,
    },
    emptyJobsContainer: {
      padding: theme.spacing.xl,
      alignItems: "center",
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    emptyJobsIcon: {
      marginBottom: theme.spacing.md,
      color: theme.colors.textLight,
    },
    emptyJobsTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "700",
      marginBottom: theme.spacing.sm,
      textAlign: "center",
      color: theme.colors.text,
    },
    emptyJobsText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginBottom: theme.spacing.lg,
      textAlign: "center",
      paddingHorizontal: theme.spacing.lg,
    },
    progressCard: {
      marginTop: theme.spacing.lg,
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    progressHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    progressTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "700",
      color: theme.colors.text,
    },
    progressToggle: {
      padding: theme.spacing.xs,
    },
    progressItem: {
      marginBottom: theme.spacing.md,
    },
    progressItemHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: theme.spacing.xs,
    },
    progressLabel: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
      fontWeight: "500",
    },
    progressValue: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      fontWeight: "600",
    },
    progressBar: {
      height: 8,
      backgroundColor: `${theme.colors.border}50`,
      borderRadius: theme.borderRadius.round,
    },
    progressFill: {
      height: "100%",
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.round,
    },
    skeletonContainer: {
      flex: 1,
      padding: theme.spacing.md,
    },
    skeletonCard: {
      height: 100,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
    },
    skeletonLine: {
      height: 12,
      backgroundColor: theme.colors.border,
      borderRadius: theme.borderRadius.sm,
      marginBottom: theme.spacing.sm,
    },
    refreshIndicator: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
  }), [theme])

  // Handle navigation to profile screen
  const handleProfilePress = useCallback(() => {
    navigation.navigate("ProviderProfile")
  }, [navigation])

  // Handle navigation to notifications screen
  const handleNotificationsPress = useCallback(() => {
    navigation.navigate("Notifications")
  }, [navigation])

  // Handle opening map directions
  const handleGetDirections = useCallback((address: string) => {
    // Format the address for map URL
    const formattedAddress = encodeURIComponent(address)

    // Create map URL (works for both iOS and Android)
    const mapsUrl = Platform.OS === 'ios'
      ? `maps://maps.apple.com/?q=${formattedAddress}&dirflg=d`
      : `https://www.google.com/maps/dir/?api=1&destination=${formattedAddress}&travelmode=driving`

    // Check if the device can open the URL
    Linking.canOpenURL(mapsUrl)
      .then(supported => {
        if (supported) {
          return Linking.openURL(mapsUrl)
        } else {
          Alert.alert(
            "Navigation Error",
            "Could not open maps application. Please make sure you have a maps app installed."
          )
        }
      })
      .catch(error => {
        console.error('An error occurred while opening maps:', error)
        Alert.alert("Navigation Error", "Could not open maps application.")
      })
  }, [])

  // Handle navigation to chat with client
  const handleContactClient = useCallback((clientName: string, clientId: string) => {
    // Navigate to the Chat screen with client information
    navigation.navigate("Chat", {
      customerId: clientId,
      customerName: clientName,
      customerImage: "https://randomuser.me/api/portraits/" + (clientName.includes("Fatou") || clientName.includes("Isatou") || clientName.includes("Mariama") ? "women" : "men") + "/" + (parseInt(clientId) * 10 + 7) + ".jpg"
    })
  }, [navigation])

  // Handle job cancellation with warnings about penalties
  const handleCancelJob = useCallback((job: any) => {
    // Calculate hours until job starts
    const isToday = job.date === "Today"
    const jobTime = job.time.split(" - ")[0] // Get start time
    const [hourStr, minuteStr] = jobTime.split(":")
    const hour = parseInt(hourStr)
    const minute = parseInt(minuteStr.split(" ")[0])
    const isPM = jobTime.includes("PM") && hour !== 12

    const jobDate = new Date()
    if (!isToday) {
      // If job is tomorrow, add 1 day
      if (job.date === "Tomorrow") {
        jobDate.setDate(jobDate.getDate() + 1)
      } else {
        // Parse date like "May 18"
        const [month, day] = job.date.split(" ")
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        const monthIndex = months.findIndex(m => m === month)
        jobDate.setMonth(monthIndex)
        jobDate.setDate(parseInt(day))
      }
    }

    // Set job time
    jobDate.setHours(isPM ? hour + 12 : hour)
    jobDate.setMinutes(minute)

    // Calculate hours until job
    const now = new Date()
    const hoursUntilJob = Math.round((jobDate.getTime() - now.getTime()) / (1000 * 60 * 60))

    // Determine cancellation fee based on time until job
    let cancellationFee = 0
    let warningMessage = ""

    if (hoursUntilJob < 2) {
      cancellationFee = Math.round(job.price * 0.5) // 50% fee for less than 2 hours
      warningMessage = `This job starts in less than 2 hours. A cancellation fee of D${cancellationFee} (50% of job price) will be applied.`
    } else if (hoursUntilJob < 24) {
      cancellationFee = Math.round(job.price * 0.25) // 25% fee for less than 24 hours
      warningMessage = `This job starts in less than 24 hours. A cancellation fee of D${cancellationFee} (25% of job price) will be applied.`
    } else if (hoursUntilJob < 48) {
      cancellationFee = Math.round(job.price * 0.1) // 10% fee for less than 48 hours
      warningMessage = `A small cancellation fee of D${cancellationFee} (10% of job price) will be applied.`
    } else {
      warningMessage = "No cancellation fee will be applied, but frequent cancellations may affect your rating."
    }

    // Show confirmation alert with appropriate warning
    Alert.alert(
      "Cancel Job",
      `Are you sure you want to cancel this ${job.service} job with ${job.client}?\n\n${warningMessage}`,
      [
        {
          text: "No, Keep Job",
          style: "cancel"
        },
        {
          text: "Yes, Cancel Job",
          style: "destructive",
          onPress: () => {
            // Here you would call an API to cancel the job
            // For now, just show a confirmation
            Alert.alert(
              "Job Cancelled",
              `The job has been cancelled. ${cancellationFee > 0 ? `A fee of D${cancellationFee} has been applied to your account.` : ""}`,
              [{ text: "OK" }]
            )
          }
        }
      ]
    )
  }, [])

  const todaysJobs = upcomingJobs.filter((job) => job.date === "Today")

  // Show loading indicator while verifying role
  if (isLoading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={{ fontSize: theme.fontSizes.md, marginTop: theme.spacing.md, color: theme.colors.text }}>
            Loading your dashboard...
          </Text>
        </View>
      </SafeAreaView>
    )
  }

  // Render skeleton loading state
  const renderSkeletonLoading = () => (
    <View style={styles.skeletonContainer}>
      <View style={[styles.skeletonCard, { width: '100%', height: 60, marginBottom: theme.spacing.lg }]} />
      <View style={styles.statsGrid}>
        <View style={styles.statCard}>
          <View style={[styles.skeletonCard, { height: 80 }]} />
        </View>
        <View style={styles.statCard}>
          <View style={[styles.skeletonCard, { height: 80 }]} />
        </View>
        <View style={styles.statCard}>
          <View style={[styles.skeletonCard, { height: 80 }]} />
        </View>
        <View style={styles.statCard}>
          <View style={[styles.skeletonCard, { height: 80 }]} />
        </View>
      </View>
      <View style={[styles.skeletonLine, { width: '50%' }]} />
      <View style={[styles.skeletonCard, { height: 180 }]} />
      <View style={[styles.skeletonLine, { width: '50%', marginTop: theme.spacing.md }]} />
      <View style={[styles.skeletonCard, { height: 120 }]} />
    </View>
  )

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        {/* App Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.userInfo}
            onPress={handleProfilePress}
            activeOpacity={0.7}
            accessibilityRole="button"
            accessibilityLabel="Go to profile"
            accessibilityHint="Navigates to your profile screen"
          >
            <Image
              source={{ uri: "https://randomuser.me/api/portraits/women/44.jpg" }}
              style={styles.avatar}
              accessibilityLabel="Profile picture"
            />
            <View style={styles.userTextContainer}>
              <Text style={styles.greeting}>Hi, Mariama</Text>
              <Text style={styles.welcomeBack}>Professional Cleaner</Text>
            </View>
          </TouchableOpacity>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.notificationButton}
              onPress={handleNotificationsPress}
              accessibilityRole="button"
              accessibilityLabel="View notifications"
              accessibilityHint="Opens your notifications"
            >
              <Feather name="bell" size={20} color="white" />
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationCount}>2</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Main Content */}
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          ref={scrollViewRef}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        >
          {/* Date Card */}
          <View style={styles.dateContainer}>
            <Text style={styles.dateText}>May 15 - May 21, 2024</Text>
            <View style={styles.dateActions}>
              <TouchableOpacity style={styles.dateButton}>
                <Feather name="chevron-left" size={20} color={theme.colors.text} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.dateButton}>
                <Feather name="chevron-right" size={20} color={theme.colors.text} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Stats Cards */}
          <View style={{
            flexDirection: "row",
            flexWrap: "wrap",
            marginBottom: theme.spacing.md,
          }}>
            {/* Earnings Card */}
            <View style={{
              width: "50%",
              padding: theme.spacing.xs,
              height: 110,
            }}>
              <View style={{
                flex: 1,
                padding: theme.spacing.md,
                backgroundColor: theme.colors.card,
                borderRadius: theme.borderRadius.lg,
                shadowColor: theme.colors.text,
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.1,
                shadowRadius: 6,
                elevation: 4,
                borderLeftWidth: 4,
                borderLeftColor: theme.colors.primary,
                justifyContent: "space-between",
              }}>
                <View style={{
                  flexDirection: "row",
                  alignItems: "center",
                }}>
                  <View style={{
                    width: 44,
                    height: 44,
                    borderRadius: 12,
                    backgroundColor: `${theme.colors.primary}10`,
                    justifyContent: "center",
                    alignItems: "center",
                    marginRight: theme.spacing.sm,
                    borderWidth: 1,
                    borderColor: `${theme.colors.primary}30`,
                  }}>
                    <Feather name="dollar-sign" size={22} color={theme.colors.primary} />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      fontSize: theme.fontSizes.sm,
                      color: theme.colors.textLight,
                      marginBottom: 4,
                      fontWeight: "500",
                    }}>Weekly Earnings</Text>
                    <Text style={{
                      fontSize: theme.fontSizes.xl,
                      fontWeight: "700",
                      color: theme.colors.text,
                    }}>D4,500</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Hours Card */}
            <View style={{
              width: "50%",
              padding: theme.spacing.xs,
              height: 110,
            }}>
              <View style={{
                flex: 1,
                padding: theme.spacing.md,
                backgroundColor: theme.colors.card,
                borderRadius: theme.borderRadius.lg,
                shadowColor: theme.colors.text,
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.1,
                shadowRadius: 6,
                elevation: 4,
                borderLeftWidth: 4,
                borderLeftColor: theme.colors.secondary,
                justifyContent: "space-between",
              }}>
                <View style={{
                  flexDirection: "row",
                  alignItems: "center",
                }}>
                  <View style={{
                    width: 44,
                    height: 44,
                    borderRadius: 12,
                    backgroundColor: `${theme.colors.secondary}10`,
                    justifyContent: "center",
                    alignItems: "center",
                    marginRight: theme.spacing.sm,
                    borderWidth: 1,
                    borderColor: `${theme.colors.secondary}30`,
                  }}>
                    <Feather name="clock" size={22} color={theme.colors.secondary} />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      fontSize: theme.fontSizes.sm,
                      color: theme.colors.textLight,
                      marginBottom: 4,
                      fontWeight: "500",
                    }}>Hours Worked</Text>
                    <Text style={{
                      fontSize: theme.fontSizes.xl,
                      fontWeight: "700",
                      color: theme.colors.text,
                    }}>18h</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Jobs Completed Card */}
            <View style={{
              width: "50%",
              padding: theme.spacing.xs,
              height: 110,
              marginTop: theme.spacing.xs,
            }}>
              <View style={{
                flex: 1,
                padding: theme.spacing.md,
                backgroundColor: theme.colors.card,
                borderRadius: theme.borderRadius.lg,
                shadowColor: theme.colors.text,
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.1,
                shadowRadius: 6,
                elevation: 4,
                borderLeftWidth: 4,
                borderLeftColor: theme.colors.success,
                justifyContent: "space-between",
              }}>
                <View style={{
                  flexDirection: "row",
                  alignItems: "center",
                }}>
                  <View style={{
                    width: 44,
                    height: 44,
                    borderRadius: 12,
                    backgroundColor: `${theme.colors.success}10`,
                    justifyContent: "center",
                    alignItems: "center",
                    marginRight: theme.spacing.sm,
                    borderWidth: 1,
                    borderColor: `${theme.colors.success}30`,
                  }}>
                    <Feather name="check-circle" size={22} color={theme.colors.success} />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      fontSize: theme.fontSizes.sm,
                      color: theme.colors.textLight,
                      marginBottom: 4,
                      fontWeight: "500",
                    }}>Jobs Completed</Text>
                    <Text style={{
                      fontSize: theme.fontSizes.xl,
                      fontWeight: "700",
                      color: theme.colors.text,
                    }}>7</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Rating Card */}
            <View style={{
              width: "50%",
              padding: theme.spacing.xs,
              height: 110,
              marginTop: theme.spacing.xs,
            }}>
              <View style={{
                flex: 1,
                padding: theme.spacing.md,
                backgroundColor: theme.colors.card,
                borderRadius: theme.borderRadius.lg,
                shadowColor: theme.colors.text,
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.1,
                shadowRadius: 6,
                elevation: 4,
                borderLeftWidth: 4,
                borderLeftColor: theme.colors.warning,
                justifyContent: "space-between",
              }}>
                <View style={{
                  flexDirection: "row",
                  alignItems: "center",
                }}>
                  <View style={{
                    width: 44,
                    height: 44,
                    borderRadius: 12,
                    backgroundColor: `${theme.colors.warning}10`,
                    justifyContent: "center",
                    alignItems: "center",
                    marginRight: theme.spacing.sm,
                    borderWidth: 1,
                    borderColor: `${theme.colors.warning}30`,
                  }}>
                    <Feather name="star" size={22} color={theme.colors.warning} />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      fontSize: theme.fontSizes.sm,
                      color: theme.colors.textLight,
                      marginBottom: 4,
                      fontWeight: "500",
                    }}>Rating</Text>
                    <Text style={{
                      fontSize: theme.fontSizes.xl,
                      fontWeight: "700",
                      color: theme.colors.text,
                    }}>4.9</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>

          {/* Today's Schedule */}
          <View style={{ marginBottom: theme.spacing.lg }}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Today's Schedule</Text>
              {todaysJobs.length > 0 && (
                <TouchableOpacity style={styles.sectionAction}>
                  <Text style={styles.sectionActionText}>View All</Text>
                  <Feather name="chevron-right" size={16} color={theme.colors.primary} />
                </TouchableOpacity>
              )}
            </View>

            {todaysJobs.length > 0 ? (
              todaysJobs.map((job) => (
                <View key={job.id} style={styles.jobCard}>
                  <View style={styles.jobContent}>
                    <View style={styles.jobHeader}>
                      <View style={styles.jobTitleContainer}>
                        <Text style={styles.jobTitle}>{job.service}</Text>
                        <Text style={styles.jobClient}>{job.client}</Text>
                      </View>
                      <View
                        style={[
                          styles.statusBadge,
                          job.status === "Confirmed" ? styles.confirmedBadge : styles.pendingBadge,
                        ]}
                      >
                        <Feather
                          name={job.status === "Confirmed" ? "check" : "clock"}
                          size={12}
                          color={job.status === "Confirmed" ? theme.colors.success : theme.colors.warning}
                          style={styles.statusIcon}
                        />
                        <Text
                          style={[
                            styles.statusText,
                            job.status === "Confirmed" ? styles.confirmedText : styles.pendingText,
                          ]}
                        >
                          {job.status}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.jobInfo}>
                      <View style={styles.infoRow}>
                        <Feather name="map-pin" size={16} style={styles.infoIcon} />
                        <Text style={styles.infoText}>{job.address}</Text>
                      </View>
                      <View style={styles.infoRow}>
                        <Feather name="clock" size={16} style={styles.infoIcon} />
                        <Text style={styles.infoText}>{job.time}</Text>
                      </View>
                      <View style={styles.infoRow}>
                        <Feather name="dollar-sign" size={16} style={styles.infoIcon} />
                        <Text style={styles.infoHighlight}>D{job.price}</Text>
                      </View>
                    </View>
                    <View style={styles.jobActions}>
                      <Button
                        title="Get Directions"
                        variant="primary"
                        size="small"
                        icon={<Feather name="map" size={16} color="white" />}
                        style={styles.jobAction}
                        onPress={() => handleGetDirections(job.address)}
                      />
                      <Button
                        title="Contact"
                        variant="outline"
                        size="small"
                        icon={<Feather name="message-circle" size={16} color={theme.colors.primary} />}
                        style={styles.jobAction}
                        onPress={() => handleContactClient(job.client, job.id.toString())}
                      />
                    </View>
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.emptyJobsContainer}>
                <Feather name="calendar" size={48} style={styles.emptyJobsIcon} />
                <Text style={styles.emptyJobsTitle}>No jobs scheduled for today</Text>
                <Text style={styles.emptyJobsText}>You have no cleaning jobs scheduled for today. Update your availability to get more bookings.</Text>
                <Button
                  title="Update Availability"
                  variant="primary"
                  icon={<Feather name="calendar" size={16} color="white" />}
                  onPress={() => navigation.navigate("ManageAvailability")}
                />
              </View>
            )}
          </View>

          {/* Upcoming Jobs */}
          <View style={{ marginBottom: theme.spacing.lg }}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Upcoming Jobs</Text>
              <TouchableOpacity
                style={styles.sectionAction}
                onPress={() => navigation.navigate("ProviderJobs")}
              >
                <Text style={styles.sectionActionText}>View All</Text>
                <Feather name="chevron-right" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>

            {upcomingJobs
              .filter((job) => job.date !== "Today")
              .map((job) => (
                <View key={job.id} style={styles.jobCard}>
                  <View style={styles.jobContent}>
                    <View style={styles.jobHeader}>
                      <View style={styles.jobTitleContainer}>
                        <View
                          style={[
                            styles.statusBadge,
                            { alignSelf: "flex-start", marginBottom: theme.spacing.xs, backgroundColor: `${theme.colors.secondary}20` },
                          ]}
                        >
                          <Feather name="calendar" size={12} color={theme.colors.secondary} style={styles.statusIcon} />
                          <Text style={[styles.statusText, { color: theme.colors.secondary }]}>{job.date}</Text>
                        </View>
                        <Text style={styles.jobTitle}>{job.service}</Text>
                        <Text style={styles.jobClient}>{job.client}</Text>
                      </View>
                      <View
                        style={[
                          styles.statusBadge,
                          job.status === "Confirmed" ? styles.confirmedBadge : styles.pendingBadge,
                        ]}
                      >
                        <Feather
                          name={job.status === "Confirmed" ? "check" : "clock"}
                          size={12}
                          color={job.status === "Confirmed" ? theme.colors.success : theme.colors.warning}
                          style={styles.statusIcon}
                        />
                        <Text
                          style={[
                            styles.statusText,
                            job.status === "Confirmed" ? styles.confirmedText : styles.pendingText,
                          ]}
                        >
                          {job.status}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.jobInfo}>
                      <View style={styles.infoRow}>
                        <Feather name="clock" size={16} style={styles.infoIcon} />
                        <Text style={styles.infoText}>{job.time}</Text>
                      </View>
                      <View style={styles.infoRow}>
                        <Feather name="dollar-sign" size={16} style={styles.infoIcon} />
                        <Text style={styles.infoHighlight}>D{job.price}</Text>
                      </View>
                    </View>
                    <View style={styles.jobActions}>
                      <Button
                        title="Cancel Job"
                        variant="outline"
                        size="small"
                        icon={<Feather name="x-circle" size={16} color={theme.colors.notification} />}
                        style={styles.jobAction}
                        textStyle={{ color: theme.colors.notification }}
                        onPress={() => handleCancelJob(job)}
                      />
                      <Button
                        title="Contact"
                        variant="outline"
                        size="small"
                        icon={<Feather name="message-circle" size={16} color={theme.colors.primary} />}
                        style={styles.jobAction}
                        onPress={() => handleContactClient(job.client, job.id.toString())}
                      />
                    </View>
                  </View>
                </View>
              ))}
          </View>

          {/* Weekly Goal Progress */}
          {showWeeklyGoals && (
            <View style={styles.progressCard}>
              <View style={styles.progressHeader}>
                <Text style={styles.progressTitle}>Weekly Goal Progress</Text>
                <TouchableOpacity
                  style={styles.progressToggle}
                  onPress={() => setShowWeeklyGoals(false)}
                >
                  <Feather name="x" size={18} color={theme.colors.textLight} />
                </TouchableOpacity>
              </View>
              <View style={styles.progressItem}>
                <View style={styles.progressItemHeader}>
                  <Text style={styles.progressLabel}>Earnings</Text>
                  <Text style={styles.progressValue}>D4,500 / D6,000</Text>
                </View>
                <View style={styles.progressBar}>
                  <View style={[styles.progressFill, { width: "75%" }]} />
                </View>
              </View>
              <View style={styles.progressItem}>
                <View style={styles.progressItemHeader}>
                  <Text style={styles.progressLabel}>Jobs</Text>
                  <Text style={styles.progressValue}>7 / 10</Text>
                </View>
                <View style={styles.progressBar}>
                  <View style={[styles.progressFill, { width: "70%" }]} />
                </View>
              </View>
              <View style={styles.progressItem}>
                <View style={styles.progressItemHeader}>
                  <Text style={styles.progressLabel}>Hours</Text>
                  <Text style={styles.progressValue}>18 / 25</Text>
                </View>
                <View style={styles.progressBar}>
                  <View style={[styles.progressFill, { width: "72%" }]} />
                </View>
              </View>
            </View>
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  )
}

export default CleanerDashboardScreen
