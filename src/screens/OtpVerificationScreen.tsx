import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { AuthStackParamList } from '../navigation/AuthNavigator';
import Button from '../components/common/Button';
import { localAuthService } from '../services/localAuthService';

type OtpVerificationScreenRouteProp = RouteProp<AuthStackParamList, 'OtpVerification'>;
type OtpVerificationScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'OtpVerification'>;

const OtpVerificationScreen: React.FC = () => {
  const navigation = useNavigation<OtpVerificationScreenNavigationProp>();
  const route = useRoute<OtpVerificationScreenRouteProp>();
  const theme = useTheme();

  const { email, purpose } = route.params;

  const [otp, setOtp] = useState(['', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const inputRefs = useRef<Array<TextInput | null>>([null, null, null, null]);

  // Timer for OTP resend
  useEffect(() => {
    if (timeLeft > 0) {
      const timerId = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timerId);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  // Handle OTP input change
  const handleOtpChange = (text: string, index: number) => {
    if (text.length > 1) {
      text = text[0]; // Only take the first character
    }

    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Auto-focus next input
    if (text && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle OTP verification
  const handleVerifyOtp = async () => {
    const otpCode = otp.join('');

    if (otpCode.length !== 4) {
      Alert.alert('Invalid OTP', 'Please enter a valid 4-digit OTP code.');
      return;
    }

    try {
      setIsLoading(true);

      if (purpose === 'LOGIN') {
        // For login, we need to complete the login process
        const response = await localAuthService.verifyOtp(email, otpCode);

        if (response.success) {
          // Navigate to main screen based on user role
          Alert.alert(
            'Login Successful',
            'You have been logged in successfully.',
            [
              {
                text: 'OK',
                onPress: () => {
                  // Navigation will be handled by the AuthContext
                  // The token is already stored by the completeLogin function
                },
              },
            ]
          );
        } else {
          Alert.alert('Verification Failed', 'Invalid verification code. Please try again.');
        }
      } else {
        // For registration and password reset
        const response = await localAuthService.verifyOtp(email, otpCode);

        if (response.success) {
          if (purpose === 'REGISTER') {
            // Navigate to home screen after registration
            Alert.alert(
              'Registration Successful',
              'Your account has been verified successfully.',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    // Complete the login process after verification
                    localAuthService.verifyOtp(email, otpCode)
                      .then(loginResponse => {
                        if (loginResponse.success) {
                          // The AuthContext will handle navigation based on the stored token
                          console.log('Login completed after verification');
                        } else {
                          // If login fails, go to login screen
                          navigation.navigate('Login');
                        }
                      })
                      .catch(err => {
                        console.error('Error completing login after verification:', err);
                        navigation.navigate('Login');
                      });
                  },
                },
              ]
            );
          } else if (purpose === 'RESET') {
            // Navigate to password reset screen
            Alert.alert(
              'Verification Successful',
              'You can now reset your password.',
              [
                {
                  text: 'OK',
                  onPress: () => navigation.navigate('Login'),
                },
              ]
            );
          }
        } else {
          Alert.alert('Verification Failed', 'Invalid verification code. Please try again.');
        }
      }
    } catch (error: any) {
      console.error('OTP verification error:', error);
      Alert.alert('Verification Failed', error.error || 'Invalid verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP resend
  const handleResendOtp = async () => {
    if (!canResend) return;

    try {
      setIsLoading(true);

      // Call local auth service to send OTP
      const response = await localAuthService.sendOtp(email);

      if (response.success) {
        // Reset timer
        setTimeLeft(60);
        setCanResend(false);

        Alert.alert('OTP Sent', `A new OTP has been sent to ${email}.`);
      } else {
        Alert.alert('Resend Failed', 'Failed to resend OTP. Please try again.');
      }
    } catch (error: any) {
      console.error('OTP resend error:', error);
      Alert.alert('Resend Failed', error.error || 'Failed to resend OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Verification Code
        </Text>

        <Text style={[styles.subtitle, { color: theme.colors.textLight }]}>
          {purpose === 'REGISTER'
            ? 'We have sent a verification code to'
            : purpose === 'RESET'
            ? 'Enter the code sent to reset your password'
            : 'Enter the code sent to verify your account'}
        </Text>

        <Text style={[styles.email, { color: theme.colors.text }]}>
          {email}
        </Text>

        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={ref => (inputRefs.current[index] = ref)}
              style={[
                styles.otpInput,
                { borderColor: theme.colors.border, color: theme.colors.text },
                digit ? { borderColor: theme.colors.primary } : {},
              ]}
              value={digit}
              onChangeText={text => handleOtpChange(text, index)}
              keyboardType="number-pad"
              maxLength={1}
              onKeyPress={({ nativeEvent }) => {
                if (nativeEvent.key === 'Backspace' && !digit && index > 0) {
                  inputRefs.current[index - 1]?.focus();
                }
              }}
            />
          ))}
        </View>

        <Button
          title={isLoading ? "Verifying..." : "Verify"}
          onPress={handleVerifyOtp}
          disabled={isLoading || otp.join('').length !== 4}
          loading={isLoading}
          style={{ marginTop: 30 }}
        />

        <View style={styles.resendContainer}>
          <Text style={[styles.resendText, { color: theme.colors.textLight }]}>
            Didn't receive the code?
          </Text>

          {canResend ? (
            <TouchableOpacity onPress={handleResendOtp} disabled={isLoading}>
              <Text style={[styles.resendButton, { color: theme.colors.primary }]}>
                Resend Code
              </Text>
            </TouchableOpacity>
          ) : (
            <Text style={[styles.timerText, { color: theme.colors.textLight }]}>
              Resend in {timeLeft}s
            </Text>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  email: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 40,
    textAlign: 'center',
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%',
    marginBottom: 30,
  },
  otpInput: {
    width: 60,
    height: 60,
    borderWidth: 1,
    borderRadius: 8,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  resendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 30,
  },
  resendText: {
    fontSize: 14,
    marginRight: 5,
  },
  resendButton: {
    fontSize: 14,
    fontWeight: '500',
  },
  timerText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default OtpVerificationScreen;
