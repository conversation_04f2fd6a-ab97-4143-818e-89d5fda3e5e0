"use client"

import { useState, useEffect } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert
} from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Header from "../components/Header"
import Card from "../components/Card"
import Button from "../components/Button"
import Badge from "../components/Badge"
import Storage from "../utils/storage"
import { UserRole } from "../types/user"

type ManageAvailabilityScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

// Default work hours for MVP
const DEFAULT_WORK_HOURS = "9:00 AM - 6:00 PM"

// Interface for day availability
interface DayAvailability {
  available: boolean
  defaultHours: string
}

// Interface for weekly availability
interface WeeklyAvailability {
  [key: string]: DayAvailability
}

const ManageAvailabilityScreen = () => {
  const navigation = useNavigation<ManageAvailabilityScreenNavigationProp>()
  const theme = useTheme()
  const { user } = useAuth()
  const [showCalendarOverride, setShowCalendarOverride] = useState(false)
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [availableOnSelectedDate, setAvailableOnSelectedDate] = useState(true)
  const [overriddenDates, setOverriddenDates] = useState<string[]>([])

  // Verify user role on component mount
  useEffect(() => {
    const verifyUserRole = async () => {
      try {
        // Get user role from both storage and AuthContext
        const userRole = await Storage.getItem("userRole")
        const isAuthenticated = await Storage.getItem("isAuthenticated")

        // Check if user is authenticated and has the provider role
        // Check both the storage value and the AuthContext user role
        const isProvider =
          (userRole === "provider") ||
          (user?.role === UserRole.PROVIDER) ||
          (await Storage.getItem("userRoleEnum") === UserRole.PROVIDER)

        console.log("Availability Screen access check:", {
          storageRole: userRole,
          contextRole: user?.role,
          isAuthenticated: isAuthenticated
        })

        // If user is not a provider, redirect to login
        // We're being more lenient with the authentication check since we've seen issues with it
        if (!isProvider) {
          Alert.alert(
            "Access Denied",
            "You must be logged in as a cleaner to access this screen.",
            [
              {
                text: "OK",
                onPress: () => {
                  // Clear any existing auth data
                  Storage.removeItem("userRole")
                  Storage.removeItem("isAuthenticated")

                  // Redirect to shared login screen
                  navigation.reset({
                    index: 0,
                    routes: [{ name: "Login" }],
                  })
                },
              },
            ]
          )
        }
      } catch (error) {
        console.error("Error verifying user role:", error)
      }
    }

    verifyUserRole()
  }, [])

  // Weekly availability state with default toggles
  const [weeklyAvailability, setWeeklyAvailability] = useState<WeeklyAvailability>({
    monday: { available: true, defaultHours: "9:00 AM - 6:00 PM" },
    tuesday: { available: true, defaultHours: "9:00 AM - 6:00 PM" },
    wednesday: { available: true, defaultHours: "9:00 AM - 6:00 PM" },
    thursday: { available: true, defaultHours: "9:00 AM - 6:00 PM" },
    friday: { available: true, defaultHours: "9:00 AM - 6:00 PM" },
    saturday: { available: false, defaultHours: "9:00 AM - 6:00 PM" },
    sunday: { available: false, defaultHours: "9:00 AM - 6:00 PM" },
  })

  // Generate calendar days
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay()
  }

  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear()
    const month = currentMonth.getMonth()

    const daysInMonth = getDaysInMonth(year, month)
    const firstDay = getFirstDayOfMonth(year, month)

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push({ day: 0, date: null })
    }

    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month, i)
      const dateString = date.toISOString().split('T')[0]
      days.push({
        day: i,
        date,
        isToday: new Date().toDateString() === date.toDateString(),
        isSelected: selectedDate && selectedDate.toDateString() === date.toDateString(),
        isOverridden: overriddenDates.includes(dateString),
      })
    }

    return days
  }

  const calendarDays = generateCalendarDays()

  const prevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1))
  }

  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1))
  }

  const formatMonth = (date: Date) => {
    return date.toLocaleDateString("en-US", { month: "long", year: "numeric" })
  }

  const handleDateClick = (date: Date) => {
    setSelectedDate(date)
    const dateString = date.toISOString().split('T')[0]

    // Check if this date is already overridden
    if (overriddenDates.includes(dateString)) {
      // Use the overridden settings
      setAvailableOnSelectedDate(true) // You would load the actual saved value here
    } else {
      // Use the weekly settings for this day of the week
      const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'lowercase' })
      const daySettings = weeklyAvailability[dayOfWeek]

      setAvailableOnSelectedDate(daySettings.available)
    }
  }

  const handleSaveOverride = () => {
    if (!selectedDate) return

    const dateString = selectedDate.toISOString().split('T')[0]

    // Add to overridden dates if not already there
    if (!overriddenDates.includes(dateString)) {
      setOverriddenDates([...overriddenDates, dateString])
    }

    Alert.alert("Success", "Date availability override saved successfully!")
    setSelectedDate(null)
  }

  const handleDayAvailabilityToggle = (day: string) => {
    setWeeklyAvailability({
      ...weeklyAvailability,
      [day]: {
        ...weeklyAvailability[day],
        available: !weeklyAvailability[day].available
      }
    })
  }

  const handleSaveWeeklySchedule = () => {
    // Check if all days are toggled off
    const allDaysOff = Object.values(weeklyAvailability).every(day => !day.available)

    if (allDaysOff) {
      Alert.alert(
        "Warning",
        "You have turned off all weekdays. This may limit your job opportunities.",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Save Anyway",
            onPress: () => Alert.alert("Success", "Weekly availability schedule saved successfully!")
          }
        ]
      )
    } else {
      // In a real app, this would save to a backend
      Alert.alert("Success", "Weekly availability schedule saved successfully!")
    }
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.md,
    },
    infoCard: {
      marginBottom: theme.spacing.md,
    },
    infoContent: {
      padding: theme.spacing.md,
      backgroundColor: `${theme.colors.primary}10`,
      borderLeftWidth: 4,
      borderLeftColor: theme.colors.primary,
    },
    infoText: {
      fontSize: theme.fontSizes.sm,
      lineHeight: 20,
    },
    sectionTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.sm,
      marginTop: theme.spacing.md,
    },
    weeklyScheduleCard: {
      marginBottom: theme.spacing.md,
    },
    weeklyScheduleContent: {
      padding: theme.spacing.md,
    },
    weeklyScheduleTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    dayScheduleItem: {
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      paddingBottom: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    lastDayScheduleItem: {
      borderBottomWidth: 0,
      paddingBottom: 0,
      marginBottom: 0,
    },
    dayScheduleHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    dayName: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
      textTransform: "capitalize",
    },
    timeSlotsList: {
      marginLeft: theme.spacing.sm,
    },
    timeSlotItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    timeSlotItemText: {
      fontSize: theme.fontSizes.sm,
      marginLeft: theme.spacing.sm,
    },
    addTimeSlotButton: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: theme.spacing.sm,
      marginLeft: theme.spacing.sm,
    },
    addTimeSlotText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.primary,
      marginLeft: theme.spacing.xs,
    },
    commonTimeSlotsContainer: {
      marginTop: theme.spacing.sm,
      marginLeft: theme.spacing.sm,
    },
    commonTimeSlotsTitle: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      marginBottom: theme.spacing.xs,
    },
    commonTimeSlotsRow: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginTop: theme.spacing.xs,
    },
    commonTimeSlotChip: {
      backgroundColor: `${theme.colors.primary}10`,
      borderRadius: theme.borderRadius.sm,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 6,
      marginRight: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    commonTimeSlotText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.primary,
    },
    addTimeSlotInput: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.sm,
      marginTop: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
      marginLeft: theme.spacing.sm,
      fontSize: theme.fontSizes.sm,
    },
    addTimeSlotActions: {
      flexDirection: "row",
      justifyContent: "flex-end",
      marginLeft: theme.spacing.sm,
    },
    calendarToggleButton: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    calendarToggleText: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
      marginLeft: theme.spacing.sm,
      color: theme.colors.primary,
    },
    calendarCard: {
      marginBottom: theme.spacing.md,
    },
    calendarContent: {
      padding: theme.spacing.md,
    },
    calendarHeader: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    monthNavigation: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    monthText: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "500",
    },
    weekdaysRow: {
      flexDirection: "row",
      marginBottom: theme.spacing.sm,
    },
    weekdayCell: {
      flex: 1,
      alignItems: "center",
    },
    weekdayText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
    },
    calendarGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
    },
    dayCell: {
      width: `${100 / 7}%`,
      aspectRatio: 1,
      padding: 2,
      justifyContent: "center",
      alignItems: "center",
    },
    dayContent: {
      width: "100%",
      height: "100%",
      justifyContent: "center",
      alignItems: "center",
      borderRadius: theme.borderRadius.sm,
    },
    todayContent: {
      backgroundColor: `${theme.colors.accent}30`,
    },
    selectedContent: {
      backgroundColor: theme.colors.primary,
    },
    overriddenContent: {
      borderWidth: 2,
      borderColor: theme.colors.warning,
    },
    dayText: {
      fontSize: theme.fontSizes.sm,
    },
    selectedDayText: {
      color: "white",
      fontWeight: "600",
    },
    dateDetailsCard: {
      marginBottom: theme.spacing.md,
    },
    dateDetailsContent: {
      padding: theme.spacing.md,
    },
    dateDetailsHeader: {
      fontSize: theme.fontSizes.md,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    availabilityRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    availabilityLabel: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
    },
    timeSlotsTitle: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      marginBottom: theme.spacing.sm,
    },
    timeSlotsGrid: {
      marginBottom: theme.spacing.md,
    },
    timeSlotContainer: {
      marginBottom: theme.spacing.sm,
    },
    timeSlotContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    timeSlotText: {
      fontSize: theme.fontSizes.sm,
      marginLeft: theme.spacing.sm,
    },
    saveButton: {
      marginTop: theme.spacing.md,
    },
    overrideNote: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginBottom: theme.spacing.md,
      fontStyle: "italic",
    },
  })

  return (
    <View style={styles.container}>
      <Header title="Manage Availability" showBackButton />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info Card */}
        <Card style={styles.infoCard}>
          <View style={styles.infoContent}>
            <Text style={styles.infoText}>
              CleanConnect will only offer you bookings during your set availability. You can update this anytime.
            </Text>
          </View>
        </Card>

        {/* Instructions Card */}
        <Card style={[styles.infoCard, { marginTop: theme.spacing.sm }]}>
          <View style={styles.infoContent}>
            <Text style={styles.infoText}>
              We've pre-selected Monday to Friday for you. You can customize your weekly schedule at any time.
            </Text>
          </View>
        </Card>

        {/* Weekly Availability Section */}
        <Text style={styles.sectionTitle}>WEEKLY AVAILABILITY</Text>
        <Card style={styles.weeklyScheduleCard}>
          <View style={styles.weeklyScheduleContent}>
            {Object.entries(weeklyAvailability).map(([day, data], index, array) => (
              <View
                key={day}
                style={[styles.dayScheduleItem, index === array.length - 1 && styles.lastDayScheduleItem]}
              >
                <View style={styles.dayScheduleHeader}>
                  <Text style={styles.dayName}>{day}</Text>
                  <Switch
                    value={data.available}
                    onValueChange={() => handleDayAvailabilityToggle(day)}
                    trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                    thumbColor="white"
                  />
                </View>

                {data.available && (
                  <View style={styles.timeSlotsList}>
                    <View style={styles.timeSlotItem}>
                      <View style={{ flexDirection: "row", alignItems: "center" }}>
                        <Feather name="clock" size={16} color={theme.colors.textLight} />
                        <Text style={styles.timeSlotItemText}>{data.defaultHours}</Text>
                      </View>
                    </View>
                  </View>
                )}
              </View>
            ))}

            <Button
              title="Save Weekly Schedule"
              variant="warning"
              icon={<Feather name="save" size={16} color="black" />}
              style={styles.saveButton}
              fullWidth
              onPress={handleSaveWeeklySchedule}
            />
          </View>
        </Card>

        {/* Calendar Override Section */}
        <TouchableOpacity
          style={styles.calendarToggleButton}
          onPress={() => setShowCalendarOverride(!showCalendarOverride)}
        >
          <Feather
            name={showCalendarOverride ? "chevron-down" : "chevron-right"}
            size={20}
            color={theme.colors.primary}
          />
          <Text style={styles.calendarToggleText}>
            Override Availability for Specific Dates
          </Text>
        </TouchableOpacity>

        {showCalendarOverride && (
          <>
            <Text style={styles.overrideNote}>
              Use this calendar to set exceptions to your weekly schedule, such as holidays or special events.
            </Text>

            {/* Month Navigation */}
            <View style={styles.monthNavigation}>
              <TouchableOpacity onPress={prevMonth}>
                <Feather name="chevron-left" size={24} color={theme.colors.text} />
              </TouchableOpacity>
              <Text style={styles.monthText}>{formatMonth(currentMonth)}</Text>
              <TouchableOpacity onPress={nextMonth}>
                <Feather name="chevron-right" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            {/* Calendar */}
            <Card style={styles.calendarCard}>
              <View style={styles.calendarContent}>
                {/* Weekday headers */}
                <View style={styles.weekdaysRow}>
                  {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                    <View key={day} style={styles.weekdayCell}>
                      <Text style={styles.weekdayText}>{day}</Text>
                    </View>
                  ))}
                </View>

                {/* Calendar grid */}
                <View style={styles.calendarGrid}>
                  {calendarDays.map((day, index) => (
                    <View key={index} style={styles.dayCell}>
                      {day.date ? (
                        <TouchableOpacity
                          style={[
                            styles.dayContent,
                            day.isToday && styles.todayContent,
                            day.isSelected && styles.selectedContent,
                            day.isOverridden && styles.overriddenContent,
                          ]}
                          onPress={() => day.date && handleDateClick(day.date)}
                        >
                          <Text
                            style={[
                              styles.dayText,
                              day.isSelected && styles.selectedDayText
                            ]}
                          >
                            {day.day}
                          </Text>
                        </TouchableOpacity>
                      ) : (
                        <View />
                      )}
                    </View>
                  ))}
                </View>
              </View>
            </Card>

            {/* Selected Date Details */}
            {selectedDate && (
              <Card style={styles.dateDetailsCard}>
                <View style={styles.dateDetailsContent}>
                  <Text style={styles.dateDetailsHeader}>
                    {selectedDate.toLocaleDateString("en-US", {
                      weekday: "long",
                      month: "long",
                      day: "numeric",
                    })}
                  </Text>

                  <View style={styles.availabilityRow}>
                    <Text style={styles.availabilityLabel}>Available for bookings</Text>
                    <Switch
                      value={availableOnSelectedDate}
                      onValueChange={setAvailableOnSelectedDate}
                      trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                      thumbColor="white"
                    />
                  </View>

                  {availableOnSelectedDate && (
                    <View>
                      <Text style={styles.timeSlotsTitle}>Default Hours</Text>
                      <View style={styles.timeSlotContainer}>
                        <View style={styles.timeSlotContent}>
                          <Feather name="clock" size={16} color={theme.colors.textLight} />
                          <Text style={styles.timeSlotText}>{DEFAULT_WORK_HOURS}</Text>
                        </View>
                      </View>
                    </View>
                  )}

                  <Button
                    title="Save Override"
                    variant="warning"
                    icon={<Feather name="save" size={16} color="black" />}
                    style={styles.saveButton}
                    fullWidth
                    onPress={handleSaveOverride}
                  />
                </View>
              </Card>
            )}
          </>
        )}
      </ScrollView>
    </View>
  )
}

export default ManageAvailabilityScreen
