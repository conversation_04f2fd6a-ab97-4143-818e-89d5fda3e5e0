import { Booking, BookingStatus } from '../types/booking';

// Mock bookings data
export const mockBookings: Booking[] = [
  {
    id: 'BK-12345',
    serviceId: '1',
    serviceName: 'Regular Home Cleaning',
    serviceImage: 'https://picsum.photos/id/26/600/300',
    date: '2023-06-15',
    time: '10:00 AM',
    duration: '2 hours',
    status: BookingStatus.COMPLETED,
    price: 250,
    address: {
      id: 'addr1',
      label: 'Home',
      address: '123 Kairaba Avenue, Serrekunda'
    },
    provider: {
      id: 'prov1',
      name: '<PERSON><PERSON>',
      image: 'https://randomuser.me/api/portraits/women/44.jpg',
      phone: '+220 7123456',
      rating: 4.9
    },
    paymentMethod: 'Mobile Money',
    paymentStatus: 'PAID',
    notes: 'Please focus on kitchen and bathrooms'
  },
  {
    id: 'BK-12346',
    serviceId: '2',
    serviceName: 'Deep Cleaning',
    serviceImage: 'https://picsum.photos/id/28/600/300',
    date: '2023-07-20',
    time: '09:00 AM',
    duration: '4 hours',
    status: BookingStatus.CONFIRMED,
    price: 450,
    address: {
      id: 'addr2',
      label: 'Office',
      address: '45 Kairaba Avenue, Serrekunda',
    },
    provider: {
      id: 'prov2',
      name: 'Ousman Ceesay',
      image: 'https://randomuser.me/api/portraits/men/32.jpg',
      phone: '+220 7654321',
      rating: 4.7
    },
    paymentMethod: 'Pay Later',
    paymentStatus: 'PENDING',
    notes: 'Need thorough cleaning of all areas'
  },
  {
    id: 'BK-12347',
    serviceId: '3',
    serviceName: 'Office Cleaning',
    serviceImage: 'https://picsum.photos/id/30/600/300',
    date: '2023-08-05',
    time: '08:00 AM',
    duration: '3 hours',
    status: BookingStatus.PENDING,
    price: 350,
    address: {
      id: 'addr3',
      label: 'Business',
      address: '78 Kairaba Avenue, Serrekunda',
    },
    provider: null,
    paymentMethod: 'Mobile Money',
    paymentStatus: 'PENDING',
    notes: 'Weekly office cleaning'
  }
];
