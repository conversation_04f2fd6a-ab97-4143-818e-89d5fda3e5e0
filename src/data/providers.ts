// Provider data for the CleanConnect app
export interface Provider {
  id: string;
  name: string;
  image: string;
  rating: number;
  jobs: number;
  verified: boolean;
  active: boolean;
  services: string[]; // IDs of services they can perform
  availability: {
    [date: string]: string[]; // Date string -> array of available time slots
  };
  reliability: number; // 0-100 score based on showing up on time, completing jobs, etc.
}

// Sample providers data
export const providers: Provider[] = [
  {
    id: "p1",
    name: "<PERSON><PERSON>",
    image: "https://randomuser.me/api/portraits/women/44.jpg",
    rating: 4.9,
    jobs: 245,
    verified: true,
    active: true,
    services: ["1", "2", "3"], // Regular, Deep, Move-in/out
    availability: {
      "2023-05-15": ["9:00 AM", "10:00 AM", "2:00 PM", "3:00 PM"],
      "2023-05-16": ["9:00 AM", "10:00 AM", "11:00 AM", "1:00 PM", "2:00 PM"],
      "2023-05-17": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-18": ["2:00 PM", "3:00 PM", "4:00 PM", "5:00 PM"],
      "2023-05-19": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-20": ["10:00 AM", "11:00 AM", "12:00 PM"],
    },
    reliability: 95,
  },
  {
    id: "p2",
    name: "Lamin Ceesay",
    image: "https://randomuser.me/api/portraits/men/22.jpg",
    rating: 4.8,
    jobs: 189,
    verified: true,
    active: true,
    services: ["1", "2", "4"], // Regular, Deep, Window
    availability: {
      "2023-05-15": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-16": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-17": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-18": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-19": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-20": ["10:00 AM", "11:00 AM", "12:00 PM"],
    },
    reliability: 92,
  },
  {
    id: "p3",
    name: "Isatou Sanneh",
    image: "https://randomuser.me/api/portraits/women/28.jpg",
    rating: 4.7,
    jobs: 132,
    verified: true,
    active: true,
    services: ["1", "3", "4"], // Regular, Move-in/out, Window
    availability: {
      "2023-05-15": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM", "5:00 PM"],
      "2023-05-16": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-17": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-18": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-19": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-20": ["10:00 AM", "11:00 AM", "12:00 PM"],
    },
    reliability: 88,
  },
  {
    id: "p4",
    name: "Bakary Jammeh",
    image: "https://randomuser.me/api/portraits/men/36.jpg",
    rating: 4.6,
    jobs: 98,
    verified: true,
    active: true,
    services: ["1", "2", "4"], // Regular, Deep, Window
    availability: {
      "2023-05-15": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-16": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-17": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-18": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-19": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-20": ["10:00 AM", "11:00 AM", "12:00 PM"],
    },
    reliability: 90,
  },
  {
    id: "p5",
    name: "Fatou Bah",
    image: "https://randomuser.me/api/portraits/women/67.jpg",
    rating: 4.5,
    jobs: 76,
    verified: true,
    active: true,
    services: ["1", "2", "3"], // Regular, Deep, Move-in/out
    availability: {
      "2023-05-15": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-16": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-17": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-18": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-19": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-20": ["10:00 AM", "11:00 AM", "12:00 PM"],
    },
    reliability: 85,
  },
  {
    id: "p6",
    name: "Ousman Jobe",
    image: "https://randomuser.me/api/portraits/men/32.jpg",
    rating: 4.4,
    jobs: 62,
    verified: true,
    active: true,
    services: ["1", "3", "4"], // Regular, Move-in/out, Window
    availability: {
      "2023-05-15": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-16": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-17": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-18": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-19": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-20": ["10:00 AM", "11:00 AM", "12:00 PM"],
    },
    reliability: 82,
  },
  {
    id: "p7",
    name: "Ebrima Jallow",
    image: "https://randomuser.me/api/portraits/men/45.jpg",
    rating: 4.3,
    jobs: 54,
    verified: true,
    active: true,
    services: ["1", "2"], // Regular, Deep
    availability: {
      "2023-05-15": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-16": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-17": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-18": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-19": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-20": ["10:00 AM", "11:00 AM", "12:00 PM"],
    },
    reliability: 80,
  },
  {
    id: "p8",
    name: "Awa Touray",
    image: "https://randomuser.me/api/portraits/women/48.jpg",
    rating: 4.2,
    jobs: 42,
    verified: true,
    active: true,
    services: ["1", "4"], // Regular, Window
    availability: {
      "2023-05-15": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-16": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-17": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-18": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM"],
      "2023-05-19": ["1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"],
      "2023-05-20": ["10:00 AM", "11:00 AM", "12:00 PM"],
    },
    reliability: 78,
  },
];

// Function to get a formatted date string for availability lookup
export const getFormattedDateString = (date: Date): string => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// Function to get eligible providers based on service, date, and time
export const getEligibleProviders = (
  serviceId: string,
  date: string,
  time: string
): Provider[] => {
  if (!serviceId || !date || !time) return [];

  // Convert date string like "Mon, 15 May" to "2023-05-15" format
  // This is a simplified conversion for the demo
  const dateParts = date.split(', ')[1].split(' ');
  const day = dateParts[0];
  const month = dateParts[1];
  
  // Map month name to month number
  const monthMap: { [key: string]: string } = {
    'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
    'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
  };
  
  const formattedDate = `2023-${monthMap[month]}-${day.padStart(2, '0')}`;
  
  return providers.filter(provider => 
    provider.active && 
    provider.services.includes(serviceId) && 
    provider.availability[formattedDate] && 
    provider.availability[formattedDate].includes(time)
  );
};

// Function to get a random subset of providers with slight weighting
export const getRandomProviders = (
  eligibleProviders: Provider[],
  count: number = 5,
  previouslyRatedHighly: string[] = []
): Provider[] => {
  if (eligibleProviders.length <= count) {
    return eligibleProviders;
  }
  
  // First, include any previously highly-rated providers if they're eligible
  const highlyRatedProviders = eligibleProviders.filter(
    provider => previouslyRatedHighly.includes(provider.id)
  );
  
  // If we have enough highly-rated providers, return them
  if (highlyRatedProviders.length >= count) {
    return highlyRatedProviders.slice(0, count);
  }
  
  // Otherwise, we need to select additional providers
  const remainingCount = count - highlyRatedProviders.length;
  const remainingProviders = eligibleProviders.filter(
    provider => !previouslyRatedHighly.includes(provider.id)
  );
  
  // Apply slight weighting based on reliability and rating
  const weightedProviders = remainingProviders.map(provider => {
    // Calculate a weight between 1-3 based on reliability and rating
    // This gives a slight advantage but doesn't dominate the randomness
    const reliabilityWeight = provider.reliability / 33; // 0-3 scale
    const ratingWeight = provider.rating - 4; // 0-1 scale for ratings 4-5
    const weight = 1 + reliabilityWeight * 0.6 + ratingWeight * 0.4; // 1-3 scale with reliability having more impact
    
    return {
      provider,
      weight
    };
  });
  
  // Select providers with weighted randomness
  const selectedProviders: Provider[] = [];
  
  while (selectedProviders.length < remainingCount && weightedProviders.length > 0) {
    // Calculate total weight
    const totalWeight = weightedProviders.reduce((sum, p) => sum + p.weight, 0);
    
    // Generate random value between 0 and totalWeight
    const random = Math.random() * totalWeight;
    
    // Find the provider that corresponds to this random value
    let cumulativeWeight = 0;
    let selectedIndex = -1;
    
    for (let i = 0; i < weightedProviders.length; i++) {
      cumulativeWeight += weightedProviders[i].weight;
      if (random <= cumulativeWeight) {
        selectedIndex = i;
        break;
      }
    }
    
    // Add the selected provider to our results
    if (selectedIndex !== -1) {
      selectedProviders.push(weightedProviders[selectedIndex].provider);
      weightedProviders.splice(selectedIndex, 1);
    }
  }
  
  // Combine highly-rated providers with randomly selected ones
  return [...highlyRatedProviders, ...selectedProviders];
};

// Function to simulate auto-assignment of a provider
export const autoAssignProvider = (
  eligibleProviders: Provider[]
): Provider | null => {
  if (eligibleProviders.length === 0) {
    return null;
  }
  
  // In a real app, this would involve sending notifications to providers
  // and waiting for the first acceptance. For this demo, we'll simulate
  // by randomly selecting a provider with a slight bias toward reliability.
  
  // Sort by reliability (higher reliability = higher chance of accepting quickly)
  const sortedProviders = [...eligibleProviders].sort(
    (a, b) => b.reliability - a.reliability
  );
  
  // Select from the top half of the list to simulate faster acceptance
  const topHalfCount = Math.max(1, Math.ceil(sortedProviders.length / 2));
  const selectedIndex = Math.floor(Math.random() * topHalfCount);
  
  return sortedProviders[selectedIndex];
};

// Function to get previously highly-rated providers for a user
// In a real app, this would come from the user's booking history
export const getPreviouslyHighlyRatedProviders = (userId: string): string[] => {
  // This is a mock function - in a real app, this would query the user's past bookings
  // and return provider IDs that the user rated 4.5 or higher
  
  // For demo purposes, we'll return a fixed set
  return ["p1", "p3"]; // Mariama and Isatou were highly rated by this user
};
