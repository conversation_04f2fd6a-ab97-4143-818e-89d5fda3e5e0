import { Service, ServiceWithProviders } from '../types/service';

// Mock services data
export const mockServices: Service[] = [
  {
    id: '1',
    title: 'Regular Home Cleaning',
    description: 'Standard cleaning service for homes',
    image: 'https://picsum.photos/id/26/600/300',
    price: 100,
    duration: '2 hours',
    category: 'HOME',
  },
  {
    id: '2',
    title: 'Deep Cleaning',
    description: 'Thorough cleaning of all areas',
    image: 'https://picsum.photos/id/28/600/300',
    price: 200,
    duration: '4 hours',
    category: 'HOME',
  },
  {
    id: '3',
    title: 'Office Cleaning',
    description: 'Professional cleaning for offices',
    image: 'https://picsum.photos/id/30/600/300',
    price: 150,
    duration: '3 hours',
    category: 'OFFICE',
  },
  {
    id: '4',
    title: 'Move-in/Move-out Cleaning',
    description: 'Comprehensive cleaning for moving',
    image: 'https://picsum.photos/id/42/600/300',
    price: 250,
    duration: '5 hours',
    category: 'HOME',
  },
  {
    id: '5',
    title: 'Post-Construction Cleaning',
    description: 'Cleaning after construction work',
    image: 'https://picsum.photos/id/48/600/300',
    price: 300,
    duration: '6 hours',
    category: 'CONSTRUCTION',
  }
];

// Mock providers for services
export const mockProviders = [
  {
    id: 'prov1',
    name: 'Mariama Jallow',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    phone: '+220 7123456',
    rating: 4.9,
    jobs: 120,
    verified: true,
  },
  {
    id: 'prov2',
    name: 'Ousman Ceesay',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    phone: '+220 7654321',
    rating: 4.7,
    jobs: 85,
    verified: true,
  },
  {
    id: 'prov3',
    name: 'Fatou Saine',
    image: 'https://randomuser.me/api/portraits/women/68.jpg',
    phone: '+220 7987654',
    rating: 4.8,
    jobs: 95,
    verified: true,
  }
];
