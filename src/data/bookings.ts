// Booking data
export const bookings = [
  {
    id: "BK-12345",
    serviceId: "1",
    serviceName: "Regular Home Cleaning",
    serviceImage: "https://picsum.photos/id/26/600/300",
    date: "2023-06-15",
    time: "10:00 AM",
    duration: "2 hours",
    status: "completed",
    price: 250,
    address: {
      id: "addr1",
      label: "Home",
      address: "123 Kairaba Avenue, Serrekunda"
    },
    provider: {
      id: "prov1",
      name: "<PERSON><PERSON>",
      image: "https://randomuser.me/api/portraits/women/44.jpg",
      phone: "+220 7123456",
      rating: 4.9
    },
    paymentMethod: "Mobile Money",
    paymentStatus: "paid",
    notes: "Please focus on kitchen and bathrooms"
  },
  {
    id: "BK-54321",
    serviceId: "2",
    serviceName: "Deep Cleaning",
    serviceImage: "https://picsum.photos/id/28/600/300",
    date: "2023-06-20",
    time: "2:00 PM",
    duration: "4 hours",
    status: "upcoming",
    price: 450,
    address: {
      id: "addr2",
      label: "Office",
      address: "45 Bertil Harding Highway, Bakau"
    },
    provider: {
      id: "prov2",
      name: "<PERSON><PERSON>",
      image: "https://randomuser.me/api/portraits/men/32.jpg",
      phone: "+220 7654321",
      rating: 4.7
    },
    paymentMethod: "Pay on Work Done",
    paymentStatus: "pending",
    notes: ""
  },
  {
    id: "BK-67890",
    serviceId: "3",
    serviceName: "Move-in/Move-out Cleaning",
    serviceImage: "https://picsum.photos/id/42/600/300",
    date: "2023-06-10",
    time: "9:00 AM",
    duration: "5 hours",
    status: "cancelled",
    price: 550,
    address: {
      id: "addr3",
      label: "New Apartment",
      address: "78 Pipeline Road, Fajara"
    },
    provider: null,
    paymentMethod: "Mobile Money",
    paymentStatus: "refunded",
    notes: "Cancelled due to scheduling conflict"
  }
];

export const getBookingById = (id: string) => {
  return bookings.find(booking => booking.id === id);
};

export const getBookingsByStatus = (status: string) => {
  return bookings.filter(booking => booking.status === status);
};

export const getRecentBookings = (limit = 3) => {
  return [...bookings].sort((a, b) => {
    const dateA = new Date(`${a.date} ${a.time}`);
    const dateB = new Date(`${b.date} ${b.time}`);
    return dateB.getTime() - dateA.getTime();
  }).slice(0, limit);
};
