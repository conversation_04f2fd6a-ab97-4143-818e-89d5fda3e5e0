/**
 * Persistent storage implementation using AsyncStorage for native and localStorage for web
 * This provides persistent storage across app restarts
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage interface
const Storage = {
  /**
   * Store a key-value pair
   */
  setItem: async (key: string, value: string): Promise<void> => {
    try {
      // For web, use localStorage
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(key, value);
      } else {
        // For native, use AsyncStorage
        await AsyncStorage.setItem(key, value);
      }
    } catch (error) {
      console.error('Error storing data', error);
    }
  },

  /**
   * Retrieve a value by key
   */
  getItem: async (key: string): Promise<string | null> => {
    try {
      // For web, use localStorage
      if (typeof localStorage !== 'undefined') {
        return localStorage.getItem(key);
      } else {
        // For native, use AsyncStorage
        return await AsyncStorage.getItem(key);
      }
    } catch (error) {
      console.error('Error retrieving data', error);
      return null;
    }
  },

  /**
   * Remove a key-value pair
   */
  removeItem: async (key: string): Promise<void> => {
    try {
      // For web, use localStorage
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem(key);
      } else {
        // For native, use AsyncStorage
        await AsyncStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Error removing data', error);
    }
  },

  /**
   * Clear all storage
   */
  clear: async (): Promise<void> => {
    try {
      // For web, use localStorage
      if (typeof localStorage !== 'undefined') {
        localStorage.clear();
      } else {
        // For native, use AsyncStorage
        await AsyncStorage.clear();
      }
    } catch (error) {
      console.error('Error clearing data', error);
    }
  }
};

export default Storage;
