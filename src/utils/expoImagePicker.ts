import * as ImagePicker from 'expo-image-picker';
import { Alert, Platform } from 'react-native';

/**
 * Request permission to access the camera and photo library
 */
export const requestMediaPermissions = async (): Promise<boolean> => {
  try {
    // Request camera permissions
    const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();

    // Request media library permissions
    const mediaLibraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

    return cameraPermission.granted && mediaLibraryPermission.granted;
  } catch (error) {
    console.error('Error requesting media permissions:', error);
    return false;
  }
};

/**
 * Pick an image from the camera
 */
export const takePhoto = async (): Promise<string | null> => {
  try {
    // Request permissions first
    const permissionsGranted = await requestMediaPermissions();

    if (!permissionsGranted) {
      Alert.alert(
        'Permissions Required',
        'Please grant camera and photo library permissions to take photos.',
        [{ text: 'OK' }]
      );
      return null;
    }

    // Launch camera
    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.7,
      mediaTypes: ['images'],
      base64: true,
    });

    // Check if the user cancelled
    if (result.canceled || !result.assets || result.assets.length === 0) {
      return null;
    }

    // Get the selected asset
    const selectedAsset = result.assets[0];

    // Return the base64 data or URI
    if (selectedAsset.base64) {
      return `data:image/jpeg;base64,${selectedAsset.base64}`;
    } else if (selectedAsset.uri) {
      return selectedAsset.uri;
    }

    return null;
  } catch (error) {
    console.error('Error taking photo:', error);
    Alert.alert('Error', 'Failed to take photo. Please try again.');
    return null;
  }
};

/**
 * Pick an image from the gallery
 */
export const pickFromGallery = async (): Promise<string | null> => {
  try {
    // Request permissions first
    const permissionsGranted = await requestMediaPermissions();

    if (!permissionsGranted) {
      Alert.alert(
        'Permissions Required',
        'Please grant photo library permissions to select images.',
        [{ text: 'OK' }]
      );
      return null;
    }

    // Launch image library
    const result = await ImagePicker.launchImageLibraryAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.7,
      mediaTypes: ['images'],
      base64: true,
    });

    // Check if the user cancelled
    if (result.canceled || !result.assets || result.assets.length === 0) {
      return null;
    }

    // Get the selected asset
    const selectedAsset = result.assets[0];

    // Return the base64 data or URI
    if (selectedAsset.base64) {
      return `data:image/jpeg;base64,${selectedAsset.base64}`;
    } else if (selectedAsset.uri) {
      return selectedAsset.uri;
    }

    return null;
  } catch (error) {
    console.error('Error picking image:', error);
    Alert.alert('Error', 'Failed to pick image. Please try again.');
    return null;
  }
};

/**
 * Show image picker options (camera or gallery)
 */
export const showImagePickerOptions = async (): Promise<string | null> => {
  return new Promise((resolve) => {
    Alert.alert(
      'Upload Image',
      'Choose an option',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => resolve(null),
        },
        {
          text: 'Take Photo',
          onPress: async () => {
            const result = await takePhoto();
            resolve(result);
          },
        },
        {
          text: 'Choose from Gallery',
          onPress: async () => {
            const result = await pickFromGallery();
            resolve(result);
          },
        },
      ],
      { cancelable: true }
    );
  });
};
