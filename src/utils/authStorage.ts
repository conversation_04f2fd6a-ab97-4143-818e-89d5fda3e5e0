/**
 * Authentication storage service
 * Provides a centralized way to store and retrieve authentication-related data
 * Uses SecureStore for sensitive data and AsyncStorage for non-sensitive data
 */

import * as SecureStore from 'expo-secure-store';
import Storage from './storage';
import { STORAGE_KEYS } from '../config/constants';
import { User, UserRole } from '../types/user';

// Keys for secure storage
const SECURE_STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_DATA: 'user_data',
  AUTH_STATE: 'isAuthenticated',
  USER_ROLE: 'userRoleEnum',
  USER_ROLE_DISPLAY: 'userRole',
  PROFILE_DATA: 'profile_data',
  CACHED_EMAIL: 'cached_email',
  CACHED_PASSWORD: 'cached_password',
  REMEMBER_ME: 'remember_me',
  ONBOARDING_COMPLETED: 'onboardingCompleted',
};

/**
 * Save authentication tokens
 */
export const saveAuthTokens = async (accessToken: string, refreshToken?: string): Promise<void> => {
  try {
    // Save to SecureStore
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.ACCESS_TOKEN, accessToken);

    // Also save to regular storage for backward compatibility
    await Storage.setItem(STORAGE_KEYS.AUTH_TOKEN, accessToken);

    // Save refresh token if provided
    if (refreshToken) {
      await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    }

    console.log('Auth tokens saved successfully');
  } catch (error) {
    console.error('Error saving auth tokens:', error);
  }
};

/**
 * Get access token
 */
export const getAccessToken = async (): Promise<string | null> => {
  try {
    // Try SecureStore first
    const token = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.ACCESS_TOKEN);

    // If not found, try regular storage
    if (!token) {
      return await Storage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    }

    return token;
  } catch (error) {
    console.error('Error getting access token:', error);
    return null;
  }
};

/**
 * Get refresh token
 */
export const getRefreshToken = async (): Promise<string | null> => {
  try {
    return await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.REFRESH_TOKEN);
  } catch (error) {
    console.error('Error getting refresh token:', error);
    return null;
  }
};

/**
 * Save user data
 */
export const saveUserData = async (user: User): Promise<void> => {
  try {
    const userData = JSON.stringify(user);

    // Save to SecureStore
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.USER_DATA, userData);

    // Also save to regular storage for backward compatibility
    await Storage.setItem(STORAGE_KEYS.USER_DATA, userData);

    console.log('User data saved successfully');
  } catch (error) {
    console.error('Error saving user data:', error);
  }
};

/**
 * Get user data
 */
export const getUserData = async (): Promise<User | null> => {
  try {
    // Try SecureStore first
    const userData = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.USER_DATA);

    // If not found, try regular storage
    if (!userData) {
      const regularData = await Storage.getItem(STORAGE_KEYS.USER_DATA);
      return regularData ? JSON.parse(regularData) : null;
    }

    return JSON.parse(userData);
  } catch (error) {
    console.error('Error getting user data:', error);
    return null;
  }
};

/**
 * Save authentication state
 */
export const saveAuthState = async (isAuthenticated: boolean): Promise<void> => {
  try {
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.AUTH_STATE, isAuthenticated ? 'true' : 'false');
    console.log('Auth state saved:', isAuthenticated);
  } catch (error) {
    console.error('Error saving auth state:', error);
  }
};

/**
 * Get authentication state
 */
export const getAuthState = async (): Promise<boolean> => {
  try {
    const state = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.AUTH_STATE);
    return state === 'true';
  } catch (error) {
    console.error('Error getting auth state:', error);
    return false;
  }
};

/**
 * Save user role
 */
export const saveUserRole = async (role: UserRole): Promise<void> => {
  try {
    // Save enum value
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.USER_ROLE, role.toString());

    // Save display value (provider/customer)
    const displayRole = role === UserRole.PROVIDER ? 'provider' : 'customer';
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.USER_ROLE_DISPLAY, displayRole);

    // Also save to regular storage for backward compatibility
    await Storage.setItem(STORAGE_KEYS.USER_ROLE, displayRole);

    console.log('User role saved:', role);
  } catch (error) {
    console.error('Error saving user role:', error);
  }
};

/**
 * Get user role
 */
export const getUserRole = async (): Promise<UserRole | null> => {
  try {
    // Try SecureStore first
    const roleStr = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.USER_ROLE);

    if (roleStr) {
      return roleStr as UserRole;
    }

    // If not found, try regular storage
    const displayRole = await Storage.getItem(STORAGE_KEYS.USER_ROLE);

    if (displayRole === 'provider') {
      return UserRole.PROVIDER;
    } else if (displayRole === 'customer') {
      return UserRole.CUSTOMER;
    }

    return null;
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
};

/**
 * Save profile data
 */
export const saveProfileData = async (profile: any): Promise<void> => {
  try {
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.PROFILE_DATA, JSON.stringify(profile));
    console.log('Profile data saved successfully');
  } catch (error) {
    console.error('Error saving profile data:', error);
  }
};

/**
 * Get profile data
 */
export const getProfileData = async (): Promise<any | null> => {
  try {
    const profileData = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.PROFILE_DATA);
    return profileData ? JSON.parse(profileData) : null;
  } catch (error) {
    console.error('Error getting profile data:', error);
    return null;
  }
};

/**
 * Save remember me preferences
 */
export const saveRememberMe = async (email: string, rememberMe: boolean): Promise<void> => {
  try {
    if (rememberMe) {
      await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.CACHED_EMAIL, email);
      await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.REMEMBER_ME, 'true');
    } else {
      await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.REMEMBER_ME);
      await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.CACHED_EMAIL);
    }
  } catch (error) {
    console.error('Error saving remember me preferences:', error);
  }
};

/**
 * Get remembered email
 */
export const getRememberedEmail = async (): Promise<string | null> => {
  try {
    const rememberMe = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.REMEMBER_ME);

    if (rememberMe === 'true') {
      return await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.CACHED_EMAIL);
    }

    return null;
  } catch (error) {
    console.error('Error getting remembered email:', error);
    return null;
  }
};

/**
 * Save offline credentials
 */
export const saveOfflineCredentials = async (email: string, password: string): Promise<void> => {
  try {
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.CACHED_EMAIL, email);
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.CACHED_PASSWORD, password);
    console.log('Offline credentials saved');
  } catch (error) {
    console.error('Error saving offline credentials:', error);
  }
};

/**
 * Save onboarding completion status
 */
export const saveOnboardingStatus = async (completed: boolean): Promise<void> => {
  try {
    // Save to SecureStore
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.ONBOARDING_COMPLETED, completed ? 'true' : 'false');

    // Also save to regular storage for backward compatibility
    await Storage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, completed ? 'true' : 'false');

    console.log('Onboarding status saved:', completed);
  } catch (error) {
    console.error('Error saving onboarding status:', error);
  }
};

/**
 * Get onboarding completion status
 */
export const getOnboardingStatus = async (): Promise<boolean> => {
  try {
    // Try SecureStore first
    const status = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.ONBOARDING_COMPLETED);

    if (status !== null) {
      return status === 'true';
    }

    // If not found, try regular storage
    const regularStatus = await Storage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETED);

    return regularStatus === 'true';
  } catch (error) {
    console.error('Error getting onboarding status:', error);
    return false;
  }
};

/**
 * Reset onboarding status (useful for testing or app updates)
 */
export const resetOnboardingStatus = async (): Promise<void> => {
  try {
    // Remove from SecureStore
    await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.ONBOARDING_COMPLETED);

    // Remove from regular storage
    await Storage.removeItem(STORAGE_KEYS.ONBOARDING_COMPLETED);

    console.log('Onboarding status reset successfully');
  } catch (error) {
    console.error('Error resetting onboarding status:', error);
  }
};

/**
 * Check if this is a fresh app install (no previous data)
 */
export const isFreshInstall = async (): Promise<boolean> => {
  try {
    // Check for any existing data that would indicate previous usage
    const hasOnboardingData = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.ONBOARDING_COMPLETED);
    const hasAuthData = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.ACCESS_TOKEN);
    const hasUserData = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.USER_DATA);

    // If none of these exist, it's likely a fresh install
    return !hasOnboardingData && !hasAuthData && !hasUserData;
  } catch (error) {
    console.error('Error checking fresh install status:', error);
    return true; // Default to fresh install on error
  }
};

/**
 * Clear all app data (useful for testing or complete reset)
 */
export const clearAllAppData = async (): Promise<void> => {
  try {
    // Clear all SecureStore data
    const secureKeys = Object.values(SECURE_STORAGE_KEYS);
    for (const key of secureKeys) {
      try {
        await SecureStore.deleteItemAsync(key);
      } catch (error) {
        // Ignore errors for non-existent keys
      }
    }

    // Clear all regular storage data
    const storageKeys = Object.values(STORAGE_KEYS);
    for (const key of storageKeys) {
      try {
        await Storage.removeItem(key);
      } catch (error) {
        // Ignore errors for non-existent keys
      }
    }

    console.log('All app data cleared successfully');
  } catch (error) {
    console.error('Error clearing app data:', error);
  }
};

/**
 * Clear all authentication data
 */
export const clearAuthData = async (): Promise<void> => {
  try {
    // Clear SecureStore items
    await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.ACCESS_TOKEN);
    await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.REFRESH_TOKEN);
    await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.USER_DATA);
    await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.AUTH_STATE);
    await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.USER_ROLE);
    await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.USER_ROLE_DISPLAY);
    await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.PROFILE_DATA);

    // Don't clear remember me preferences unless explicitly requested
    // Don't clear onboarding status - it should persist across logouts

    // Clear regular storage items
    await Storage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    await Storage.removeItem(STORAGE_KEYS.USER_DATA);
    await Storage.removeItem(STORAGE_KEYS.USER_ROLE);

    // Set auth state to false
    await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.AUTH_STATE, 'false');

    console.log('All auth data cleared successfully');
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
};
