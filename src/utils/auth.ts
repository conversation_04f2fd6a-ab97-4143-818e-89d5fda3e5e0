import Storage from './storage';
import { STORAGE_KEYS } from '../config/constants';

// Get the authentication token from storage
export const getAuthToken = async (): Promise<string | null> => {
  try {
    // First try to get token from SecureStore (used by auth service)
    try {
      const secureToken = await import('expo-secure-store').then(SecureStore =>
        SecureStore.getItemAsync('auth_token')
      );
      if (secureToken) {
        console.log('Found token in SecureStore');
        return secureToken;
      }
    } catch (secureError) {
      console.log('Error accessing SecureStore:', secureError);
      // Continue to try regular storage
    }

    // Fallback to regular storage
    const token = await Storage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    if (token) {
      console.log('Found token in regular storage');
    } else {
      console.log('No token found in any storage');
    }
    return token;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Save the authentication token to storage
export const saveAuthToken = async (token: string): Promise<void> => {
  try {
    // Save to regular storage
    await Storage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);

    // Also save to SecureStore for auth service
    try {
      const SecureStore = await import('expo-secure-store');
      await SecureStore.setItemAsync('auth_token', token);
      console.log('Token saved to both storages');
    } catch (secureError) {
      console.log('Error saving to SecureStore:', secureError);
      // Continue even if SecureStore fails
    }
  } catch (error) {
    console.error('Error saving auth token:', error);
  }
};

// Remove the authentication token from storage
export const removeAuthToken = async (): Promise<void> => {
  try {
    // Remove from regular storage
    await Storage.removeItem(STORAGE_KEYS.AUTH_TOKEN);

    // Also remove from SecureStore
    try {
      const SecureStore = await import('expo-secure-store');
      await SecureStore.deleteItemAsync('auth_token');
      console.log('Token removed from both storages');
    } catch (secureError) {
      console.log('Error removing from SecureStore:', secureError);
      // Continue even if SecureStore fails
    }
  } catch (error) {
    console.error('Error removing auth token:', error);
  }
};
