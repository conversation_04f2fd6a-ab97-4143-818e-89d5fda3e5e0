import { Alert, Platform } from 'react-native';
import {
  launchCamera,
  launchImageLibrary,
  ImagePickerResponse,
  Asset,
  CameraOptions,
  ImageLibraryOptions,
} from 'react-native-image-picker';

/**
 * Pick an image from the camera
 */
export const takePhoto = async (options: Partial<CameraOptions> = {}): Promise<string | null> => {
  try {
    const defaultOptions: CameraOptions = {
      mediaType: 'photo',
      includeBase64: true,
      maxHeight: 1200,
      maxWidth: 1200,
      quality: 0.8,
      saveToPhotos: false,
      ...options,
    };

    const response = await new Promise<ImagePickerResponse>((resolve) => {
      launchCamera(defaultOptions, resolve);
    });

    if (response.didCancel) {
      console.log('User cancelled camera picker');
      return null;
    }

    if (response.errorCode) {
      console.error('Camera Error:', response.errorMessage);
      Alert.alert('Error', response.errorMessage || 'Something went wrong with the camera');
      return null;
    }

    if (!response.assets || response.assets.length === 0) {
      console.log('No image selected');
      return null;
    }

    const asset = response.assets[0];
    return getImageData(asset);
  } catch (error) {
    console.error('Error taking photo:', error);
    Alert.alert('Error', 'Failed to take photo. Please try again.');
    return null;
  }
};

/**
 * Pick an image from the gallery
 */
export const pickFromGallery = async (options: Partial<ImageLibraryOptions> = {}): Promise<string | null> => {
  try {
    const defaultOptions: ImageLibraryOptions = {
      mediaType: 'photo',
      includeBase64: true,
      maxHeight: 1200,
      maxWidth: 1200,
      quality: 0.8,
      selectionLimit: 1,
      ...options,
    };

    const response = await new Promise<ImagePickerResponse>((resolve) => {
      launchImageLibrary(defaultOptions, resolve);
    });

    if (response.didCancel) {
      console.log('User cancelled image picker');
      return null;
    }

    if (response.errorCode) {
      console.error('ImagePicker Error:', response.errorMessage);
      Alert.alert('Error', response.errorMessage || 'Something went wrong with the image picker');
      return null;
    }

    if (!response.assets || response.assets.length === 0) {
      console.log('No image selected');
      return null;
    }

    const asset = response.assets[0];
    return getImageData(asset);
  } catch (error) {
    console.error('Error picking image:', error);
    Alert.alert('Error', 'Failed to pick image. Please try again.');
    return null;
  }
};

/**
 * Get image data from asset
 */
const getImageData = (asset: Asset): string | null => {
  if (asset.base64) {
    return `data:image/jpeg;base64,${asset.base64}`;
  }
  
  if (asset.uri) {
    return asset.uri;
  }
  
  return null;
};

/**
 * Show image picker options (camera or gallery)
 */
export const showImagePickerOptions = async (): Promise<string | null> => {
  return new Promise((resolve) => {
    Alert.alert(
      'Upload Image',
      'Choose an option',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => resolve(null),
        },
        {
          text: 'Take Photo',
          onPress: async () => {
            const result = await takePhoto();
            resolve(result);
          },
        },
        {
          text: 'Choose from Gallery',
          onPress: async () => {
            const result = await pickFromGallery();
            resolve(result);
          },
        },
      ],
      { cancelable: true }
    );
  });
};
