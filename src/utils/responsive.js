// Responsive Utilities
// Enhanced responsive design utilities for consistent sizing across devices

import { Dimensions, PixelRatio } from 'react-native'

// Get device dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window')

// Base dimensions for scaling (iPhone 6/7/8 as reference)
const BASE_WIDTH = 375
const BASE_HEIGHT = 667

// Responsive width percentage
export const wp = (percentage) => {
  const value = (percentage * SCREEN_WIDTH) / 100
  return Math.round(PixelRatio.roundToNearestPixel(value))
}

// Responsive height percentage
export const hp = (percentage) => {
  const value = (percentage * SCREEN_HEIGHT) / 100
  return Math.round(PixelRatio.roundToNearestPixel(value))
}

// Responsive font size based on screen width
export const rf = (size) => {
  const scale = SCREEN_WIDTH / BASE_WIDTH
  const newSize = size * scale
  return Math.round(PixelRatio.roundToNearestPixel(newSize))
}

// Responsive spacing based on screen width
export const rs = (size) => {
  const scale = SCREEN_WIDTH / BASE_WIDTH
  const newSize = size * scale
  return Math.round(PixelRatio.roundToNearestPixel(newSize))
}

// Device type detection
export const isTablet = () => {
  const pixelDensity = PixelRatio.get()
  const adjustedWidth = SCREEN_WIDTH * pixelDensity
  const adjustedHeight = SCREEN_HEIGHT * pixelDensity
  
  if (pixelDensity < 2 && (adjustedWidth >= 1000 || adjustedHeight >= 1000)) {
    return true
  } else if (pixelDensity === 2 && (adjustedWidth >= 1920 || adjustedHeight >= 1920)) {
    return true
  } else {
    return false
  }
}

export const isSmallDevice = () => {
  return SCREEN_WIDTH < 360 || SCREEN_HEIGHT < 600
}

export const isLargeDevice = () => {
  return SCREEN_WIDTH > 414 || SCREEN_HEIGHT > 800
}

// Breakpoint utilities
export const breakpoints = {
  xs: 0,
  sm: 360,
  md: 480,
  lg: 768,
  xl: 1024,
}

export const getBreakpoint = () => {
  if (SCREEN_WIDTH >= breakpoints.xl) return 'xl'
  if (SCREEN_WIDTH >= breakpoints.lg) return 'lg'
  if (SCREEN_WIDTH >= breakpoints.md) return 'md'
  if (SCREEN_WIDTH >= breakpoints.sm) return 'sm'
  return 'xs'
}

// Responsive value selector
export const responsiveValue = (values) => {
  const currentBreakpoint = getBreakpoint()
  
  // Return the value for current breakpoint or fallback to smaller ones
  if (values[currentBreakpoint] !== undefined) {
    return values[currentBreakpoint]
  }
  
  // Fallback logic
  const breakpointOrder = ['xl', 'lg', 'md', 'sm', 'xs']
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint)
  
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i]
    if (values[bp] !== undefined) {
      return values[bp]
    }
  }
  
  // Return the first available value as last resort
  return Object.values(values)[0]
}

// Responsive style creator
export const createResponsiveStyle = (styleObject) => {
  const result = {}
  
  Object.keys(styleObject).forEach(key => {
    const value = styleObject[key]
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      // Check if it's a responsive object (has breakpoint keys)
      const hasBreakpoints = Object.keys(value).some(k => 
        Object.keys(breakpoints).includes(k)
      )
      
      if (hasBreakpoints) {
        result[key] = responsiveValue(value)
      } else {
        result[key] = value
      }
    } else {
      result[key] = value
    }
  })
  
  return result
}

// Common responsive dimensions
export const responsiveDimensions = {
  // Button heights
  buttonHeight: {
    small: responsiveValue({ xs: 32, md: 36, lg: 40 }),
    medium: responsiveValue({ xs: 40, md: 44, lg: 48 }),
    large: responsiveValue({ xs: 48, md: 52, lg: 56 }),
  },
  
  // Input heights
  inputHeight: responsiveValue({ xs: 40, md: 44, lg: 48 }),
  
  // Header heights
  headerHeight: responsiveValue({ xs: 56, md: 60, lg: 64 }),
  
  // Tab bar heights
  tabBarHeight: responsiveValue({ xs: 60, md: 65, lg: 70 }),
  
  // Icon sizes
  iconSize: {
    xs: responsiveValue({ xs: 12, md: 14, lg: 16 }),
    sm: responsiveValue({ xs: 16, md: 18, lg: 20 }),
    md: responsiveValue({ xs: 20, md: 22, lg: 24 }),
    lg: responsiveValue({ xs: 24, md: 26, lg: 28 }),
    xl: responsiveValue({ xs: 32, md: 36, lg: 40 }),
  },
  
  // Avatar sizes
  avatarSize: {
    xs: responsiveValue({ xs: 24, md: 28, lg: 32 }),
    sm: responsiveValue({ xs: 32, md: 36, lg: 40 }),
    md: responsiveValue({ xs: 40, md: 44, lg: 48 }),
    lg: responsiveValue({ xs: 56, md: 60, lg: 64 }),
    xl: responsiveValue({ xs: 72, md: 80, lg: 88 }),
  },
}

// Responsive spacing
export const responsiveSpacing = {
  xs: responsiveValue({ xs: 2, md: 3, lg: 4 }),
  sm: responsiveValue({ xs: 4, md: 6, lg: 8 }),
  md: responsiveValue({ xs: 8, md: 12, lg: 16 }),
  lg: responsiveValue({ xs: 16, md: 20, lg: 24 }),
  xl: responsiveValue({ xs: 24, md: 28, lg: 32 }),
  xxl: responsiveValue({ xs: 32, md: 40, lg: 48 }),
}

// Responsive font sizes
export const responsiveFontSizes = {
  xs: responsiveValue({ xs: 10, md: 11, lg: 12 }),
  sm: responsiveValue({ xs: 12, md: 13, lg: 14 }),
  md: responsiveValue({ xs: 14, md: 15, lg: 16 }),
  lg: responsiveValue({ xs: 16, md: 17, lg: 18 }),
  xl: responsiveValue({ xs: 18, md: 19, lg: 20 }),
  xxl: responsiveValue({ xs: 20, md: 22, lg: 24 }),
  xxxl: responsiveValue({ xs: 24, md: 26, lg: 28 }),
  display: responsiveValue({ xs: 28, md: 30, lg: 32 }),
  hero: responsiveValue({ xs: 32, md: 36, lg: 40 }),
}

export default {
  wp,
  hp,
  rf,
  rs,
  isTablet,
  isSmallDevice,
  isLargeDevice,
  getBreakpoint,
  responsiveValue,
  createResponsiveStyle,
  responsiveDimensions,
  responsiveSpacing,
  responsiveFontSizes,
}
