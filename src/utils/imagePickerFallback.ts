import { Alert } from 'react-native';
import * as ExpoImagePicker from 'expo-image-picker';

// Try to import react-native-image-picker, but don't fail if it's not available
let ReactNativeImagePicker: any = null;
try {
  ReactNativeImagePicker = require('react-native-image-picker');
} catch (error) {
  console.log('react-native-image-picker not available, using expo-image-picker only');
}

/**
 * Pick an image using the best available image picker
 */
export const pickImage = async (useCamera: boolean = false): Promise<string | null> => {
  try {
    // Try Expo Image Picker first
    try {
      console.log('Trying Expo Image Picker...');

      // Request permissions
      if (useCamera) {
        const { status } = await ExpoImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission needed', 'Please grant camera permissions to take photos');
          return null;
        }
      } else {
        const { status } = await ExpoImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission needed', 'Please grant media library permissions to select photos');
          return null;
        }
      }

      // Launch picker
      const pickerMethod = useCamera
        ? ExpoImagePicker.launchCameraAsync
        : ExpoImagePicker.launchImageLibraryAsync;

      const result = await pickerMethod({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true,
      });

      console.log('Expo Image Picker result:', result.canceled ? 'Canceled' : 'Success');

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        if (asset.base64) {
          return `data:image/jpeg;base64,${asset.base64}`;
        } else if (asset.uri) {
          return asset.uri;
        }
      }

      // If we get here with a non-canceled result but no image, it's an error
      if (!result.canceled) {
        throw new Error('No image data returned from Expo Image Picker');
      }

      // If canceled, return null
      return null;
    } catch (expoError) {
      console.warn('Expo Image Picker failed:', expoError);

      // If React Native Image Picker is available, try that as fallback
      if (ReactNativeImagePicker) {
        console.log('Falling back to React Native Image Picker...');

        return await useReactNativeImagePicker(useCamera);
      }

      // If we get here, both methods failed
      throw expoError;
    }
  } catch (error) {
    console.error('Image picker error:', error);
    Alert.alert(
      'Image Picker Error',
      `Failed to pick image: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return null;
  }
};

/**
 * Use React Native Image Picker as fallback
 */
const useReactNativeImagePicker = async (useCamera: boolean): Promise<string | null> => {
  if (!ReactNativeImagePicker) {
    throw new Error('React Native Image Picker not available');
  }

  const { launchCamera, launchImageLibrary } = ReactNativeImagePicker;

  const options = {
    mediaType: 'photo',
    includeBase64: true,
    maxHeight: 1600,
    maxWidth: 1200,
    quality: 0.8,
  };

  const method = useCamera ? launchCamera : launchImageLibrary;

  return new Promise((resolve, reject) => {
    method(options, (response: any) => {
      if (response.didCancel) {
        resolve(null);
        return;
      }

      if (response.errorCode) {
        reject(new Error(response.errorMessage || 'Image picker error'));
        return;
      }

      if (!response.assets || response.assets.length === 0) {
        reject(new Error('No image selected'));
        return;
      }

      const asset = response.assets[0];

      if (asset.base64) {
        resolve(`data:image/jpeg;base64,${asset.base64}`);
      } else if (asset.uri) {
        resolve(asset.uri);
      } else {
        reject(new Error('No image data available'));
      }
    });
  });
};

/**
 * Show image picker options (camera or gallery)
 */
export const showImagePickerOptions = async (): Promise<string | null> => {
  return new Promise((resolve) => {
    Alert.alert(
      'Upload Image',
      'Choose an option',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => resolve(null),
        },
        {
          text: 'Take Photo',
          onPress: async () => {
            const result = await pickImage(true);
            resolve(result);
          },
        },
        {
          text: 'Choose from Gallery',
          onPress: async () => {
            const result = await pickImage(false);
            resolve(result);
          },
        },
      ],
      { cancelable: true }
    );
  });
};
