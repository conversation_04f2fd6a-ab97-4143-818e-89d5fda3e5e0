import { Platform } from 'react-native';

/**
 * Helper utility specifically for physical device testing
 * This is now UI-only with no backend API calls
 */

// Helper functions for common operations - UI only, no backend calls
export const physicalDeviceApi = {
  // Auth endpoints - UI only, no backend calls
  login: (email: string, password: string) => {
    console.log('UI-only physical device login with:', { email, passwordLength: password.length });

    // Return a mock successful response
    return Promise.resolve({
      data: {
        success: true,
        data: {
          user: {
            id: 'user-' + Date.now(),
            email: email,
            firstName: email.split('@')[0],
            lastName: 'User',
            role: 'CUSTOMER'
          },
          token: 'mock-token-' + Date.now()
        }
      }
    });
  },

  register: (userData: any) => {
    console.log('UI-only physical device register with:', { ...userData, password: '[MASKED]' });

    // Return a mock successful response
    return Promise.resolve({
      data: {
        success: true,
        data: {
          user: {
            id: 'user-' + Date.now(),
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            role: userData.role || 'CUSTOMER'
          },
          token: 'mock-token-' + Date.now()
        }
      }
    });
  },

  verifyOtp: (email: string, code: string, purpose: string) => {
    console.log('UI-only physical device verify OTP:', { email, code, purpose });

    // Return a mock successful response
    return Promise.resolve({
      data: {
        success: true,
        data: {
          verified: true
        }
      }
    });
  },

  // Raw API access - UI only, no backend calls
  get: (endpoint: string, config = {}) => {
    console.log('UI-only physical device GET:', endpoint);
    return Promise.resolve({ data: { success: true, data: [] } });
  },

  post: (endpoint: string, data = {}, config = {}) => {
    console.log('UI-only physical device POST:', endpoint, data);
    return Promise.resolve({ data: { success: true, data: {} } });
  },

  put: (endpoint: string, data = {}, config = {}) => {
    console.log('UI-only physical device PUT:', endpoint, data);
    return Promise.resolve({ data: { success: true, data: {} } });
  },

  delete: (endpoint: string, config = {}) => {
    console.log('UI-only physical device DELETE:', endpoint);
    return Promise.resolve({ data: { success: true, data: {} } });
  },
};

// Helper function to check if running on a physical device
export const isPhysicalDevice = (): boolean => {
  // This is a simple check - in a real app, you'd use expo-device
  return Platform.OS === 'android' || Platform.OS === 'ios';
};
