import { Address } from './user';
import { Service } from './service';

// Booking status enum
export enum BookingStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REJECTED = 'REJECTED',
}

// Provider interface for booking
export interface BookingProvider {
  id: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    profileImage?: string;
  };
}

// Customer interface for booking
export interface BookingCustomer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  profileImage?: string;
}

// Review interface
export interface Review {
  id: string;
  bookingId: string;
  customerId: string;
  providerId: string;
  rating: number;
  comment?: string;
  createdAt: string;
}

// Booking interface
export interface Booking {
  id: string;
  customerId: string;
  providerId?: string;
  serviceId: string;
  addressId: string;
  date: string;
  startTime: string;
  endTime: string;
  status: BookingStatus;
  price: number;
  notes?: string;
  paymentMethod?: string;
  paymentStatus?: string;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  service?: Service;
  address?: Address;
  provider?: BookingProvider;
  customer?: BookingCustomer;
  review?: Review;
}
