// User role enum
export enum UserRole {
  CUSTOMER = 'CUSTOMER',
  PROVIDER = 'PROVIDER',
  ADMIN = 'ADMIN',
}

// User interface
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  profileImage?: string;
  role: UserRole;
  createdAt?: string;
}

// Customer profile interface
export interface CustomerProfile extends User {
  addresses?: Address[];
}

// Provider profile interface
export interface ProviderProfile extends User {
  providerProfile?: {
    id: string;
    bio?: string;
    experience?: number;
    tools?: string;
    workingAreas?: string;
    averageRating?: number;
    totalJobs: number;
    completedJobs: number;
    reliability?: number;
    verified: boolean;
    active: boolean;
  };
}

// Address interface
export interface Address {
  id: string;
  label: string;
  address: string;
  area?: string;
  isPrimary?: boolean;
  latitude?: number;
  longitude?: number;
  plusCode?: string;
}
