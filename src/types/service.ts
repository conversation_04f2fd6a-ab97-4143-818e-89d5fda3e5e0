// Service interface - matches the database schema with UI extensions
export interface Service {
  id: string;
  name: string;           // In database this is 'name', but we'll map to 'title' for UI consistency
  title?: string;         // Alias for 'name' for backward compatibility
  description: string;
  price: number;          // In database this is 'price'
  basePrice?: number;     // Alias for 'price' for backward compatibility
  duration?: string;      // Calculated or provided duration (e.g., "2 hours")
  estimatedDuration?: number; // Duration in minutes for backward compatibility
  image?: string;         // URL to service image (not in DB schema, but used in UI)
  features?: string[];    // List of features/what's included
  rating?: number;        // Average rating from reviews
  reviewCount?: number;   // Number of reviews
  reviews?: Review[];     // List of reviews
  longDescription?: string; // Extended description for details page
  isActive: boolean;      // Whether the service is active
  createdAt?: string;
  updatedAt?: string;

  // UI-specific properties (not in database)
  icon?: string;          // Icon name for UI display (e.g., "home", "truck")
  category?: string;      // Service category for grouping
}

// Service provider interface
export interface ServiceProvider {
  id: string;
  userId: string;
  name: string;
  profileImage?: string;
  averageRating?: number;
  totalJobs: number;
  completedJobs: number;
  verified: boolean;
  active: boolean;
  price: number;
}

// Service with providers interface
export interface ServiceWithProviders extends Service {
  providers: ServiceProvider[];
}
