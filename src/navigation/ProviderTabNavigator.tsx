import React from "react"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { Feather } from "@expo/vector-icons"
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useTheme } from "../context/ThemeContext"

// Import provider screens
import CleanerDashboardScreen from "../screens/CleanerDashboardScreen"
import ProviderJobsScreen from "../screens/ProviderJobsScreen"
import ProviderProfileScreen from "../screens/ProviderProfileScreen"
import JobRequestsScreen from "../screens/JobRequestsScreen"

// Define the provider tab param list
export type ProviderTabParamList = {
  ProviderHome: undefined
  JobRequests: undefined
  ProviderJobs: undefined
  ProviderProfile: undefined
}

const Tab = createBottomTabNavigator<ProviderTabParamList>()

const ProviderTabNavigator = () => {
  const theme = useTheme()
  const insets = useSafeAreaInsets()

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textLight,
        tabBarStyle: {
          borderTopColor: theme.colors.border,
          height: 60 + (insets.bottom > 0 ? insets.bottom - 8 : 0),
          paddingBottom: insets.bottom > 0 ? insets.bottom : 8,
          borderTopWidth: 1,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
        },
        headerShown: false,
      }}
    >
      <Tab.Screen
        name="ProviderHome"
        component={CleanerDashboardScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Feather name="home" size={size} color={color} />,
          tabBarLabel: "Home",
        }}
      />
      <Tab.Screen
        name="JobRequests"
        component={JobRequestsScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Feather name="bell" size={size} color={color} />,
          tabBarLabel: "Requests",
          tabBarBadge: 3, // This will show a badge with the number of new requests
        }}
      />
      <Tab.Screen
        name="ProviderJobs"
        component={ProviderJobsScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Feather name="calendar" size={size} color={color} />,
          tabBarLabel: "Jobs",
        }}
      />
      <Tab.Screen
        name="ProviderProfile"
        component={ProviderProfileScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Feather name="user" size={size} color={color} />,
          tabBarLabel: "Profile",
        }}
      />
    </Tab.Navigator>
  )
}

export default ProviderTabNavigator
