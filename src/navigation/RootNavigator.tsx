import React, { useState, useEffect } from "react"
import { createNativeStackNavigator } from "@react-navigation/native-stack"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { Feather } from "@expo/vector-icons"
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import { UserRole } from "../types/user"
import type { Location } from "../context/LocationContext"
import * as authStorage from "../utils/authStorage"
import AppLoadingScreen from "../screens/AppLoadingScreen"

// Screens
import HomeScreen from "../screens/HomeScreen"
import ServiceBrowsingScreen from "../screens/ServiceBrowsingScreen"
import ServiceDetailScreen from "../screens/ServiceDetailScreen"
import BookingScreen from "../screens/BookingScreen"
import BookingDetailsScreen from "../screens/BookingDetailsScreen"
import PaymentOptionScreen from "../screens/PaymentOptionScreen"
import BookingSuccessScreen from "../screens/BookingSuccessScreen"
import BookingHistoryScreen from "../screens/BookingHistoryScreen"
import ProfileScreen from "../screens/ProfileScreen"
import PaymentMethodsScreen from "../screens/PaymentMethodsScreen"
import RatingsReviewsScreen from "../screens/RatingsReviewsScreen"
import OnboardingScreen from "../screens/OnboardingScreen"
import RoleSelectionScreen from "../screens/RoleSelectionScreen"
import LoginScreen from "../screens/LoginScreen"
import ProviderLoginScreen from "../screens/ProviderLoginScreen"
import PhoneVerificationScreen from "../screens/PhoneVerificationScreen"
import RegisterScreen from "../screens/RegisterScreen"
import CleanerApplicationFormScreen from "../screens/CleanerApplicationFormScreen"
import CleanerDashboardScreen from "../screens/CleanerDashboardScreen"
import ProviderJobsScreen from "../screens/ProviderJobsScreen"
import ProviderProfileScreen from "../screens/ProviderProfileScreen"
import ManageAvailabilityScreen from "../screens/ManageAvailabilityScreen"
import JobRequestsScreen from "../screens/JobRequestsScreen"
import ChatScreen from "../screens/ChatScreen"
import CustomerChatScreen from "../screens/CustomerChatScreen"
import NotificationsScreen from "../screens/NotificationsScreen"
import LocationSelectionScreen from "../screens/LocationSelectionScreen"
import AddAddressScreen from "../screens/AddAddressScreen"
import HeroDetailScreen from "../screens/HeroDetailScreen"
import AllHeroesScreen from "../screens/AllHeroesScreen"
import OtpVerificationScreen from "../screens/OtpVerificationScreen"
import ForgotPasswordScreen from "../screens/ForgotPasswordScreen"

// Provider Tab Navigator
import ProviderTabNavigator from "./ProviderTabNavigator"

// Define the navigation types
export type RootStackParamList = {
  Main: undefined
  Onboarding: undefined
  RoleSelection: undefined
  Login: { returnTo?: keyof RootStackParamList, returnParams?: any, fromBooking?: boolean }
  ProviderLogin: undefined
  PhoneVerification: undefined
  Register: { returnTo?: keyof RootStackParamList, returnParams?: any, fromBooking?: boolean, selectedRole?: UserRole }
  ForgotPassword: undefined
  OtpVerification: { email: string; purpose: 'register' | 'login' | 'reset' }
  CleanerApplicationForm: undefined
  ServiceBrowsing: { searchQuery?: string }
  ServiceDetail: { serviceId: string, transition?: string }
  Booking: {
    serviceId?: string,
    selectedProvider?: {
      id: string,
      name: string,
      image: string,
      rating: number,
      reviews?: number,
      verified?: boolean,
      price?: number
    }
  }
  BookingDetails: { bookingId: string, providerId?: string }
  PaymentOption: {
    bookingId: string,
    serviceId: string,
    serviceTitle: string,
    date: string,
    time: string,
    address: string,
    duration: string,
    subtotal: number,
    fee: number,
    total: number,
    providerId?: string
  }
  LocationSelection: undefined
  AddAddress: { address?: Location }
  HeroDetail: { heroId: string }
  AllHeroes: undefined
  BookingSuccess: {
    bookingId: string,
    serviceTitle?: string,
    date?: string,
    time?: string,
    providerName?: string,
    paymentMethod?: string,
    total?: number,
    customerName?: string,
    customerEmail?: string,
    customerPhone?: string,
    address?: string
  }
  BookingHistory: undefined
  PaymentMethods: undefined
  RatingsReviews: undefined
  Notifications: undefined

  // Provider routes
  Provider: undefined
  CleanerDashboard: undefined
  ManageAvailability: undefined
  ProviderJobs: undefined
  ProviderProfile: undefined
  JobRequests: undefined
  Chat: { customerId: string, customerName: string, customerImage: string }
  CustomerChat: { providerId: string, providerName: string, providerImage: string }
  Notifications: undefined
}

export type MainTabParamList = {
  Home: undefined
  Search: undefined
  Bookings: undefined
  Profile: undefined
}

const Stack = createNativeStackNavigator<RootStackParamList>()
const Tab = createBottomTabNavigator<MainTabParamList>()

// Main tab navigator
const MainTabNavigator = () => {
  const theme = useTheme()
  const { isAuthenticated, userRole } = useAuth()
  const insets = useSafeAreaInsets()

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textLight,
        tabBarStyle: {
          borderTopColor: theme.colors.border,
          height: 60 + (insets.bottom > 0 ? insets.bottom - 8 : 0),
          paddingBottom: insets.bottom > 0 ? insets.bottom : 8,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          borderTopWidth: 1,
        },
        headerShown: false,
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Feather name="home" size={size} color={color} />,
        }}
      />
      <Tab.Screen
        name="Search"
        component={ServiceBrowsingScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Feather name="search" size={size} color={color} />,
        }}
      />
      <Tab.Screen
        name="Bookings"
        component={BookingHistoryScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Feather name="calendar" size={size} color={color} />,
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Feather name="user" size={size} color={color} />,
        }}
      />
    </Tab.Navigator>
  )
}

// Root stack navigator
const RootNavigator = () => {
  const { isAuthenticated, isLoading, user, userRole } = useAuth()

  // State to track onboarding status and app initialization
  const [onboardingCompleted, setOnboardingCompleted] = useState<boolean | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);

  // Check onboarding status on mount
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Check if this is a fresh install
        const isFresh = await authStorage.isFreshInstall();

        if (isFresh) {
          console.log('Fresh install detected - showing onboarding');
          setOnboardingCompleted(false);
        } else {
          // Check if onboarding has been completed for existing installations
          const status = await authStorage.getOnboardingStatus();
          setOnboardingCompleted(status);
          console.log('Existing installation - onboarding status:', status);
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Default to false for first-time users
        setOnboardingCompleted(false);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeApp();
  }, []);

  // Show loading while initializing or authenticating
  if (isLoading || isInitializing) {
    return <AppLoadingScreen />
  }

  // Determine initial route based on onboarding, authentication, and user role
  const getInitialRouteName = () => {
    // First-time users: Show onboarding
    if (!onboardingCompleted) {
      return "Onboarding";
    }

    // Returning users: Check authentication status
    if (!isAuthenticated) {
      return "RoleSelection";
    }

    // Authenticated users: Navigate based on role
    if (user?.role === UserRole.PROVIDER || userRole === UserRole.PROVIDER) {
      return "Provider";
    }

    return "Main";
  };

  return (
    <Stack.Navigator
      initialRouteName={getInitialRouteName()}
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
        animationDuration: 200,
        gestureEnabled: true,
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen name="Onboarding" component={OnboardingScreen} />
      <Stack.Screen name="RoleSelection" component={RoleSelectionScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="ProviderLogin" component={ProviderLoginScreen} />
      <Stack.Screen name="PhoneVerification" component={PhoneVerificationScreen} />
      <Stack.Screen name="Register" component={RegisterScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <Stack.Screen name="OtpVerification" component={OtpVerificationScreen} />
      <Stack.Screen name="CleanerApplicationForm" component={CleanerApplicationFormScreen} />

      {/* Customer routes */}
      <Stack.Screen name="Main" component={MainTabNavigator} />
      <Stack.Screen name="ServiceBrowsing" component={ServiceBrowsingScreen} />
      <Stack.Screen name="ServiceDetail" component={ServiceDetailScreen} />
      <Stack.Screen name="Booking" component={BookingScreen} />
      <Stack.Screen name="BookingDetails" component={BookingDetailsScreen} />
      <Stack.Screen name="PaymentOption" component={PaymentOptionScreen} />
      <Stack.Screen name="BookingSuccess" component={BookingSuccessScreen} />
      <Stack.Screen name="LocationSelection" component={LocationSelectionScreen} />
      <Stack.Screen name="AddAddress" component={AddAddressScreen} />
      <Stack.Screen name="HeroDetail" component={HeroDetailScreen} />
      <Stack.Screen name="AllHeroes" component={AllHeroesScreen} />
      <Stack.Screen name="BookingHistory" component={BookingHistoryScreen} />
      <Stack.Screen name="PaymentMethods" component={PaymentMethodsScreen} />
      <Stack.Screen name="RatingsReviews" component={RatingsReviewsScreen} />
      <Stack.Screen name="Notifications" component={NotificationsScreen} />

      {/* Provider routes */}
      <Stack.Screen name="Provider" component={ProviderTabNavigator} />
      <Stack.Screen name="CleanerDashboard" component={CleanerDashboardScreen} />
      <Stack.Screen name="ManageAvailability" component={ManageAvailabilityScreen} />
      <Stack.Screen name="ProviderJobs" component={ProviderJobsScreen} />
      <Stack.Screen name="ProviderProfile" component={ProviderProfileScreen} />
      <Stack.Screen name="JobRequests" component={JobRequestsScreen} />
      <Stack.Screen name="Chat" component={ChatScreen} />
      <Stack.Screen name="CustomerChat" component={CustomerChatScreen} />
    </Stack.Navigator>
  )
}

export default RootNavigator
