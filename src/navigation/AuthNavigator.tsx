import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import OnboardingScreen from '../screens/OnboardingScreen';
import RoleSelectionScreen from '../screens/RoleSelectionScreen';
import LoginScreen from '../screens/LoginScreen';
import ProviderLoginScreen from '../screens/ProviderLoginScreen';
import PhoneVerificationScreen from '../screens/PhoneVerificationScreen';
import RegisterScreen from '../screens/RegisterScreen';
import CleanerApplicationFormScreen from '../screens/CleanerApplicationFormScreen';
// OtpVerificationScreen removed - using modal approach instead

// Auth stack param list
export type AuthStackParamList = {
  Onboarding: undefined;
  RoleSelection: undefined;
  Login: undefined;
  ProviderLogin: undefined;
  PhoneVerification: undefined;
  Register: { selectedRole?: string };
  CleanerApplicationForm: undefined;
  // OtpVerification route removed - using modal approach instead
};

// Create stack navigator
const Stack = createNativeStackNavigator<AuthStackParamList>();

// Auth navigator component
const AuthNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="Onboarding"
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="Onboarding" component={OnboardingScreen} />
      <Stack.Screen name="RoleSelection" component={RoleSelectionScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="ProviderLogin" component={ProviderLoginScreen} />
      <Stack.Screen name="PhoneVerification" component={PhoneVerificationScreen} />
      <Stack.Screen name="Register" component={RegisterScreen} />
      <Stack.Screen name="CleanerApplicationForm" component={CleanerApplicationFormScreen} />
      {/* OtpVerification screen removed - using modal approach instead */}
    </Stack.Navigator>
  );
};

export default AuthNavigator;
