import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Feather } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CleanerDashboardScreen from '../screens/CleanerDashboardScreen';
import ProviderJobsScreen from '../screens/ProviderJobsScreen';
import ProviderProfileScreen from '../screens/ProviderProfileScreen';
import ManageAvailabilityScreen from '../screens/ManageAvailabilityScreen';
import JobRequestsScreen from '../screens/JobRequestsScreen';
import BookingDetailsScreen from '../screens/BookingDetailsScreen';
import CustomerChatScreen from '../screens/CustomerChatScreen';
import { useTheme } from '../context/ThemeContext';

// Provider stack param list
export type ProviderStackParamList = {
  Main: undefined;
  CleanerDashboard: undefined;
  ManageAvailability: undefined;
  ProviderJobs: undefined;
  ProviderProfile: undefined;
  JobRequests: undefined;
  BookingDetails: { bookingId: string };
  CustomerChat: { customerId: string; customerName: string; customerImage: string };
};

// Provider tab param list
export type ProviderTabParamList = {
  Dashboard: undefined;
  Requests: undefined;
  Jobs: undefined;
  Availability: undefined;
  Profile: undefined;
};

// Create stack navigator
const Stack = createNativeStackNavigator<ProviderStackParamList>();
const Tab = createBottomTabNavigator<ProviderTabParamList>();

// Provider tab navigator component
const ProviderTabNavigator: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ color, size }) => {
          let iconName: any = 'home';

          if (route.name === 'Dashboard') {
            iconName = 'home';
          } else if (route.name === 'Jobs') {
            iconName = 'briefcase';
          } else if (route.name === 'Availability') {
            iconName = 'calendar';
          } else if (route.name === 'Profile') {
            iconName = 'user';
          }

          return <Feather name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textLight,
        tabBarStyle: {
          height: 60 + (insets.bottom > 0 ? insets.bottom - 10 : 0),
          paddingBottom: insets.bottom > 0 ? insets.bottom : 10,
          paddingTop: 10,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          borderTopWidth: 1,
          borderTopColor: theme.colors.border,
        },
      })}
    >
      <Tab.Screen name="Dashboard" component={CleanerDashboardScreen} />
      <Tab.Screen
        name="Requests"
        component={JobRequestsScreen}
        options={{
          tabBarBadge: 3, // This will show a badge with the number of new requests
        }}
      />
      <Tab.Screen name="Jobs" component={ProviderJobsScreen} />
      <Tab.Screen name="Availability" component={ManageAvailabilityScreen} />
      <Tab.Screen name="Profile" component={ProviderProfileScreen} />
    </Tab.Navigator>
  );
};

// Provider navigator component
const ProviderNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="Main"
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="Main" component={ProviderTabNavigator} />
      <Stack.Screen name="CleanerDashboard" component={CleanerDashboardScreen} />
      <Stack.Screen name="ManageAvailability" component={ManageAvailabilityScreen} />
      <Stack.Screen name="ProviderJobs" component={ProviderJobsScreen} />
      <Stack.Screen name="ProviderProfile" component={ProviderProfileScreen} />
      <Stack.Screen name="JobRequests" component={JobRequestsScreen} />
      <Stack.Screen name="BookingDetails" component={BookingDetailsScreen} />
      <Stack.Screen name="CustomerChat" component={CustomerChatScreen} />
    </Stack.Navigator>
  );
};

export default ProviderNavigator;
