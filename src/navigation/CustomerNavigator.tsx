import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Feather } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import HomeScreen from '../screens/HomeScreen';
import ServiceBrowsingScreen from '../screens/ServiceBrowsingScreen';
import ServiceDetailScreen from '../screens/ServiceDetailScreen';
import BookingScreen from '../screens/BookingScreen';
import BookingDetailsScreen from '../screens/BookingDetailsScreen';
import PaymentOptionScreen from '../screens/PaymentOptionScreen';
import BookingSuccessScreen from '../screens/BookingSuccessScreen';
import BookingHistoryScreen from '../screens/BookingHistoryScreen';
import ProfileScreen from '../screens/ProfileScreen';
import PaymentMethodsScreen from '../screens/PaymentMethodsScreen';
import RatingsReviewsScreen from '../screens/RatingsReviewsScreen';
import AddAddressScreen from '../screens/AddAddressScreen';
import ChatScreen from '../screens/ChatScreen';
import { useTheme } from '../context/ThemeContext';

// Customer stack param list
export type CustomerStackParamList = {
  Main: undefined;
  ServiceBrowsing: { searchQuery?: string };
  ServiceDetail: { serviceId: string; transition?: string };
  Booking: { serviceId?: string };
  BookingDetails: { bookingId: string; providerId?: string };
  PaymentOption: {
    bookingId: string;
    serviceId: string;
    serviceTitle: string;
    date: string;
    time: string;
    address: string;
    duration: string;
    subtotal: number;
    fee: number;
    total: number;
    providerId?: string;
  };
  BookingSuccess: {
    bookingId: string,
    serviceTitle?: string,
    date?: string,
    time?: string,
    providerName?: string,
    paymentMethod?: string,
    total?: number,
    customerName?: string,
    customerEmail?: string,
    customerPhone?: string,
    address?: string
  };
  BookingHistory: undefined;
  PaymentMethods: undefined;
  RatingsReviews: undefined;
  AddAddress: { address?: any };
  Chat: { providerId: string; providerName: string; providerImage: string };
};

// Customer tab param list
export type CustomerTabParamList = {
  Home: undefined;
  Search: undefined;
  Bookings: undefined;
  Profile: undefined;
};

// Create stack navigator
const Stack = createNativeStackNavigator<CustomerStackParamList>();
const Tab = createBottomTabNavigator<CustomerTabParamList>();

// Customer tab navigator component
const CustomerTabNavigator: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ color, size }) => {
          let iconName: any = 'home';

          if (route.name === 'Home') {
            iconName = 'home';
          } else if (route.name === 'Search') {
            iconName = 'search';
          } else if (route.name === 'Bookings') {
            iconName = 'calendar';
          } else if (route.name === 'Profile') {
            iconName = 'user';
          }

          return <Feather name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textLight,
        tabBarStyle: {
          height: 60 + (insets.bottom > 0 ? insets.bottom - 10 : 0),
          paddingBottom: insets.bottom > 0 ? insets.bottom : 10,
          paddingTop: 10,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          borderTopWidth: 1,
          borderTopColor: theme.colors.border,
        },
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Search" component={ServiceBrowsingScreen} />
      <Tab.Screen name="Bookings" component={BookingHistoryScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
};

// Customer navigator component
const CustomerNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="Main"
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="Main" component={CustomerTabNavigator} />
      <Stack.Screen name="ServiceBrowsing" component={ServiceBrowsingScreen} />
      <Stack.Screen name="ServiceDetail" component={ServiceDetailScreen} />
      <Stack.Screen name="Booking" component={BookingScreen} />
      <Stack.Screen name="BookingDetails" component={BookingDetailsScreen} />
      <Stack.Screen name="PaymentOption" component={PaymentOptionScreen} />
      <Stack.Screen name="BookingSuccess" component={BookingSuccessScreen} />
      <Stack.Screen name="BookingHistory" component={BookingHistoryScreen} />
      <Stack.Screen name="PaymentMethods" component={PaymentMethodsScreen} />
      <Stack.Screen name="RatingsReviews" component={RatingsReviewsScreen} />
      <Stack.Screen name="AddAddress" component={AddAddressScreen} />
      <Stack.Screen name="Chat" component={ChatScreen} />
    </Stack.Navigator>
  );
};

export default CustomerNavigator;
