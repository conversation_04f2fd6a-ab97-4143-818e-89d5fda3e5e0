import React, { createContext, useState, useContext, useEffect } from 'react';
import Storage from '../utils/storage';
import { useAuth } from './AuthContext';
import { localDataService, STORAGE_KEYS as LOCAL_STORAGE_KEYS, gambianAddresses } from '../services/localDataService';
import { STORAGE_KEYS, DEFAULT_LOCATION } from '../config/constants';

export interface Location {
  id: string;
  label: string;
  address: string;
  area?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  isPrimary?: boolean;
}

interface LocationContextType {
  currentLocation: Location | null;
  savedLocations: Location[];
  setCurrentLocation: (location: Location) => Promise<void>;
  addLocation: (location: Location) => Promise<void>;
  removeLocation: (locationId: string) => Promise<void>;
  updateLocation: (location: Location) => Promise<void>;
  isLoading: boolean;
}

const LocationContext = createContext<LocationContextType>({
  currentLocation: DEFAULT_LOCATION,
  savedLocations: [DEFAULT_LOCATION],
  setCurrentLocation: async () => {},
  addLocation: async () => {},
  removeLocation: async () => {},
  updateLocation: async () => {},
  isLoading: true
});

export const useLocation = () => useContext(LocationContext);

export const LocationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [currentLocation, setCurrentLocationState] = useState<Location | null>(null);
  const [savedLocations, setSavedLocations] = useState<Location[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load saved locations from local storage
  useEffect(() => {
    const loadLocations = async () => {
      try {
        setIsLoading(true);
        console.log('Loading locations from local storage...');

        // Get locations from local data
        let locations: Location[] = [];
        
        // Try to get saved locations from storage first
        const savedLocationsJson = await Storage.getItem(STORAGE_KEYS.SAVED_LOCATIONS);
        
        if (savedLocationsJson) {
          locations = JSON.parse(savedLocationsJson);
          console.log(`Found ${locations.length} saved locations`);
        } else {
          // If no saved locations, use Gambian addresses as defaults
          locations = gambianAddresses;
          await Storage.setItem(STORAGE_KEYS.SAVED_LOCATIONS, JSON.stringify(locations));
          console.log('Initialized with Gambian addresses');
        }

        // Get current location from storage
        const currentLocationJson = await Storage.getItem(STORAGE_KEYS.CURRENT_LOCATION);
        let current: Location | null = null;
        
        if (currentLocationJson) {
          current = JSON.parse(currentLocationJson);
        } else if (locations.length > 0) {
          // Use the primary location or first location as current
          current = locations.find(loc => loc.isPrimary) || locations[0];
          await Storage.setItem(STORAGE_KEYS.CURRENT_LOCATION, JSON.stringify(current));
        }

        setSavedLocations(locations);
        setCurrentLocationState(current);
        console.log('Locations loaded successfully');
      } catch (error) {
        console.error('Error loading locations:', error);
        // Fallback to defaults if there's an error
        setSavedLocations([DEFAULT_LOCATION]);
        setCurrentLocationState(DEFAULT_LOCATION);
      } finally {
        setIsLoading(false);
      }
    };

    loadLocations();
  }, [isAuthenticated]);

  const setCurrentLocation = async (location: Location) => {
    try {
      console.log('Setting current location:', location.label);
      await Storage.setItem(STORAGE_KEYS.CURRENT_LOCATION, JSON.stringify(location));
      setCurrentLocationState(location);
    } catch (error) {
      console.error('Error setting current location:', error);
      // Still update state even if storage fails
      setCurrentLocationState(location);
    }
  };

  const addLocation = async (location: Location) => {
    try {
      console.log('Adding new location:', location.label);
      
      // Generate a unique ID if not provided
      const newLocation = {
        ...location,
        id: location.id || `loc_${Date.now()}`
      };

      // Check if this is the first location being added
      const isPrimary = savedLocations.length === 0 || location.isPrimary;

      // If this is marked as primary, remove primary flag from other locations
      let updatedLocations = [...savedLocations];
      if (isPrimary) {
        updatedLocations = updatedLocations.map(loc => ({
          ...loc,
          isPrimary: false
        }));
      }

      // Add the new location with primary flag if needed
      const locationToAdd = {
        ...newLocation,
        isPrimary: isPrimary
      };

      updatedLocations.push(locationToAdd);

      // Save to storage
      await Storage.setItem(STORAGE_KEYS.SAVED_LOCATIONS, JSON.stringify(updatedLocations));
      setSavedLocations(updatedLocations);

      // If this is the first location or marked as primary, set it as current
      if (isPrimary) {
        await setCurrentLocation(locationToAdd);
      }

      console.log('Location added successfully');
    } catch (error) {
      console.error('Error adding location:', error);
      throw new Error('Failed to add location');
    }
  };

  const removeLocation = async (locationId: string) => {
    try {
      console.log('Removing location:', locationId);
      
      // Don't allow removing the last location
      if (savedLocations.length <= 1) {
        throw new Error('Cannot remove the only location');
      }

      const updatedLocations = savedLocations.filter(loc => loc.id !== locationId);

      // If we removed a primary location, set a new one as primary
      const removedLocation = savedLocations.find(loc => loc.id === locationId);
      if (removedLocation?.isPrimary && updatedLocations.length > 0) {
        updatedLocations[0].isPrimary = true;
      }

      // Save to storage
      await Storage.setItem(STORAGE_KEYS.SAVED_LOCATIONS, JSON.stringify(updatedLocations));
      setSavedLocations(updatedLocations);

      // If the current location was removed, set a new current location
      if (currentLocation?.id === locationId) {
        const newCurrent = updatedLocations.find(loc => loc.isPrimary) || updatedLocations[0];
        await setCurrentLocation(newCurrent);
      }

      console.log('Location removed successfully');
    } catch (error) {
      console.error('Error removing location:', error);
      throw new Error('Failed to remove location');
    }
  };

  const updateLocation = async (location: Location) => {
    try {
      console.log('Updating location:', location.label);
      
      const index = savedLocations.findIndex(loc => loc.id === location.id);
      if (index === -1) {
        throw new Error('Location not found');
      }

      // If this location is being set as primary, remove primary flag from others
      let updatedLocations = [...savedLocations];
      if (location.isPrimary) {
        updatedLocations = updatedLocations.map(loc => ({
          ...loc,
          isPrimary: loc.id === location.id
        }));
      } else {
        updatedLocations[index] = location;
      }

      // Save to storage
      await Storage.setItem(STORAGE_KEYS.SAVED_LOCATIONS, JSON.stringify(updatedLocations));
      setSavedLocations(updatedLocations);

      // Update current location if it's the one being updated
      if (currentLocation?.id === location.id) {
        await setCurrentLocation(location);
      }

      console.log('Location updated successfully');
    } catch (error) {
      console.error('Error updating location:', error);
      throw new Error('Failed to update location');
    }
  };

  return (
    <LocationContext.Provider
      value={{
        currentLocation,
        savedLocations,
        setCurrentLocation,
        addLocation,
        removeLocation,
        updateLocation,
        isLoading
      }}
    >
      {children}
    </LocationContext.Provider>
  );
};

export default LocationContext;
