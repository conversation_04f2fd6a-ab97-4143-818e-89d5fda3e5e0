import { createContext, useContext, type ReactNode } from "react"
import { theme as designSystemTheme } from "../constants/theme"

// Use the centralized theme from design system
export const theme = designSystemTheme

type ThemeContextType = {
  theme: typeof theme
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  return <ThemeContext.Provider value={{ theme }}>{children}</ThemeContext.Provider>
}

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context.theme
}
