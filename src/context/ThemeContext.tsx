import { createContext, useContext, type ReactNode } from "react"

// Define the theme colors based on the existing color scheme
export const theme = {
  colors: {
    primary: "#6FD1FF", // Sky Blue
    primaryDark: "#4AA8E0",
    primaryLight: "#ADE1FF",
    secondary: "#A2E2BD", // Mint Green
    secondaryDark: "#5AB987",
    secondaryLight: "#C3EFD7",
    accent: "#84D3F2", // Light Cyan
    accentDark: "#3CA9DA",
    accentLight: "#B0E0F4",
    warning: "#FFD43B", // Bright Yellow
    warningDark: "#E6B000",
    warningLight: "#FFE99D",
    background: "#F5F5F5",
    card: "#FFFFFF",
    text: "#333333",
    textLight: "#666666",
    border: "#E1E1E1",
    notification: "#FF4D4F",
    success: "#52C41A",
    error: "#FF3B30",
  },
  spacing: {
    xxs: 2,
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 16,
    xl: 24,
    round: 9999,
  },
  fontSizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
  },
}

type ThemeContextType = {
  theme: typeof theme
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  return <ThemeContext.Provider value={{ theme }}>{children}</ThemeContext.Provider>
}

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context.theme
}
