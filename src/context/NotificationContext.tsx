import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { useAuth } from './AuthContext';
// Note: Notification service converted to local storage for demo

// Define the notification data interface
export interface NotificationData {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: string;
  actionRoute?: string;
  actionParams?: Record<string, any>;
}

// Define the context type
interface NotificationContextType {
  notifications: NotificationData[];
  unreadCount: number;
  isLoading: boolean;
  isRefreshing: boolean;
  isOffline: boolean;
  loadNotifications: (showFullLoading?: boolean) => Promise<void>;
  markAsRead: (notificationId: string) => Promise<boolean>;
  markAllAsRead: () => Promise<boolean>;
  deleteNotification: (notificationId: string) => Promise<boolean>;
  handleRefresh: () => void;
}

// Create the context
const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  isRefreshing: false,
  isOffline: false,
  loadNotifications: async () => {},
  markAsRead: async () => false,
  markAllAsRead: async () => false,
  deleteNotification: async () => false,
  handleRefresh: () => {},
});

// Storage key
const NOTIFICATIONS_STORAGE_KEY = 'user_notifications_data';
const UNREAD_COUNT_STORAGE_KEY = 'user_notifications_unread_count';

// Provider component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [isOffline, setIsOffline] = useState<boolean>(false);

  // Load notifications data
  const loadNotifications = async (showFullLoading = true) => {
    if (!isAuthenticated || !user) {
      setNotifications([]);
      setUnreadCount(0);
      setIsLoading(false);
      return;
    }

    try {
      if (showFullLoading) {
        setIsLoading(true);
      }
      setIsOffline(false);

      // Get notifications data from local storage (demo mode)
      const cachedData = await SecureStore.getItemAsync(NOTIFICATIONS_STORAGE_KEY);
      let data: NotificationData[] = [];

      if (cachedData) {
        data = JSON.parse(cachedData);
      } else {
        // Initialize with sample notifications for demo
        data = [
          {
            id: '1',
            title: 'Booking Confirmed',
            message: 'Your cleaning service booking has been confirmed for tomorrow at 10:00 AM',
            type: 'booking',
            isRead: false,
            createdAt: new Date().toISOString(),
            actionRoute: 'BookingDetails',
            actionParams: { bookingId: 'BK-001' }
          },
          {
            id: '2',
            title: 'Service Completed',
            message: 'Your cleaning service has been completed. Please rate your experience.',
            type: 'service',
            isRead: true,
            createdAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
          },
          {
            id: '3',
            title: 'New Provider Available',
            message: 'A new highly-rated cleaner is now available in your area.',
            type: 'promotion',
            isRead: false,
            createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          }
        ];
        await SecureStore.setItemAsync(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(data));
      }

      setNotifications(data);

      // Calculate unread count
      const count = data.filter(n => !n.isRead).length;
      setUnreadCount(count);
      await SecureStore.setItemAsync(UNREAD_COUNT_STORAGE_KEY, count.toString());
    } catch (error) {
      console.error('Error loading notifications:', error);

      // Try to load from cache if available
      try {
        const cachedData = await SecureStore.getItemAsync(NOTIFICATIONS_STORAGE_KEY);
        const cachedCount = await SecureStore.getItemAsync(UNREAD_COUNT_STORAGE_KEY);
        
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          setNotifications(parsedData);
          console.log('Loaded notifications from cache');

          if (cachedCount) {
            setUnreadCount(parseInt(cachedCount, 10));
          }

          // Show offline indicator for network errors
          if (error instanceof Error && error.message.includes('Network')) {
            setIsOffline(true);
          } else if (showFullLoading) {
            Alert.alert('Error', 'Failed to load notifications. Using cached data.');
          }
        } else if (showFullLoading) {
          Alert.alert('Error', 'Failed to load notifications. Please try again.');
        }
      } catch (cacheError) {
        console.error('Error loading cached notifications:', cacheError);
        if (showFullLoading) {
          Alert.alert('Error', 'Failed to load notifications. Please try again.');
        }
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId: string): Promise<boolean> => {
    try {
      // Mark as read locally (demo mode)

      // Update local state
      setNotifications(prevData =>
        prevData.map(item => item.id === notificationId ? { ...item, isRead: true } : item)
      );
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
      
      // Update cache
      await SecureStore.setItemAsync(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(notifications));
      await SecureStore.setItemAsync(UNREAD_COUNT_STORAGE_KEY, unreadCount.toString());
      
      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      Alert.alert('Error', 'Failed to mark notification as read. Please try again.');
      return false;
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async (): Promise<boolean> => {
    try {
      // Mark all as read locally (demo mode)

      // Update local state
      setNotifications(prevData =>
        prevData.map(item => ({ ...item, isRead: true }))
      );
      
      // Update unread count
      setUnreadCount(0);
      
      // Update cache
      await SecureStore.setItemAsync(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(notifications));
      await SecureStore.setItemAsync(UNREAD_COUNT_STORAGE_KEY, '0');
      
      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      Alert.alert('Error', 'Failed to mark all notifications as read. Please try again.');
      return false;
    }
  };

  // Delete notification
  const deleteNotification = async (notificationId: string): Promise<boolean> => {
    try {
      // Delete notification locally (demo mode)
      
      // Check if the notification was unread
      const wasUnread = notifications.find(n => n.id === notificationId && !n.isRead);
      
      // Update local state
      setNotifications(prevData => prevData.filter(item => item.id !== notificationId));
      
      // Update unread count if needed
      if (wasUnread) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      // Update cache
      await SecureStore.setItemAsync(
        NOTIFICATIONS_STORAGE_KEY, 
        JSON.stringify(notifications.filter(item => item.id !== notificationId))
      );
      await SecureStore.setItemAsync(UNREAD_COUNT_STORAGE_KEY, unreadCount.toString());
      
      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      Alert.alert('Error', 'Failed to delete notification. Please try again.');
      return false;
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    if (isAuthenticated && user) {
      setIsRefreshing(true);
      loadNotifications(false);
    }
  };

  // Load notifications when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadNotifications();
    }
  }, [isAuthenticated, user]);

  // Set up polling for new notifications (every 30 seconds)
  useEffect(() => {
    if (!isAuthenticated || !user) return;

    const pollInterval = setInterval(() => {
      // Use a lighter refresh that doesn't show the full loading screen
      loadNotifications(false);
    }, 30000); // 30 seconds

    return () => clearInterval(pollInterval);
  }, [isAuthenticated, user]);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        isLoading,
        isRefreshing,
        isOffline,
        loadNotifications,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        handleRefresh,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

// Custom hook to use the notification context
export const useNotifications = () => useContext(NotificationContext);
