import React, { createContext, useState, useEffect, useContext } from 'react';
import { User, UserRole } from '../types/user';
import { localAuthService } from '../services/localAuthService';
import { localDataService } from '../services/localDataService';

// Auth context types
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  userRole: UserRole | null;
  login: (loginData: any) => Promise<any>;
  register: (firstName: string, lastName: string, email: string, password: string, role: string, phone?: string) => Promise<any>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<any>;
  resetPassword: (email: string, otp: string, newPassword: string) => Promise<any>;
  verifyEmail: (email: string, otp: string) => Promise<any>;
  verifyOtp: (verifyData: { email?: string; phone?: string; otp: string; purpose?: string }) => Promise<any>;
  error: string | null;
  clearError: () => void;
  setUser: (user: User | null) => void;
  setIsAuthenticated: (value: boolean) => void;
  setUserRole: (role: UserRole) => Promise<void>;
  getUserRole: () => Promise<UserRole | null>;
  refreshToken: () => Promise<boolean>;
  showAuthModal: () => void;
  hideAuthModal: () => void;
  isAuthModalVisible: boolean;
}

// Create context
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  userRole: null,
  login: async () => ({}),
  register: async () => ({}),
  logout: async () => {},
  forgotPassword: async () => ({}),
  resetPassword: async () => ({}),
  verifyEmail: async () => ({}),
  verifyOtp: async () => ({}),
  error: null,
  clearError: () => {},
  setUser: () => {},
  setIsAuthenticated: () => {},
  setUserRole: async () => {},
  getUserRole: async () => null,
  refreshToken: async () => false,
  showAuthModal: () => {},
  hideAuthModal: () => {},
  isAuthModalVisible: false,
});

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [userRole, setUserRoleState] = useState<UserRole | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isAuthModalVisible, setIsAuthModalVisible] = useState<boolean>(false);

  // Clear error
  const clearError = () => setError(null);

  // Initialize local data on app start
  useEffect(() => {
    const initializeApp = async () => {
      try {
        setIsLoading(true);
        console.log('Initializing frontend-only app...');

        // Initialize local data
        await localDataService.initializeData();

        // Check if user is authenticated
        const isAuth = await localAuthService.isAuthenticated();
        
        if (isAuth) {
          console.log('User is authenticated');
          
          // Load user data
          const userData = await localAuthService.getCurrentUser();
          const role = await localAuthService.getUserRole();
          
          if (userData) {
            console.log('Loaded user data:', userData.email || userData.phone);
            setUser(userData);
            setIsAuthenticated(true);
            setUserRoleState(role);
          }
        } else {
          console.log('User is not authenticated');
          setUser(null);
          setIsAuthenticated(false);
          
          // Still load role preference if available
          const role = await localAuthService.getUserRole();
          if (role) {
            setUserRoleState(role);
          }
        }
      } catch (error) {
        console.error('Error initializing app:', error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  // Login function
  const login = async (loginData: any) => {
    try {
      setIsLoading(true);
      clearError();

      console.log('Attempting login with local auth service');
      const response = await localAuthService.login(loginData);

      if (response.success && response.data?.user) {
        const userData = response.data.user;
        
        // Update state
        setUser(userData);
        setIsAuthenticated(true);
        setUserRoleState(userData.role);

        console.log('Login successful:', userData.email || userData.phone);
        return response;
      } else {
        throw new Error(response.error || 'Login failed');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setError(error.message || 'Login failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (firstName: string, lastName: string, email: string, password: string, role: string, phone?: string) => {
    try {
      setIsLoading(true);
      clearError();

      // Convert role string to enum
      let userRoleEnum: UserRole;
      if (typeof role === 'string') {
        userRoleEnum = role.toLowerCase() === 'provider' ? UserRole.PROVIDER : UserRole.CUSTOMER;
      } else {
        userRoleEnum = role as unknown as UserRole;
      }

      const registerData = {
        firstName,
        lastName,
        email,
        password,
        phone,
        role: userRoleEnum,
      };

      console.log('Attempting registration with local auth service');
      const response = await localAuthService.register(registerData);

      if (response.success && response.data?.user) {
        const userData = response.data.user;
        
        // Update state
        setUser(userData);
        setIsAuthenticated(true);
        setUserRoleState(userData.role);

        console.log('Registration successful:', userData.email || userData.phone);
        return response;
      } else {
        throw new Error(response.error || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      setError(error.message || 'Registration failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // OTP functions (mock implementations)
  const verifyOtp = async (verifyData: { email?: string; phone?: string; otp: string; purpose?: string }) => {
    try {
      setIsLoading(true);
      clearError();

      console.log('Verifying OTP with local auth service');
      const response = await localAuthService.verifyOtp(verifyData.phone || '', verifyData.otp);

      if (response.success) {
        console.log('OTP verification successful');
        return response;
      } else {
        throw new Error(response.error || 'OTP verification failed');
      }
    } catch (error: any) {
      console.error('OTP verification error:', error);
      setError(error.message || 'Verification failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Mock functions for compatibility
  const verifyEmail = async (email: string, otp: string) => {
    return { success: true, message: 'Email verification not needed in demo mode' };
  };

  const forgotPassword = async (email: string) => {
    return { success: true, message: 'Password reset not available in demo mode' };
  };

  const resetPassword = async (email: string, otp: string, newPassword: string) => {
    return { success: true, message: 'Password reset not available in demo mode' };
  };

  const refreshToken = async (): Promise<boolean> => {
    // Always return true for demo mode
    return true;
  };

  // Set user role function
  const setUserRole = async (role: UserRole) => {
    try {
      console.log('Setting user role to:', role);
      setUserRoleState(role);

      // If a user is logged in, update their role
      if (user) {
        const updatedUser = { ...user, role };
        setUser(updatedUser);
        console.log('User role updated successfully to:', role);
      }
    } catch (error) {
      console.error('Error setting user role:', error);
    }
  };

  // Get user role function
  const getUserRole = async (): Promise<UserRole | null> => {
    try {
      if (userRole) {
        return userRole;
      }
      if (user?.role) {
        return user.role;
      }
      return await localAuthService.getUserRole();
    } catch (error) {
      console.error('Error getting user role:', error);
      return null;
    }
  };

  // Auth modal functions
  const showAuthModal = () => setIsAuthModalVisible(true);
  const hideAuthModal = () => setIsAuthModalVisible(false);

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);
      console.log('Logging out...');

      // Clear local authentication
      await localAuthService.logout();

      // Clear state
      setUser(null);
      setIsAuthenticated(false);
      // Keep userRole for next login

      console.log('Logout completed');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated,
        userRole,
        login,
        register,
        logout,
        forgotPassword,
        resetPassword,
        verifyEmail,
        verifyOtp,
        error,
        clearError,
        setUser,
        setIsAuthenticated,
        setUserRole,
        getUserRole,
        refreshToken,
        showAuthModal,
        hideAuthModal,
        isAuthModalVisible,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => useContext(AuthContext);
