import React from "react"
import { View, Image, TouchableOpacity } from "react-native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../../../context/ThemeContext"
import { Card, ResponsiveText, Badge } from "../../shared/components"
import { profileHeaderStyles } from "../styles/ProfileHeader.styles"

interface ProfileHeaderProps {
  name: string
  email?: string
  phone?: string
  avatar?: string
  rating?: number
  reviewCount?: number
  isProvider?: boolean
  isVerified?: boolean
  onEditPress?: () => void
  onAvatarPress?: () => void
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  name,
  email,
  phone,
  avatar,
  rating,
  reviewCount,
  isProvider = false,
  isVerified = false,
  onEditPress,
  onAvatarPress,
}) => {
  const theme = useTheme()
  const styles = profileHeaderStyles(theme)

  return (
    <Card style={styles.container} variant="elevated" padding="large">
      <View style={styles.content}>
        {/* Avatar Section */}
        <View style={styles.avatarSection}>
          <TouchableOpacity onPress={onAvatarPress} style={styles.avatarContainer}>
            {avatar ? (
              <Image source={{ uri: avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Feather name="user" size={40} color={theme.colors.textSecondary} />
              </View>
            )}
            <View style={styles.avatarEditButton}>
              <Feather name="camera" size={16} color={theme.colors.textInverse} />
            </View>
          </TouchableOpacity>
        </View>

        {/* Profile Info */}
        <View style={styles.infoSection}>
          <View style={styles.nameRow}>
            <ResponsiveText variant="h4" style={styles.name}>
              {name}
            </ResponsiveText>
            {isVerified && (
              <Badge variant="success" size="small">
                <Feather name="check" size={12} color={theme.colors.success} />
                <ResponsiveText variant="caption" style={styles.verifiedText}>
                  Verified
                </ResponsiveText>
              </Badge>
            )}
          </View>

          {/* Contact Info */}
          <View style={styles.contactInfo}>
            {email && (
              <View style={styles.contactRow}>
                <Feather name="mail" size={16} color={theme.colors.textSecondary} />
                <ResponsiveText variant="body" style={styles.contactText}>
                  {email}
                </ResponsiveText>
              </View>
            )}
            {phone && (
              <View style={styles.contactRow}>
                <Feather name="phone" size={16} color={theme.colors.textSecondary} />
                <ResponsiveText variant="body" style={styles.contactText}>
                  {phone}
                </ResponsiveText>
              </View>
            )}
          </View>

          {/* Provider Rating */}
          {isProvider && rating && (
            <View style={styles.ratingSection}>
              <View style={styles.ratingRow}>
                <Feather name="star" size={18} color={theme.colors.warning} />
                <ResponsiveText variant="h6" style={styles.ratingText}>
                  {rating.toFixed(1)}
                </ResponsiveText>
                <ResponsiveText variant="bodySmall" style={styles.reviewText}>
                  ({reviewCount || 0} reviews)
                </ResponsiveText>
              </View>
            </View>
          )}
        </View>

        {/* Edit Button */}
        {onEditPress && (
          <TouchableOpacity style={styles.editButton} onPress={onEditPress}>
            <Feather name="edit-2" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        )}
      </View>
    </Card>
  )
}

export default ProfileHeader
