import { StyleSheet } from 'react-native'
import { responsiveSpacing, responsiveDimensions } from '../../../utils/responsive'

export const profileHeaderStyles = (theme) => {
  return StyleSheet.create({
    container: {
      margin: responsiveSpacing.md,
    },
    content: {
      position: 'relative',
    },
    avatarSection: {
      alignItems: 'center',
      marginBottom: responsiveSpacing.lg,
    },
    avatarContainer: {
      position: 'relative',
    },
    avatar: {
      width: responsiveDimensions.avatarSize.xl,
      height: responsiveDimensions.avatarSize.xl,
      borderRadius: responsiveDimensions.avatarSize.xl / 2,
      borderWidth: 3,
      borderColor: theme.colors.primary,
    },
    avatarPlaceholder: {
      width: responsiveDimensions.avatarSize.xl,
      height: responsiveDimensions.avatarSize.xl,
      borderRadius: responsiveDimensions.avatarSize.xl / 2,
      backgroundColor: theme.colors.background,
      borderWidth: 3,
      borderColor: theme.colors.border,
      alignItems: 'center',
      justifyContent: 'center',
    },
    avatarEditButton: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      width: responsiveDimensions.buttonHeight.small,
      height: responsiveDimensions.buttonHeight.small,
      borderRadius: responsiveDimensions.buttonHeight.small / 2,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: theme.colors.card,
    },
    infoSection: {
      alignItems: 'center',
    },
    nameRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: responsiveSpacing.sm,
    },
    name: {
      textAlign: 'center',
      marginRight: responsiveSpacing.sm,
      fontWeight: theme.typography.h4.fontWeight,
    },
    verifiedText: {
      marginLeft: responsiveSpacing.xs,
      color: theme.colors.success,
    },
    contactInfo: {
      alignItems: 'center',
      marginBottom: responsiveSpacing.md,
    },
    contactRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: responsiveSpacing.xs,
    },
    contactText: {
      marginLeft: responsiveSpacing.sm,
      color: theme.colors.textSecondary,
    },
    ratingSection: {
      alignItems: 'center',
    },
    ratingRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    ratingText: {
      marginLeft: responsiveSpacing.xs,
      marginRight: responsiveSpacing.sm,
      color: theme.colors.text,
      fontWeight: theme.typography.h6.fontWeight,
    },
    reviewText: {
      color: theme.colors.textSecondary,
    },
    editButton: {
      position: 'absolute',
      top: 0,
      right: 0,
      width: responsiveDimensions.buttonHeight.medium,
      height: responsiveDimensions.buttonHeight.medium,
      borderRadius: responsiveDimensions.buttonHeight.medium / 2,
      backgroundColor: theme.colors.card,
      alignItems: 'center',
      justifyContent: 'center',
      ...theme.shadows.sm,
    },
  })
}
