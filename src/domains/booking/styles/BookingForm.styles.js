import { StyleSheet } from 'react-native'
import { responsiveSpacing, responsiveDimensions } from '../../../utils/responsive'

export const bookingFormStyles = (theme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    formCard: {
      margin: responsiveSpacing.md,
    },
    formTitle: {
      marginBottom: responsiveSpacing.lg,
      textAlign: 'center',
      fontWeight: theme.typography.h5.fontWeight,
    },
    inputContainer: {
      marginBottom: responsiveSpacing.md,
    },
    label: {
      marginBottom: responsiveSpacing.xs,
      color: theme.colors.text,
      fontWeight: theme.typography.label.fontWeight,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.components.input.borderRadius,
      backgroundColor: theme.colors.card,
      paddingHorizontal: responsiveSpacing.md,
      minHeight: responsiveDimensions.inputHeight,
    },
    inputError: {
      borderColor: theme.colors.error,
    },
    inputIcon: {
      marginRight: responsiveSpacing.sm,
    },
    input: {
      flex: 1,
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.text,
      paddingVertical: responsiveSpacing.sm,
    },
    multilineInput: {
      minHeight: responsiveDimensions.inputHeight * 2,
      textAlignVertical: 'top',
    },
    errorText: {
      marginTop: responsiveSpacing.xs,
      color: theme.colors.error,
    },
    dateTimeRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    dateTimeInput: {
      flex: 1,
      marginHorizontal: responsiveSpacing.xs,
    },
    frequencyContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: responsiveSpacing.xs,
    },
    frequencyOption: {
      paddingVertical: responsiveSpacing.sm,
      paddingHorizontal: responsiveSpacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      marginRight: responsiveSpacing.sm,
      marginBottom: responsiveSpacing.sm,
      backgroundColor: theme.colors.card,
    },
    frequencyOptionSelected: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    frequencyText: {
      color: theme.colors.text,
    },
    frequencyTextSelected: {
      color: theme.colors.textInverse,
      fontWeight: theme.typography.button.fontWeight,
    },
    submitButton: {
      marginTop: responsiveSpacing.lg,
    },
  })
}
