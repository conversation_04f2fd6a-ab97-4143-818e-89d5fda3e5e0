import { StyleSheet } from 'react-native'
import { responsiveSpacing, responsiveDimensions } from '../../../utils/responsive'

export const bookingCardStyles = (theme, { status }) => {
  const getStatusBorderColor = () => {
    switch (status) {
      case "pending":
        return theme.colors.warning
      case "confirmed":
        return theme.colors.info
      case "in-progress":
        return theme.colors.primary
      case "completed":
        return theme.colors.success
      case "cancelled":
        return theme.colors.error
      default:
        return theme.colors.border
    }
  }

  return StyleSheet.create({
    container: {
      marginBottom: responsiveSpacing.md,
      borderLeftWidth: 4,
      borderLeftColor: getStatusBorderColor(),
    },
    content: {
      padding: responsiveSpacing.md,
    },
    header: {
      marginBottom: responsiveSpacing.md,
    },
    serviceInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    serviceName: {
      flex: 1,
      marginRight: responsiveSpacing.sm,
      fontWeight: theme.typography.h6.fontWeight,
    },
    providerSection: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: responsiveSpacing.md,
    },
    providerImage: {
      width: responsiveDimensions.avatarSize.md,
      height: responsiveDimensions.avatarSize.md,
      borderRadius: responsiveDimensions.avatarSize.md / 2,
      marginRight: responsiveSpacing.md,
    },
    providerDetails: {
      flex: 1,
    },
    providerName: {
      fontWeight: theme.typography.button.fontWeight,
      marginBottom: responsiveSpacing.xs,
    },
    locationRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    address: {
      marginLeft: responsiveSpacing.xs,
      color: theme.colors.textSecondary,
      flex: 1,
    },
    dateTimeSection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: responsiveSpacing.md,
    },
    dateTimeRow: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    dateTimeText: {
      marginLeft: responsiveSpacing.xs,
      color: theme.colors.text,
    },
    priceSection: {
      alignItems: 'flex-end',
      marginBottom: responsiveSpacing.sm,
    },
    price: {
      color: theme.colors.primary,
      fontWeight: theme.typography.h6.fontWeight,
    },
    actionsSection: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: responsiveSpacing.sm,
      marginTop: responsiveSpacing.sm,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: responsiveSpacing.xs,
      paddingHorizontal: responsiveSpacing.sm,
      marginLeft: responsiveSpacing.md,
    },
    actionButtonText: {
      marginLeft: responsiveSpacing.xs,
      color: theme.colors.primary,
    },
  })
}
