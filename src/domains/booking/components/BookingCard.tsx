import type React from "react"
import { View, TouchableOpacity, Image } from "react-native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../../../context/ThemeContext"
import { Card, ResponsiveText, Badge } from "../../shared/components"
import { bookingCardStyles } from "../styles/BookingCard.styles"

interface BookingCardProps {
  id: string
  serviceName: string
  providerName: string
  providerImage: string
  date: string
  time: string
  status: "pending" | "confirmed" | "in-progress" | "completed" | "cancelled"
  price: number
  address: string
  onPress: () => void
  onCancelPress?: () => void
  onReschedulePress?: () => void
}

const BookingCard: React.FC<BookingCardProps> = ({
  serviceName,
  providerName,
  providerImage,
  date,
  time,
  status,
  price,
  address,
  onPress,
  onCancelPress,
  onReschedulePress,
}) => {
  const theme = useTheme()
  const styles = bookingCardStyles(theme, { status })

  const getStatusColor = () => {
    switch (status) {
      case "pending":
        return theme.colors.warning
      case "confirmed":
        return theme.colors.info
      case "in-progress":
        return theme.colors.primary
      case "completed":
        return theme.colors.success
      case "cancelled":
        return theme.colors.error
      default:
        return theme.colors.textSecondary
    }
  }

  const getStatusText = () => {
    switch (status) {
      case "pending":
        return "Pending"
      case "confirmed":
        return "Confirmed"
      case "in-progress":
        return "In Progress"
      case "completed":
        return "Completed"
      case "cancelled":
        return "Cancelled"
      default:
        return "Unknown"
    }
  }

  const canCancel = status === "pending" || status === "confirmed"
  const canReschedule = status === "pending" || status === "confirmed"

  return (
    <Card style={styles.container} variant="elevated" padding="none">
      <TouchableOpacity onPress={onPress} activeOpacity={0.9}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.serviceInfo}>
              <ResponsiveText variant="h6" style={styles.serviceName}>
                {serviceName}
              </ResponsiveText>
              <Badge variant={status === "completed" ? "success" : status === "cancelled" ? "danger" : "primary"}>
                {getStatusText()}
              </Badge>
            </View>
          </View>

          {/* Provider Info */}
          <View style={styles.providerSection}>
            <Image source={{ uri: providerImage }} style={styles.providerImage} />
            <View style={styles.providerDetails}>
              <ResponsiveText variant="body" style={styles.providerName}>
                {providerName}
              </ResponsiveText>
              <View style={styles.locationRow}>
                <Feather name="map-pin" size={14} color={theme.colors.textSecondary} />
                <ResponsiveText variant="bodySmall" style={styles.address} numberOfLines={1}>
                  {address}
                </ResponsiveText>
              </View>
            </View>
          </View>

          {/* Date & Time */}
          <View style={styles.dateTimeSection}>
            <View style={styles.dateTimeRow}>
              <Feather name="calendar" size={16} color={theme.colors.textSecondary} />
              <ResponsiveText variant="body" style={styles.dateTimeText}>
                {date}
              </ResponsiveText>
            </View>
            <View style={styles.dateTimeRow}>
              <Feather name="clock" size={16} color={theme.colors.textSecondary} />
              <ResponsiveText variant="body" style={styles.dateTimeText}>
                {time}
              </ResponsiveText>
            </View>
          </View>

          {/* Price */}
          <View style={styles.priceSection}>
            <ResponsiveText variant="h6" style={styles.price}>
              D{price}
            </ResponsiveText>
          </View>

          {/* Action Buttons */}
          {(canCancel || canReschedule) && (
            <View style={styles.actionsSection}>
              {canReschedule && onReschedulePress && (
                <TouchableOpacity 
                  style={styles.actionButton} 
                  onPress={onReschedulePress}
                >
                  <Feather name="calendar" size={16} color={theme.colors.primary} />
                  <ResponsiveText variant="bodySmall" style={styles.actionButtonText}>
                    Reschedule
                  </ResponsiveText>
                </TouchableOpacity>
              )}
              {canCancel && onCancelPress && (
                <TouchableOpacity 
                  style={styles.actionButton} 
                  onPress={onCancelPress}
                >
                  <Feather name="x" size={16} color={theme.colors.error} />
                  <ResponsiveText variant="bodySmall" style={[styles.actionButtonText, { color: theme.colors.error }]}>
                    Cancel
                  </ResponsiveText>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Card>
  )
}

export default BookingCard
