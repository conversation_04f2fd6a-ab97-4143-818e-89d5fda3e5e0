import React, { useState } from "react"
import { View, TextInput, TouchableOpacity, ScrollView } from "react-native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../../../context/ThemeContext"
import { Card, ResponsiveText, Button } from "../../shared/components"
import { bookingFormStyles } from "../styles/BookingForm.styles"

interface BookingFormData {
  address: string
  date: string
  time: string
  notes: string
  frequency: "one-time" | "weekly" | "bi-weekly" | "monthly"
}

interface BookingFormProps {
  initialData?: Partial<BookingFormData>
  onSubmit: (data: BookingFormData) => void
  isLoading?: boolean
  submitButtonText?: string
}

const BookingForm: React.FC<BookingFormProps> = ({
  initialData = {},
  onSubmit,
  isLoading = false,
  submitButtonText = "Book Now",
}) => {
  const theme = useTheme()
  const styles = bookingFormStyles(theme)

  const [formData, setFormData] = useState<BookingFormData>({
    address: initialData.address || "",
    date: initialData.date || "",
    time: initialData.time || "",
    notes: initialData.notes || "",
    frequency: initialData.frequency || "one-time",
  })

  const [errors, setErrors] = useState<Partial<BookingFormData>>({})

  const frequencyOptions = [
    { value: "one-time", label: "One Time" },
    { value: "weekly", label: "Weekly" },
    { value: "bi-weekly", label: "Bi-weekly" },
    { value: "monthly", label: "Monthly" },
  ]

  const updateFormData = (field: keyof BookingFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<BookingFormData> = {}

    if (!formData.address.trim()) {
      newErrors.address = "Address is required"
    }

    if (!formData.date.trim()) {
      newErrors.date = "Date is required"
    }

    if (!formData.time.trim()) {
      newErrors.time = "Time is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = () => {
    if (validateForm()) {
      onSubmit(formData)
    }
  }

  const renderInput = (
    field: keyof BookingFormData,
    label: string,
    placeholder: string,
    icon: string,
    multiline = false
  ) => (
    <View style={styles.inputContainer}>
      <ResponsiveText variant="label" style={styles.label}>
        {label}
      </ResponsiveText>
      <View style={[styles.inputWrapper, errors[field] && styles.inputError]}>
        <Feather name={icon as any} size={20} color={theme.colors.textSecondary} style={styles.inputIcon} />
        <TextInput
          style={[styles.input, multiline && styles.multilineInput]}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.textSecondary}
          value={formData[field]}
          onChangeText={(value) => updateFormData(field, value)}
          multiline={multiline}
          numberOfLines={multiline ? 3 : 1}
        />
      </View>
      {errors[field] && (
        <ResponsiveText variant="caption" style={styles.errorText}>
          {errors[field]}
        </ResponsiveText>
      )}
    </View>
  )

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Card style={styles.formCard} variant="elevated" padding="large">
        <ResponsiveText variant="h5" style={styles.formTitle}>
          Booking Details
        </ResponsiveText>

        {/* Address Input */}
        {renderInput("address", "Service Address", "Enter your address", "map-pin")}

        {/* Date and Time Row */}
        <View style={styles.dateTimeRow}>
          <View style={styles.dateTimeInput}>
            {renderInput("date", "Date", "Select date", "calendar")}
          </View>
          <View style={styles.dateTimeInput}>
            {renderInput("time", "Time", "Select time", "clock")}
          </View>
        </View>

        {/* Frequency Selection */}
        <View style={styles.inputContainer}>
          <ResponsiveText variant="label" style={styles.label}>
            Frequency
          </ResponsiveText>
          <View style={styles.frequencyContainer}>
            {frequencyOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.frequencyOption,
                  formData.frequency === option.value && styles.frequencyOptionSelected,
                ]}
                onPress={() => updateFormData("frequency", option.value)}
              >
                <ResponsiveText
                  variant="body"
                  style={[
                    styles.frequencyText,
                    formData.frequency === option.value && styles.frequencyTextSelected,
                  ]}
                >
                  {option.label}
                </ResponsiveText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Notes Input */}
        {renderInput("notes", "Special Instructions (Optional)", "Any special requests or notes...", "edit-3", true)}

        {/* Submit Button */}
        <Button
          title={submitButtonText}
          onPress={handleSubmit}
          loading={isLoading}
          disabled={isLoading}
          variant="primary"
          size="large"
          fullWidth
          style={styles.submitButton}
        />
      </Card>
    </ScrollView>
  )
}

export default BookingForm
