import { StyleSheet } from 'react-native'
import { responsiveSpacing, responsiveDimensions, wp } from '../../../utils/responsive'

export const serviceListItemStyles = (theme) => {
  return StyleSheet.create({
    container: {
      marginBottom: responsiveSpacing.md,
    },
    content: {
      flexDirection: "row",
    },
    image: {
      width: wp(30), // 30% of screen width
      height: responsiveDimensions.avatarSize.xl * 1.5,
      borderTopLeftRadius: theme.components.card.borderRadius,
      borderBottomLeftRadius: theme.components.card.borderRadius,
    },
    details: {
      flex: 1,
      padding: responsiveSpacing.md,
      justifyContent: 'space-between',
    },
    title: {
      fontWeight: theme.typography.button.fontWeight,
      marginBottom: responsiveSpacing.xs,
    },
    description: {
      color: theme.colors.textSecondary,
      marginBottom: responsiveSpacing.sm,
      lineHeight: theme.typography.bodySmall.lineHeight * theme.typography.bodySmall.fontSize,
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: responsiveSpacing.sm,
    },
    star: {
      color: theme.colors.warning,
      marginRight: responsiveSpacing.xs,
    },
    ratingText: {
      color: theme.colors.text,
      marginRight: responsiveSpacing.xs,
    },
    reviewsText: {
      color: theme.colors.textSecondary,
      marginRight: responsiveSpacing.sm,
    },
    separator: {
      width: 1,
      height: 12,
      backgroundColor: theme.colors.border,
      marginHorizontal: responsiveSpacing.sm,
    },
    durationText: {
      color: theme.colors.textSecondary,
    },
    footer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    price: {
      color: theme.colors.primary,
      fontWeight: theme.typography.button.fontWeight,
    },
  })
}
