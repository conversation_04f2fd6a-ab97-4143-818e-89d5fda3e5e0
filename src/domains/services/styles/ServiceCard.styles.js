import { StyleSheet } from 'react-native'
import { responsiveSpacing, responsiveDimensions, hp } from '../../../utils/responsive'

export const serviceCardStyles = (theme, { featured }) => {
  return StyleSheet.create({
    container: {
      marginBottom: responsiveSpacing.md,
      overflow: 'hidden',
    },
    imageContainer: {
      position: "relative",
    },
    image: {
      width: "100%",
      height: hp(20), // 20% of screen height, responsive
      borderTopLeftRadius: theme.components.card.borderRadius,
      borderTopRightRadius: theme.components.card.borderRadius,
    },
    featuredBadge: {
      position: "absolute",
      top: responsiveSpacing.sm,
      left: responsiveSpacing.sm,
      backgroundColor: theme.colors.warning,
      paddingHorizontal: responsiveSpacing.sm,
      paddingVertical: responsiveSpacing.xs,
      borderRadius: theme.borderRadius.sm,
      ...theme.shadows.sm,
    },
    featuredText: {
      color: theme.colors.text,
      fontWeight: theme.typography.button.fontWeight,
    },
    content: {
      padding: responsiveSpacing.md,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: responsiveSpacing.sm,
    },
    title: {
      flex: 1,
      marginRight: responsiveSpacing.sm,
    },
    price: {
      color: theme.colors.primary,
      fontWeight: theme.typography.button.fontWeight,
    },
    description: {
      color: theme.colors.textSecondary,
      marginBottom: responsiveSpacing.md,
      lineHeight: theme.typography.bodySmall.lineHeight * theme.typography.bodySmall.fontSize,
    },
    footer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: responsiveSpacing.md,
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    star: {
      color: theme.colors.warning,
      marginRight: responsiveSpacing.xs,
    },
    ratingText: {
      color: theme.colors.text,
      marginRight: responsiveSpacing.xs,
    },
    reviewsText: {
      color: theme.colors.textSecondary,
    },
    durationText: {
      color: theme.colors.textSecondary,
    },
  })
}
