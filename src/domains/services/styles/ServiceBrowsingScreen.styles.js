import { StyleSheet } from 'react-native'
import { responsiveSpacing, responsiveDimensions, wp, hp } from '../../../utils/responsive'

export const serviceBrowsingStyles = (theme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollView: {
      flex: 1,
    },
    searchContainer: {
      padding: responsiveSpacing.md,
      backgroundColor: theme.colors.card,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    searchInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
      borderRadius: theme.components.input.borderRadius,
      paddingHorizontal: responsiveSpacing.md,
      height: responsiveDimensions.inputHeight,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    searchIcon: {
      marginRight: responsiveSpacing.sm,
    },
    searchInput: {
      flex: 1,
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.text,
      paddingVertical: 0, // Remove default padding
    },
    clearButton: {
      padding: responsiveSpacing.xs,
      marginLeft: responsiveSpacing.sm,
    },
    categoriesContainer: {
      backgroundColor: theme.colors.card,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    categoriesContent: {
      paddingHorizontal: responsiveSpacing.md,
      paddingVertical: responsiveSpacing.sm,
    },
    categoryButton: {
      paddingHorizontal: responsiveSpacing.md,
      paddingVertical: responsiveSpacing.sm,
      marginRight: responsiveSpacing.sm,
      borderRadius: theme.borderRadius.round,
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    activeCategoryButton: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    categoryText: {
      color: theme.colors.text,
    },
    activeCategoryText: {
      color: theme.colors.textInverse,
      fontWeight: theme.typography.button.fontWeight,
    },
    section: {
      padding: responsiveSpacing.md,
    },
    sectionTitle: {
      marginBottom: responsiveSpacing.md,
      fontWeight: theme.typography.h5.fontWeight,
    },
    featuredServicesContent: {
      paddingRight: responsiveSpacing.md,
    },
    featuredServiceCard: {
      width: wp(80), // 80% of screen width
      marginRight: responsiveSpacing.md,
    },
    servicesList: {
      // No additional styles needed, ServiceListItem handles its own spacing
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: responsiveSpacing.xl,
      minHeight: hp(40),
    },
    emptyStateTitle: {
      marginTop: responsiveSpacing.md,
      marginBottom: responsiveSpacing.sm,
      textAlign: 'center',
    },
    emptyStateText: {
      textAlign: 'center',
      color: theme.colors.textSecondary,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: responsiveSpacing.xl,
    },
    errorTitle: {
      marginTop: responsiveSpacing.md,
      marginBottom: responsiveSpacing.sm,
      textAlign: 'center',
    },
    errorText: {
      textAlign: 'center',
      color: theme.colors.textSecondary,
      marginBottom: responsiveSpacing.lg,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: responsiveSpacing.lg,
      paddingVertical: responsiveSpacing.md,
      borderRadius: theme.components.button.borderRadius,
    },
    retryButtonText: {
      color: theme.colors.textInverse,
      fontWeight: theme.typography.button.fontWeight,
    },
  })
}
