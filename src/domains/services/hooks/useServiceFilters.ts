import { useState, useMemo } from 'react';

interface Service {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  rating: number;
  reviews: number;
  image: string;
  featured?: boolean;
  category?: string;
}

interface Category {
  id: string;
  name: string;
}

export const useServiceFilters = (services: Service[]) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("all");

  const categories: Category[] = [
    { id: "all", name: "All" },
    { id: "regular", name: "Regular" },
    { id: "deep", name: "Deep Clean" },
    { id: "office", name: "Office" },
    { id: "movein", name: "Move-in/out" },
    { id: "window", name: "Windows" },
  ];

  const filteredServices = useMemo(() => {
    let filtered = [...services];

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter((service) =>
        service.title.toLowerCase().includes(query) ||
        service.description.toLowerCase().includes(query)
      );
    }

    // Filter by category
    if (activeCategory !== "all") {
      filtered = filtered.filter((service) => {
        const title = service.title.toLowerCase();
        const category = service.category?.toLowerCase() || '';
        
        switch (activeCategory) {
          case "regular":
            return title.includes("regular") || category.includes("regular");
          case "deep":
            return title.includes("deep") || category.includes("deep");
          case "office":
            return title.includes("office") || category.includes("office");
          case "movein":
            return title.includes("move") || category.includes("move");
          case "window":
            return title.includes("window") || category.includes("window");
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [services, searchQuery, activeCategory]);

  const clearFilters = () => {
    setSearchQuery("");
    setActiveCategory("all");
  };

  const hasActiveFilters = searchQuery.trim() !== "" || activeCategory !== "all";

  return {
    searchQuery,
    setSearchQuery,
    activeCategory,
    setActiveCategory,
    categories,
    filteredServices,
    clearFilters,
    hasActiveFilters,
  };
};
