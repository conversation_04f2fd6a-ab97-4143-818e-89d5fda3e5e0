import { useState, useEffect, useCallback } from 'react';
import { localDataService, STORAGE_KEYS } from '../../../services/localDataService';

interface Service {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  rating: number;
  reviews: number;
  image: string;
  featured?: boolean;
  category?: string;
}

export const useServiceBrowsing = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchServices = useCallback(async (forceRefresh = false) => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch services from local data service
      const servicesData = await localDataService.getData(STORAGE_KEYS.SERVICES);

      if (servicesData && servicesData.length > 0) {
        // Map the services to include required properties
        const mappedServices = servicesData.map((service: any) => ({
          id: service.id,
          title: service.name || service.title || "Cleaning Service",
          description: service.description || "Professional cleaning service",
          price: service.price || 50,
          duration: service.duration || "2-3 hours",
          rating: service.rating || 4.5,
          reviews: service.reviewCount || service.reviews || 0,
          image: service.image || "https://via.placeholder.com/300x200",
          featured: service.featured || false,
          category: service.category || "general",
        }));

        setServices(mappedServices);
      } else {
        // Fallback to mock data if no services found
        const fallbackServices: Service[] = [
          {
            id: '1',
            title: 'Regular House Cleaning',
            description: 'Complete house cleaning service including all rooms',
            price: 45,
            duration: '2-3 hours',
            rating: 4.8,
            reviews: 124,
            image: 'https://via.placeholder.com/300x200',
            featured: true,
            category: 'regular',
          },
          {
            id: '2',
            title: 'Deep Cleaning Service',
            description: 'Thorough deep cleaning for your entire home',
            price: 75,
            duration: '4-6 hours',
            rating: 4.9,
            reviews: 89,
            image: 'https://via.placeholder.com/300x200',
            featured: false,
            category: 'deep',
          },
          {
            id: '3',
            title: 'Office Cleaning',
            description: 'Professional office and workspace cleaning',
            price: 60,
            duration: '2-4 hours',
            rating: 4.7,
            reviews: 156,
            image: 'https://via.placeholder.com/300x200',
            featured: false,
            category: 'office',
          },
        ];
        setServices(fallbackServices);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
      setError('Failed to load services. Please try again.');
      
      // Set fallback services even on error
      const fallbackServices: Service[] = [
        {
          id: '1',
          title: 'Regular House Cleaning',
          description: 'Complete house cleaning service including all rooms',
          price: 45,
          duration: '2-3 hours',
          rating: 4.8,
          reviews: 124,
          image: 'https://via.placeholder.com/300x200',
          featured: true,
          category: 'regular',
        },
      ];
      setServices(fallbackServices);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshServices = useCallback(() => {
    fetchServices(true);
  }, [fetchServices]);

  // Initial fetch
  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  return {
    services,
    isLoading,
    error,
    refreshServices,
    fetchServices,
  };
};
