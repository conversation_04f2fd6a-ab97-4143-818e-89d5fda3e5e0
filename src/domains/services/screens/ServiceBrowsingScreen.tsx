import { useState, useEffect } from "react"
import { View, ScrollView, TouchableOpacity, TextInput, RefreshControl } from "react-native"
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../../../context/ThemeContext"
import type { RootStackParamList } from "../../../navigation/RootNavigator"
import { ResponsiveText, Header } from "../../shared/components"
import { ServiceCard, ServiceListItem } from "../components"
import { serviceBrowsingStyles } from "../styles/ServiceBrowsingScreen.styles"
import { useServiceBrowsing } from "../hooks/useServiceBrowsing"
import { useServiceFilters } from "../hooks/useServiceFilters"

type ServiceBrowsingScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ServiceBrowsing'>
type ServiceBrowsingScreenRouteProp = RouteProp<RootStackParamList, 'ServiceBrowsing'>

const ServiceBrowsingScreen = () => {
  const navigation = useNavigation<ServiceBrowsingScreenNavigationProp>()
  const route = useRoute<ServiceBrowsingScreenRouteProp>()
  const theme = useTheme()
  const styles = serviceBrowsingStyles(theme)

  // Custom hooks for service browsing logic
  const {
    services,
    isLoading,
    error,
    refreshServices,
  } = useServiceBrowsing()

  const {
    searchQuery,
    setSearchQuery,
    activeCategory,
    setActiveCategory,
    categories,
    filteredServices,
  } = useServiceFilters(services)

  // Handle search query from route params
  useEffect(() => {
    if (route.params?.searchQuery) {
      const query = typeof route.params.searchQuery === 'string'
        ? route.params.searchQuery
        : ''
      setSearchQuery(query)
    }
  }, [route.params])

  const handleServicePress = (serviceId: string) => {
    navigation.navigate("ServiceDetail", { serviceId })
  }

  const handleBookService = (serviceId: string) => {
    navigation.navigate("Booking", { serviceId })
  }

  // Separate featured and regular services
  const featuredServices = filteredServices.filter(service => service.featured)
  const regularServices = filteredServices.filter(service => !service.featured)

  const renderCategoryFilter = () => (
    <View style={styles.categoriesContainer}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              activeCategory === category.id && styles.activeCategoryButton,
            ]}
            onPress={() => setActiveCategory(category.id)}
          >
            <ResponsiveText
              variant="body"
              style={[
                styles.categoryText,
                activeCategory === category.id && styles.activeCategoryText,
              ]}
            >
              {category.name}
            </ResponsiveText>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  )

  const renderSearchBar = () => (
    <View style={styles.searchContainer}>
      <View style={styles.searchInputContainer}>
        <Feather name="search" size={20} color={theme.colors.textSecondary} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search services..."
          placeholderTextColor={theme.colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery("")} style={styles.clearButton}>
            <Feather name="x" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  )

  const renderFeaturedServices = () => {
    if (featuredServices.length === 0) return null

    return (
      <View style={styles.section}>
        <ResponsiveText variant="h5" style={styles.sectionTitle}>
          Featured Services
        </ResponsiveText>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.featuredServicesContent}
        >
          {featuredServices.map((service) => (
            <View key={service.id} style={styles.featuredServiceCard}>
              <ServiceCard
                id={service.id}
                title={service.title}
                description={service.description}
                price={service.price}
                duration={service.duration}
                rating={service.rating}
                reviews={service.reviews}
                image={service.image}
                featured={true}
                onPress={() => handleServicePress(service.id)}
                onBookPress={() => handleBookService(service.id)}
              />
            </View>
          ))}
        </ScrollView>
      </View>
    )
  }

  const renderRegularServices = () => {
    if (regularServices.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Feather name="search" size={48} color={theme.colors.textSecondary} />
          <ResponsiveText variant="h6" style={styles.emptyStateTitle}>
            No services found
          </ResponsiveText>
          <ResponsiveText variant="body" style={styles.emptyStateText}>
            Try adjusting your search or category filter
          </ResponsiveText>
        </View>
      )
    }

    return (
      <View style={styles.section}>
        <ResponsiveText variant="h5" style={styles.sectionTitle}>
          All Services
        </ResponsiveText>
        <View style={styles.servicesList}>
          {regularServices.map((service) => (
            <ServiceListItem
              key={service.id}
              id={service.id}
              title={service.title}
              description={service.description}
              price={service.price}
              duration={service.duration}
              rating={service.rating}
              reviews={service.reviews}
              image={service.image}
              onPress={() => handleServicePress(service.id)}
              onBookPress={() => handleBookService(service.id)}
            />
          ))}
        </View>
      </View>
    )
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Header title="Browse Services" showBackButton />
        <View style={styles.errorContainer}>
          <Feather name="alert-circle" size={48} color={theme.colors.error} />
          <ResponsiveText variant="h6" style={styles.errorTitle}>
            Something went wrong
          </ResponsiveText>
          <ResponsiveText variant="body" style={styles.errorText}>
            {error}
          </ResponsiveText>
          <TouchableOpacity onPress={refreshServices} style={styles.retryButton}>
            <ResponsiveText variant="button" style={styles.retryButtonText}>
              Try Again
            </ResponsiveText>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <Header title="Browse Services" showBackButton />
      
      {renderSearchBar()}
      {renderCategoryFilter()}
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refreshServices}
            colors={[theme.colors.primary]}
          />
        }
      >
        {renderFeaturedServices()}
        {renderRegularServices()}
      </ScrollView>
    </View>
  )
}

export default ServiceBrowsingScreen
