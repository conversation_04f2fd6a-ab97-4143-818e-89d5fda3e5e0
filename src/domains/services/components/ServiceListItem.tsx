import type React from "react"
import { View, Text, Image, TouchableOpacity } from "react-native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../../../context/ThemeContext"
import { Card, Button, ResponsiveText } from "../../shared/components"
import { serviceListItemStyles } from "../styles/ServiceListItem.styles"

interface ServiceListItemProps {
  id: string
  title: string
  description: string
  price: number
  duration: string
  rating: number
  reviews: number
  image: string
  onPress: () => void
  onBookPress?: () => void
}

const ServiceListItem: React.FC<ServiceListItemProps> = ({
  title,
  description,
  price,
  duration,
  rating,
  reviews,
  image,
  onPress,
  onBookPress,
}) => {
  const theme = useTheme()
  const styles = serviceListItemStyles(theme)

  const handleBookPress = () => {
    if (onBookPress) {
      onBookPress()
    } else {
      onPress()
    }
  }

  return (
    <Card style={styles.container} variant="outlined" padding="none">
      <TouchableOpacity onPress={onPress} activeOpacity={0.9}>
        <View style={styles.content}>
          <Image source={{ uri: image }} style={styles.image} resizeMode="cover" />
          
          <View style={styles.details}>
            <ResponsiveText variant="body" style={styles.title} numberOfLines={1}>
              {title}
            </ResponsiveText>
            
            <ResponsiveText variant="bodySmall" style={styles.description} numberOfLines={2}>
              {description}
            </ResponsiveText>
            
            <View style={styles.ratingContainer}>
              <Feather name="star" size={12} style={styles.star} />
              <ResponsiveText variant="caption" style={styles.ratingText}>
                {rating}
              </ResponsiveText>
              <ResponsiveText variant="caption" style={styles.reviewsText}>
                ({reviews})
              </ResponsiveText>
              <View style={styles.separator} />
              <ResponsiveText variant="caption" style={styles.durationText}>
                Est. {duration}
              </ResponsiveText>
            </View>
            
            <View style={styles.footer}>
              <ResponsiveText variant="h6" style={styles.price}>
                D{price}/hr
              </ResponsiveText>
              <Button 
                title="Book" 
                onPress={handleBookPress} 
                variant="primary" 
                size="small"
              />
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </Card>
  )
}

export default ServiceListItem
