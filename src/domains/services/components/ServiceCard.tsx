import type React from "react"
import { View, Text, Image, TouchableOpacity } from "react-native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../../../context/ThemeContext"
import { Card, Button, ResponsiveText } from "../../shared/components"
import { serviceCardStyles } from "../styles/ServiceCard.styles"

interface ServiceCardProps {
  id: string
  title: string
  description: string
  price: number
  duration: string
  rating: number
  reviews: number
  image: string
  featured?: boolean
  onPress: () => void
  onBookPress?: () => void
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  title,
  description,
  price,
  duration,
  rating,
  reviews,
  image,
  featured = false,
  onPress,
  onBookPress,
}) => {
  const theme = useTheme()
  const styles = serviceCardStyles(theme, { featured })

  const handleBookPress = () => {
    if (onBookPress) {
      onBookPress()
    } else {
      onPress()
    }
  }

  return (
    <Card style={styles.container} variant="elevated" padding="none">
      <TouchableOpacity onPress={onPress} activeOpacity={0.9}>
        <View style={styles.imageContainer}>
          <Image source={{ uri: image }} style={styles.image} resizeMode="cover" />
          {featured && (
            <View style={styles.featuredBadge}>
              <ResponsiveText variant="caption" style={styles.featuredText}>
                Featured
              </ResponsiveText>
            </View>
          )}
        </View>
        
        <View style={styles.content}>
          <View style={styles.header}>
            <ResponsiveText variant="h6" style={styles.title} numberOfLines={1}>
              {title}
            </ResponsiveText>
            <ResponsiveText variant="h6" style={styles.price}>
              D{price}/hr
            </ResponsiveText>
          </View>
          
          <ResponsiveText variant="bodySmall" style={styles.description} numberOfLines={2}>
            {description}
          </ResponsiveText>
          
          <View style={styles.footer}>
            <View style={styles.ratingContainer}>
              <Feather name="star" size={16} style={styles.star} />
              <ResponsiveText variant="caption" style={styles.ratingText}>
                {rating}
              </ResponsiveText>
              <ResponsiveText variant="caption" style={styles.reviewsText}>
                ({reviews})
              </ResponsiveText>
            </View>
            <ResponsiveText variant="caption" style={styles.durationText}>
              Est. {duration}
            </ResponsiveText>
          </View>
          
          <Button 
            title="Book Now" 
            onPress={handleBookPress} 
            variant="warning" 
            fullWidth 
            size="medium"
          />
        </View>
      </TouchableOpacity>
    </Card>
  )
}

export default ServiceCard
