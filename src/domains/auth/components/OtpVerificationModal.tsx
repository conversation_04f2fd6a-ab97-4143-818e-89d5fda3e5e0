import React, { useState, useRef, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Animated,
  Vibration,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { Button, ResponsiveText } from '../../shared/components';
import { otpModalStyles } from '../styles/OtpVerificationModal.styles';
import { localAuthService } from '../../../services/localAuthService';

interface OtpVerificationModalProps {
  visible: boolean;
  onClose: () => void;
  email?: string;
  phone?: string;
  purpose: 'REGISTER' | 'LOGIN' | 'RESET';
  devOtp?: string;
  onSuccess: (response: any) => void;
}

const OtpVerificationModal: React.FC<OtpVerificationModalProps> = ({
  visible,
  onClose,
  email,
  phone,
  purpose,
  devOtp,
  onSuccess,
}) => {
  const theme = useTheme();
  const styles = otpModalStyles(theme);

  const [otp, setOtp] = useState(['', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [error, setError] = useState('');

  const inputRefs = useRef<Array<TextInput | null>>([null, null, null, null]);
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  // Auto-fill development OTP
  useEffect(() => {
    if (devOtp && devOtp.length === 4) {
      const otpArray = devOtp.split('');
      setOtp(otpArray);
    }
  }, [devOtp]);

  // Timer for OTP resend
  useEffect(() => {
    if (timeLeft > 0 && visible) {
      const timerId = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timerId);
    } else {
      setCanResend(true);
    }
  }, [timeLeft, visible]);

  // Reset state when modal opens
  useEffect(() => {
    if (visible) {
      setOtp(['', '', '', '']);
      setError('');
      setTimeLeft(60);
      setCanResend(false);
      setIsLoading(false);
    }
  }, [visible]);

  const handleOtpChange = (text: string, index: number) => {
    setError('');
    
    if (text.length > 1) {
      // Handle paste
      const pastedOtp = text.slice(0, 4).split('');
      const newOtp = [...otp];
      pastedOtp.forEach((digit, i) => {
        if (index + i < 4) {
          newOtp[index + i] = digit;
        }
      });
      setOtp(newOtp);
      
      // Focus the next empty input or the last input
      const nextIndex = Math.min(index + pastedOtp.length, 3);
      inputRefs.current[nextIndex]?.focus();
    } else {
      // Handle single character input
      const newOtp = [...otp];
      newOtp[index] = text;
      setOtp(newOtp);

      // Auto-focus next input
      if (text && index < 3) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const startShakeAnimation = () => {
    Vibration.vibrate(400);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();
  };

  const handleVerifyOtp = async () => {
    const otpCode = otp.join('');
    
    if (otpCode.length !== 4) {
      setError('Please enter a complete 4-digit code');
      startShakeAnimation();
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const identifier = email || phone || '';
      const response = await localAuthService.verifyOtp(identifier, otpCode);

      if (response.success) {
        onSuccess(response);
        onClose();
      } else {
        setError(response.error || 'Invalid verification code. Please try again.');
        startShakeAnimation();
        setOtp(['', '', '', '']);
        inputRefs.current[0]?.focus();
      }
    } catch (error: any) {
      console.error('OTP verification error:', error);
      setError('Verification failed. Please try again.');
      startShakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async () => {
    try {
      setIsLoading(true);
      const identifier = email || phone || '';
      const response = await localAuthService.sendOtp(identifier);

      if (response.success) {
        setTimeLeft(60);
        setCanResend(false);
        Alert.alert('Success', 'Verification code sent successfully');
      } else {
        Alert.alert('Error', response.error || 'Failed to send verification code');
      }
    } catch (error: any) {
      Alert.alert('Error', 'Failed to send verification code');
    } finally {
      setIsLoading(false);
    }
  };

  const getPurposeText = () => {
    switch (purpose) {
      case 'REGISTER':
        return 'We have sent a verification code to';
      case 'RESET':
        return 'Enter the code sent to reset your password';
      case 'LOGIN':
      default:
        return 'Enter the code sent to verify your account';
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View 
          style={[
            styles.container,
            { transform: [{ translateX: shakeAnimation }] }
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <ResponsiveText variant="h4" style={styles.title}>
              Verification Code
            </ResponsiveText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Feather name="x" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <ResponsiveText variant="body" style={styles.subtitle}>
              {getPurposeText()}
            </ResponsiveText>

            <ResponsiveText variant="bodySmall" style={styles.identifier}>
              {email || phone}
            </ResponsiveText>

            {/* OTP Input */}
            <View style={styles.otpContainer}>
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={ref => (inputRefs.current[index] = ref)}
                  style={[
                    styles.otpInput,
                    digit ? styles.otpInputFilled : {},
                    error ? styles.otpInputError : {},
                  ]}
                  value={digit}
                  onChangeText={text => handleOtpChange(text, index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  keyboardType="number-pad"
                  maxLength={1}
                  selectTextOnFocus
                />
              ))}
            </View>

            {/* Error Message */}
            {error ? (
              <ResponsiveText variant="caption" style={styles.errorText}>
                {error}
              </ResponsiveText>
            ) : null}

            {/* Verify Button */}
            <Button
              title={isLoading ? "Verifying..." : "Verify"}
              onPress={handleVerifyOtp}
              disabled={isLoading || otp.join('').length !== 4}
              loading={isLoading}
              style={styles.verifyButton}
              fullWidth
            />

            {/* Resend Section */}
            <View style={styles.resendContainer}>
              {canResend ? (
                <TouchableOpacity onPress={handleResendOtp} disabled={isLoading}>
                  <ResponsiveText variant="body" style={styles.resendText}>
                    Resend Code
                  </ResponsiveText>
                </TouchableOpacity>
              ) : (
                <ResponsiveText variant="body" style={styles.timerText}>
                  Resend code in {timeLeft}s
                </ResponsiveText>
              )}
            </View>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default OtpVerificationModal;
