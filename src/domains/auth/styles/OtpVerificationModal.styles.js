import { StyleSheet } from 'react-native';
import { responsiveSpacing, responsiveDimensions, wp, hp } from '../../../utils/responsive';

export const otpModalStyles = (theme) => {
  return StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: theme.colors.overlay,
      justifyContent: 'center',
      alignItems: 'center',
      padding: responsiveSpacing.md,
    },
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.components.modal.borderRadius,
      width: '100%',
      maxWidth: wp(90),
      maxHeight: hp(80),
      ...theme.components.modal.shadow,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: responsiveSpacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    title: {
      flex: 1,
    },
    closeButton: {
      width: responsiveDimensions.buttonHeight.small,
      height: responsiveDimensions.buttonHeight.small,
      borderRadius: responsiveDimensions.buttonHeight.small / 2,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.backgroundLight,
    },
    content: {
      padding: responsiveSpacing.lg,
    },
    subtitle: {
      textAlign: 'center',
      color: theme.colors.textSecondary,
      marginBottom: responsiveSpacing.sm,
    },
    identifier: {
      textAlign: 'center',
      color: theme.colors.text,
      fontWeight: theme.typography.button.fontWeight,
      marginBottom: responsiveSpacing.xl,
    },
    otpContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: responsiveSpacing.lg,
      paddingHorizontal: responsiveSpacing.md,
    },
    otpInput: {
      width: responsiveDimensions.inputHeight,
      height: responsiveDimensions.inputHeight,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderRadius: theme.components.input.borderRadius,
      textAlign: 'center',
      fontSize: theme.typography.h4.fontSize,
      fontWeight: theme.typography.button.fontWeight,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
    },
    otpInputFilled: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.card,
    },
    otpInputError: {
      borderColor: theme.colors.error,
    },
    errorText: {
      textAlign: 'center',
      color: theme.colors.error,
      marginBottom: responsiveSpacing.md,
    },
    verifyButton: {
      marginBottom: responsiveSpacing.lg,
    },
    resendContainer: {
      alignItems: 'center',
    },
    resendText: {
      color: theme.colors.primary,
      fontWeight: theme.typography.button.fontWeight,
    },
    timerText: {
      color: theme.colors.textSecondary,
    },
  });
};
