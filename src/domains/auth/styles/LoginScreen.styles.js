import { StyleSheet } from 'react-native';
import { responsiveSpacing, responsiveDimensions, wp, hp } from '../../../utils/responsive';

export const loginScreenStyles = (theme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      paddingHorizontal: responsiveSpacing.md,
      paddingVertical: responsiveSpacing.sm,
      flexDirection: 'row',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      width: responsiveDimensions.buttonHeight.small,
      height: responsiveDimensions.buttonHeight.small,
      borderRadius: responsiveDimensions.buttonHeight.small / 2,
      backgroundColor: theme.colors.card,
      alignItems: 'center',
      justifyContent: 'center',
      ...theme.shadows.sm,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
    },
    content: {
      flex: 1,
      padding: responsiveSpacing.md,
    },
    centeredContent: {
      alignItems: "center",
      marginTop: responsiveSpacing.xl,
      marginBottom: responsiveSpacing.xl,
    },
    formContainer: {
      width: '100%',
      maxWidth: wp(90),
      alignSelf: 'center',
    },
    logo: {
      height: responsiveDimensions.avatarSize.xl,
      width: responsiveDimensions.avatarSize.xl,
      marginBottom: responsiveSpacing.lg,
      borderRadius: responsiveDimensions.avatarSize.xl / 2,
    },
    title: {
      textAlign: "center",
      marginBottom: responsiveSpacing.sm,
    },
    subtitle: {
      textAlign: "center",
      color: theme.colors.textSecondary,
      marginBottom: responsiveSpacing.lg,
    },
    inputContainer: {
      marginBottom: responsiveSpacing.md,
    },
    label: {
      fontSize: theme.typography.label.fontSize,
      fontWeight: theme.typography.label.fontWeight,
      color: theme.colors.text,
      marginBottom: responsiveSpacing.xs,
    },
    inputWrapper: {
      position: 'relative',
    },
    input: {
      height: responsiveDimensions.inputHeight,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.components.input.borderRadius,
      paddingHorizontal: responsiveSpacing.md,
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.text,
      backgroundColor: theme.colors.card,
    },
    inputFocused: {
      borderColor: theme.colors.primary,
      ...theme.shadows.sm,
    },
    inputError: {
      borderColor: theme.colors.error,
    },
    passwordToggle: {
      position: 'absolute',
      right: responsiveSpacing.md,
      top: '50%',
      transform: [{ translateY: -12 }],
    },
    errorText: {
      fontSize: theme.typography.caption.fontSize,
      color: theme.colors.error,
      marginTop: responsiveSpacing.xs,
    },
    rememberMeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: responsiveSpacing.lg,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderRadius: 4,
      marginRight: responsiveSpacing.sm,
      alignItems: 'center',
      justifyContent: 'center',
    },
    checkboxChecked: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    rememberMeText: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.text,
    },
    loginButton: {
      marginBottom: responsiveSpacing.md,
    },
    forgotPasswordContainer: {
      alignItems: 'center',
      marginBottom: responsiveSpacing.lg,
    },
    forgotPasswordText: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.primary,
    },
    dividerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: responsiveSpacing.lg,
    },
    dividerLine: {
      flex: 1,
      height: 1,
      backgroundColor: theme.colors.border,
    },
    dividerText: {
      marginHorizontal: responsiveSpacing.md,
      fontSize: theme.typography.caption.fontSize,
      color: theme.colors.textSecondary,
    },
    socialButtonsContainer: {
      marginBottom: responsiveSpacing.lg,
    },
    socialButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      height: responsiveDimensions.buttonHeight.medium,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.components.button.borderRadius,
      marginBottom: responsiveSpacing.sm,
      backgroundColor: theme.colors.card,
    },
    socialButtonText: {
      marginLeft: responsiveSpacing.sm,
      fontSize: theme.typography.button.fontSize,
      color: theme.colors.text,
    },
    signUpContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: responsiveSpacing.lg,
    },
    signUpText: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.textSecondary,
    },
    signUpLink: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.primary,
      fontWeight: theme.typography.button.fontWeight,
      marginLeft: responsiveSpacing.xs,
    },
    switchModeContainer: {
      alignItems: 'center',
      marginTop: responsiveSpacing.md,
      paddingTop: responsiveSpacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    switchModeText: {
      fontSize: theme.typography.caption.fontSize,
      color: theme.colors.textSecondary,
      marginBottom: responsiveSpacing.sm,
    },
    switchModeButton: {
      paddingVertical: responsiveSpacing.sm,
      paddingHorizontal: responsiveSpacing.md,
    },
    switchModeButtonText: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.primary,
      fontWeight: theme.typography.button.fontWeight,
    },
  });
};
