import { useState, useRef } from 'react';
import { Animated, Vibration } from 'react-native';

interface ValidationErrors {
  email: string;
  password: string;
  phone: string;
  login: string;
}

interface LoginFormData {
  email: string;
  password: string;
  phone: string;
  rememberMe: boolean;
  showPassword: boolean;
}

export const useLoginValidation = () => {
  const [errors, setErrors] = useState<ValidationErrors>({
    email: '',
    password: '',
    phone: '',
    login: '',
  });

  const [formTouched, setFormTouched] = useState(false);
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setErrors(prev => ({ ...prev, email: "Email is required" }));
      return false;
    } else if (!emailRegex.test(email)) {
      setErrors(prev => ({ ...prev, email: "Please enter a valid email address" }));
      return false;
    } else {
      setErrors(prev => ({ ...prev, email: "" }));
      return true;
    }
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10}$/;
    if (!phone) {
      setErrors(prev => ({ ...prev, phone: "Phone number is required" }));
      return false;
    } else if (!phoneRegex.test(phone.replace(/\D/g, ''))) {
      setErrors(prev => ({ ...prev, phone: "Please enter a valid 10-digit phone number" }));
      return false;
    } else {
      setErrors(prev => ({ ...prev, phone: "" }));
      return true;
    }
  };

  const validatePassword = (password: string): boolean => {
    if (!password) {
      setErrors(prev => ({ ...prev, password: "Password is required" }));
      return false;
    } else if (password.length < 6) {
      setErrors(prev => ({ ...prev, password: "Password must be at least 6 characters" }));
      return false;
    } else {
      setErrors(prev => ({ ...prev, password: "" }));
      return true;
    }
  };

  const validateForm = (formData: LoginFormData, userRole: string | null): boolean => {
    setFormTouched(true);
    let isValid = true;

    if (userRole === 'provider') {
      if (!validateEmail(formData.email)) isValid = false;
      if (!validatePassword(formData.password)) isValid = false;
    } else {
      if (!validatePhone(formData.phone)) isValid = false;
    }

    return isValid;
  };

  const clearErrors = () => {
    setErrors({
      email: '',
      password: '',
      phone: '',
      login: '',
    });
  };

  const setLoginError = (error: string) => {
    setErrors(prev => ({ ...prev, login: error }));
  };

  const startShakeAnimation = () => {
    Vibration.vibrate(400);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();
  };

  return {
    errors,
    formTouched,
    validateEmail,
    validatePhone,
    validatePassword,
    validateForm,
    clearErrors,
    setLoginError,
    startShakeAnimation,
    shakeAnimation,
  };
};
