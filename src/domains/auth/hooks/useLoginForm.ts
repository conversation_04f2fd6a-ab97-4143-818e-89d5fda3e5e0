import { useState } from 'react';

interface LoginFormData {
  email: string;
  password: string;
  phone: string;
  rememberMe: boolean;
  showPassword: boolean;
}

interface OtpData {
  email: string;
  phone: string;
  purpose: 'REGISTER' | 'LOGIN' | 'RESET';
  devOtp?: string;
}

export const useLoginForm = () => {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    phone: '',
    rememberMe: false,
    showPassword: false,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [otpData, setOtpData] = useState<OtpData>({
    email: '',
    phone: '',
    purpose: 'LOGIN',
  });

  const updateFormData = (updates: Partial<LoginFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const resetForm = () => {
    setFormData({
      email: '',
      password: '',
      phone: '',
      rememberMe: false,
      showPassword: false,
    });
  };

  const togglePasswordVisibility = () => {
    updateFormData({ showPassword: !formData.showPassword });
  };

  const toggleRememberMe = () => {
    updateFormData({ rememberMe: !formData.rememberMe });
  };

  return {
    formData,
    updateFormData,
    resetForm,
    togglePasswordVisibility,
    toggleRememberMe,
    isLoading,
    setIsLoading,
    userRole,
    setUserRole,
    showOtpModal,
    setShowOtpModal,
    otpData,
    setOtpData,
  };
};
