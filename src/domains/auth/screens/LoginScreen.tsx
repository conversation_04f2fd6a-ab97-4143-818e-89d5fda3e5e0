import { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Animated,
  Vibration,
} from "react-native";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Feather } from "@expo/vector-icons";
import { useTheme } from "../../../context/ThemeContext";
import { useAuth } from "../../../context/AuthContext";
import { useBooking } from "../../../context/BookingContext";
import type { RootStackParamList } from "../../../navigation/RootNavigator";
import { Button, ResponsiveText } from "../../shared/components";
import { OtpVerificationModal } from "../components";
import { ToastService } from "../../shared/components";
import Storage from "../../../utils/storage";
import { localAuthService } from "../../../services/localAuthService";
import * as SecureStore from 'expo-secure-store';
import { loginScreenStyles } from "../styles/LoginScreen.styles";
import { useLoginForm } from "../hooks/useLoginForm";
import { useLoginValidation } from "../hooks/useLoginValidation";

type LoginScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;
type LoginScreenRouteProp = RouteProp<RootStackParamList, 'Login'>;

const LoginScreen = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const route = useRoute<LoginScreenRouteProp>();
  const theme = useTheme();
  const { setUser, setIsAuthenticated } = useAuth();
  const { restoreBookingData } = useBooking();

  // Custom hooks for form management
  const {
    formData,
    updateFormData,
    resetForm,
    isLoading,
    setIsLoading,
    userRole,
    setUserRole,
    showOtpModal,
    setShowOtpModal,
    otpData,
    setOtpData,
  } = useLoginForm();

  const {
    errors,
    validateForm,
    clearErrors,
    startShakeAnimation,
    shakeAnimation,
  } = useLoginValidation();

  // Refs
  const passwordInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const phoneInputRef = useRef<TextInput>(null);

  const styles = loginScreenStyles(theme);

  // Load user role and pre-fill credentials
  useEffect(() => {
    const loadUserRole = async () => {
      try {
        const role = await Storage.getItem("userRole");
        setUserRole(role);

        // Pre-fill provider credentials if needed
        if (role === 'provider') {
          updateFormData({
            email: '<EMAIL>',
            password: 'M12345a@'
          });
        }

        // Load remembered credentials
        const rememberedEmail = await SecureStore.getItemAsync('cached_email');
        const rememberMeEnabled = await SecureStore.getItemAsync('remember_me');

        if (rememberedEmail && rememberMeEnabled === 'true') {
          updateFormData({ 
            email: rememberedEmail, 
            rememberMe: true 
          });
        }
      } catch (error) {
        console.error("Error loading user role:", error);
      }
    };

    loadUserRole();
  }, []);

  const handleLogin = async () => {
    clearErrors();

    try {
      setIsLoading(true);

      // Validate form
      const isValid = validateForm(formData, userRole);
      if (!isValid) {
        startShakeAnimation();
        setIsLoading(false);
        return;
      }

      // Prepare login data
      const loginData: any = {
        role: userRole === 'provider' ? 'PROVIDER' : 'CUSTOMER'
      };

      if (userRole === 'provider') {
        loginData.email = formData.email.trim();
        loginData.password = formData.password;
      } else {
        loginData.phone = formData.phone.trim();
      }

      Keyboard.dismiss();

      // Call login service
      const response = await localAuthService.login(loginData);

      if (!response.success) {
        const errorMessage = response.error || "Please check your credentials and try again.";
        // Set login error through validation hook
        startShakeAnimation();
        setIsLoading(false);
        return;
      }

      // Handle OTP requirement
      if (response.isOtpRequired) {
        setOtpData({
          email: userRole === 'provider' ? formData.email : '',
          phone: userRole === 'customer' ? formData.phone : '',
          purpose: 'LOGIN',
          devOtp: response.otp
        });
        setShowOtpModal(true);
        setIsLoading(false);
        return;
      }

      // Handle successful login
      if (response.user) {
        await handleSuccessfulLogin(response);
      }

    } catch (error: any) {
      console.error("Login error:", error);
      startShakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccessfulLogin = async (response: any) => {
    // Store remember me preference
    if (formData.rememberMe) {
      if (userRole === 'provider') {
        await SecureStore.setItemAsync('cached_email', formData.email.trim());
      } else {
        await SecureStore.setItemAsync('cached_phone', formData.phone.trim());
      }
      await SecureStore.setItemAsync('remember_me', 'true');
    } else {
      await SecureStore.deleteItemAsync('remember_me');
      await SecureStore.deleteItemAsync('cached_email');
      await SecureStore.deleteItemAsync('cached_phone');
    }

    // Set authentication state
    setUser(response.user);
    setIsAuthenticated(true);

    // Store tokens and user data
    if (response.tokens) {
      await SecureStore.setItemAsync('accessToken', response.tokens.accessToken);
      await SecureStore.setItemAsync('refreshToken', response.tokens.refreshToken);
    }

    await SecureStore.setItemAsync('user_data', JSON.stringify(response.user));
    await SecureStore.setItemAsync('isAuthenticated', 'true');

    if (response.user.role) {
      await SecureStore.setItemAsync('userRoleEnum', response.user.role);
      await SecureStore.setItemAsync('userRole',
        response.user.role === 'PROVIDER' ? 'provider' : 'customer');
    }

    // Handle navigation
    if (route.params?.returnTo) {
      await handleReturnNavigation();
    } else {
      await handleDefaultNavigation(response.user.role);
    }
  };

  const handleReturnNavigation = async () => {
    ToastService.show({
      type: 'success',
      text1: 'Login Successful!',
      text2: 'Returning to your booking...',
      visibilityTime: 2000,
      autoHide: true,
    });

    if (route.params?.fromBooking) {
      try {
        await restoreBookingData();
      } catch (restoreError) {
        console.error('Error restoring booking data:', restoreError);
      }
    }

    setTimeout(() => {
      if (route.params?.returnTo === 'PaymentOption') {
        navigation.navigate(route.params.returnTo as any, route.params?.returnParams || {});
      } else {
        navigation.navigate(route.params?.returnTo as any);
      }
    }, 500);
  };

  const handleDefaultNavigation = async (userRole: string) => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Main' as any }],
    });
  };

  const handleSocialLogin = async (provider: string) => {
    Alert.alert(
      "Social Login",
      `${provider} login is not implemented yet.`,
      [{ text: "OK" }]
    );
  };

  const handleSignUp = () => {
    navigation.navigate("RoleSelection");
  };

  const handleForgotPassword = () => {
    navigation.navigate("ForgotPassword");
  };

  const switchToCleanerLogin = async () => {
    try {
      await Storage.setItem("userRole", "provider");
      setUserRole("provider");
      updateFormData({
        email: '<EMAIL>',
        password: 'M12345a@'
      });
    } catch (error) {
      console.error("Error switching to cleaner login:", error);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <Animated.View
            style={[
              styles.content,
              { transform: [{ translateX: shakeAnimation }] }
            ]}
          >
            {/* Logo and Title */}
            <View style={styles.centeredContent}>
              <ResponsiveText variant="h2" style={styles.title}>
                Welcome Back
              </ResponsiveText>
              <ResponsiveText variant="bodySmall" style={styles.subtitle}>
                {userRole === 'provider' 
                  ? 'Sign in to your provider account'
                  : 'Sign in with your phone number'
                }
              </ResponsiveText>
            </View>

            {/* Form Container */}
            <View style={styles.formContainer}>
              {/* Continue with form fields... */}
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* OTP Modal */}
      {showOtpModal && (
        <OtpVerificationModal
          visible={showOtpModal}
          onClose={() => setShowOtpModal(false)}
          email={otpData.email}
          phone={otpData.phone}
          purpose={otpData.purpose}
          devOtp={otpData.devOtp}
          onSuccess={handleSuccessfulLogin}
        />
      )}
    </SafeAreaView>
  );
};

export default LoginScreen;
