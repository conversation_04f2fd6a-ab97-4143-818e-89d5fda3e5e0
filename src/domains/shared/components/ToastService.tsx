import React, { useState, useEffect } from 'react';
import { View, Text, Animated, Dimensions } from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { toastStyles } from '../styles/ToastService.styles';

interface ToastConfig {
  type: 'success' | 'error' | 'info' | 'warning';
  text1: string;
  text2?: string;
  visibilityTime?: number;
  autoHide?: boolean;
}

interface ToastState extends ToastConfig {
  visible: boolean;
  id: string;
}

class ToastManager {
  private static instance: ToastManager;
  private listeners: ((toast: ToastState | null) => void)[] = [];
  private currentToast: ToastState | null = null;

  static getInstance(): ToastManager {
    if (!ToastManager.instance) {
      ToastManager.instance = new ToastManager();
    }
    return ToastManager.instance;
  }

  show(config: ToastConfig) {
    const toast: ToastState = {
      ...config,
      visible: true,
      id: Date.now().toString(),
      visibilityTime: config.visibilityTime || 3000,
      autoHide: config.autoHide !== false,
    };

    this.currentToast = toast;
    this.notifyListeners(toast);

    if (toast.autoHide) {
      setTimeout(() => {
        this.hide();
      }, toast.visibilityTime);
    }
  }

  hide() {
    if (this.currentToast) {
      this.currentToast = { ...this.currentToast, visible: false };
      this.notifyListeners(this.currentToast);
      
      setTimeout(() => {
        this.currentToast = null;
        this.notifyListeners(null);
      }, 300); // Animation duration
    }
  }

  subscribe(listener: (toast: ToastState | null) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(toast: ToastState | null) {
    this.listeners.forEach(listener => listener(toast));
  }
}

export const ToastService = {
  show: (config: ToastConfig) => ToastManager.getInstance().show(config),
  hide: () => ToastManager.getInstance().hide(),
};

const Toast: React.FC = () => {
  const theme = useTheme();
  const styles = toastStyles(theme);
  const [toast, setToast] = useState<ToastState | null>(null);
  const slideAnim = new Animated.Value(-100);

  useEffect(() => {
    const unsubscribe = ToastManager.getInstance().subscribe(setToast);
    return unsubscribe;
  }, []);

  useEffect(() => {
    if (toast?.visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else if (toast && !toast.visible) {
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [toast]);

  if (!toast) return null;

  const getToastStyle = () => {
    switch (toast.type) {
      case 'success':
        return styles.successToast;
      case 'error':
        return styles.errorToast;
      case 'warning':
        return styles.warningToast;
      case 'info':
      default:
        return styles.infoToast;
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        getToastStyle(),
        { transform: [{ translateY: slideAnim }] }
      ]}
    >
      <View style={styles.content}>
        <Text style={styles.text1}>{toast.text1}</Text>
        {toast.text2 ? (
          <Text style={styles.text2}>{toast.text2}</Text>
        ) : null}
      </View>
    </Animated.View>
  );
};

export default Toast;
