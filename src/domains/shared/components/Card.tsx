import type React from "react"
import type { ReactNode } from "react"
import { View, type StyleProp, type ViewStyle } from "react-native"
import { useTheme } from "../../../context/ThemeContext"
import { cardStyles } from "../styles/Card.styles"

interface CardProps {
  children: ReactNode
  style?: StyleProp<ViewStyle>
  variant?: "default" | "elevated" | "outlined"
  padding?: "none" | "small" | "medium" | "large"
}

const Card: React.FC<CardProps> = ({ 
  children, 
  style, 
  variant = "default",
  padding = "medium"
}) => {
  const theme = useTheme()
  const styles = cardStyles(theme, { variant, padding })

  return <View style={[styles.card, style]}>{children}</View>
}

export default Card
