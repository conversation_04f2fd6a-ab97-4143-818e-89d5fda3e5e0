import type React from "react";
import { View, TouchableOpacity } from "react-native";
import { Feather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { useTheme } from "../../../context/ThemeContext";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { ResponsiveText } from "./ResponsiveText";
import { headerStyles } from "../styles/Header.styles";

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  rightIcon?: React.ReactNode;
  rightComponent?: React.ReactNode;
  backgroundColor?: string;
  textColor?: string;
}

const Header: React.FC<HeaderProps> = ({
  title,
  showBackButton = false,
  rightIcon,
  rightComponent,
  backgroundColor,
  textColor,
}) => {
  const navigation = useNavigation();
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const styles = headerStyles(theme, { backgroundColor, textColor });

  return (
    <View style={[styles.wrapper, { backgroundColor: backgroundColor || theme.colors.primary }]}>
      {/* Status bar spacing */}
      <View style={[styles.statusBarSpacer, { height: insets.top }]} />
      
      <View style={styles.container}>
        <View style={styles.leftContainer}>
          {showBackButton && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
              accessibilityLabel="Go back"
              accessibilityRole="button"
            >
              <Feather 
                name="arrow-left" 
                size={24} 
                color={textColor || theme.colors.textInverse} 
              />
            </TouchableOpacity>
          )}
          <ResponsiveText 
            variant="h6" 
            style={styles.title}
            numberOfLines={1}
          >
            {title}
          </ResponsiveText>
        </View>
        
        <View style={styles.rightContainer}>
          {rightComponent || rightIcon}
        </View>
      </View>
    </View>
  );
};

export default Header;
