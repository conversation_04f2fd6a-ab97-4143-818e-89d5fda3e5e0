import React from "react"
import { View, Text } from "react-native"
import { useTheme } from "../../../context/ThemeContext"
import { ResponsiveText } from "./ResponsiveText"
import { badgeStyles } from "../styles/Badge.styles"

interface BadgeProps {
  children: React.ReactNode
  variant?: "default" | "primary" | "success" | "warning" | "danger" | "info"
  size?: "small" | "medium" | "large"
}

const Badge: React.FC<BadgeProps> = ({ 
  children, 
  variant = "default",
  size = "medium"
}) => {
  const theme = useTheme()
  const styles = badgeStyles(theme, { variant, size })

  return (
    <View style={styles.badge}>
      <ResponsiveText variant="caption" style={styles.text}>
        {children}
      </ResponsiveText>
    </View>
  )
}

export default Badge
