// Shared Components - Main Export
// Centralized export for all shared/common components

export { default as But<PERSON> } from './Button'
export { default as Card } from './Card'
export { default as ResponsiveText } from './ResponsiveText'
export { default as Header } from './Header'
export { default as Badge } from './Badge'
export { default as Toast, ToastService } from './ToastService'

// Re-export types if needed
export type { default as ButtonProps } from './Button'
export type { default as CardProps } from './Card'
export type { default as ResponsiveTextProps } from './ResponsiveText'
export type { default as HeaderProps } from './Header'
export type { default as BadgeProps } from './Badge'
