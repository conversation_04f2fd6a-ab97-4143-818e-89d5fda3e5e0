import type React from "react"
import { Text, type TextStyle, type StyleProp } from "react-native"
import { useTheme } from "../../../context/ThemeContext"
import { responsiveTextStyles } from "../styles/ResponsiveText.styles"

interface ResponsiveTextProps {
  children: React.ReactNode
  style?: StyleProp<TextStyle>
  variant?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "body" | "bodyLarge" | "bodySmall" | "caption" | "label" | "button"
  color?: string
  numberOfLines?: number
  accessibilityLabel?: string
  accessibilityHint?: string
}

const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  style,
  variant = "body",
  color,
  numberOfLines,
  accessibilityLabel,
  accessibilityHint,
  ...props
}) => {
  const theme = useTheme()
  const styles = responsiveTextStyles(theme, { variant, color })

  return (
    <Text 
      style={[styles.text, style]} 
      numberOfLines={numberOfLines}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      {...props}
    >
      {children}
    </Text>
  )
}

export default ResponsiveText
