import { StyleSheet } from 'react-native'
import { responsiveFontSizes } from '../../../utils/responsive'

export const responsiveTextStyles = (theme, { variant, color }) => {
  const getTypographyStyle = () => {
    switch (variant) {
      case "h1":
        return {
          ...theme.typography.h1,
          fontSize: responsiveFontSizes.hero,
        }
      case "h2":
        return {
          ...theme.typography.h2,
          fontSize: responsiveFontSizes.display,
        }
      case "h3":
        return {
          ...theme.typography.h3,
          fontSize: responsiveFontSizes.xxxl,
        }
      case "h4":
        return {
          ...theme.typography.h4,
          fontSize: responsiveFontSizes.xxl,
        }
      case "h5":
        return {
          ...theme.typography.h5,
          fontSize: responsiveFontSizes.xl,
        }
      case "h6":
        return {
          ...theme.typography.h6,
          fontSize: responsiveFontSizes.lg,
        }
      case "bodyLarge":
        return {
          ...theme.typography.bodyLarge,
          fontSize: responsiveFontSizes.lg,
        }
      case "bodySmall":
        return {
          ...theme.typography.bodySmall,
          fontSize: responsiveFontSizes.sm,
        }
      case "caption":
        return {
          ...theme.typography.caption,
          fontSize: responsiveFontSizes.xs,
        }
      case "label":
        return {
          ...theme.typography.label,
          fontSize: responsiveFontSizes.sm,
        }
      case "button":
        return {
          ...theme.typography.button,
          fontSize: responsiveFontSizes.md,
        }
      default: // body
        return {
          ...theme.typography.body,
          fontSize: responsiveFontSizes.md,
        }
    }
  }

  const getTextColor = () => {
    if (color) return color
    
    // Default colors based on variant
    switch (variant) {
      case "h1":
      case "h2":
      case "h3":
      case "h4":
      case "h5":
      case "h6":
        return theme.colors.text
      case "caption":
        return theme.colors.textSecondary
      case "label":
        return theme.colors.text
      default:
        return theme.colors.text
    }
  }

  return StyleSheet.create({
    text: {
      ...getTypographyStyle(),
      color: getTextColor(),
    },
  })
}
