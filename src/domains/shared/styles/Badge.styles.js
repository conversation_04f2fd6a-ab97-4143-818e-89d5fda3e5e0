import { StyleSheet } from 'react-native'
import { responsiveSpacing } from '../../../utils/responsive'
import { getColorWithOpacity } from '../../../constants/colors'

export const badgeStyles = (theme, { variant, size }) => {
  const getBackgroundColor = () => {
    switch (variant) {
      case "primary":
        return getColorWithOpacity(theme.colors.primary, 0.15)
      case "success":
        return getColorWithOpacity(theme.colors.success, 0.15)
      case "warning":
        return getColorWithOpacity(theme.colors.warning, 0.15)
      case "danger":
        return getColorWithOpacity(theme.colors.error, 0.15)
      case "info":
        return getColorWithOpacity(theme.colors.info, 0.15)
      default:
        return getColorWithOpacity(theme.colors.textSecondary, 0.15)
    }
  }

  const getTextColor = () => {
    switch (variant) {
      case "primary":
        return theme.colors.primary
      case "success":
        return theme.colors.success
      case "warning":
        return theme.colors.warning
      case "danger":
        return theme.colors.error
      case "info":
        return theme.colors.info
      default:
        return theme.colors.textSecondary
    }
  }

  const getPadding = () => {
    switch (size) {
      case "small":
        return {
          paddingHorizontal: responsiveSpacing.xs,
          paddingVertical: responsiveSpacing.xxs,
        }
      case "large":
        return {
          paddingHorizontal: responsiveSpacing.md,
          paddingVertical: responsiveSpacing.sm,
        }
      default: // medium
        return {
          paddingHorizontal: responsiveSpacing.sm,
          paddingVertical: responsiveSpacing.xs,
        }
    }
  }

  return StyleSheet.create({
    badge: {
      backgroundColor: getBackgroundColor(),
      borderRadius: theme.components.badge.borderRadius,
      alignSelf: "flex-start",
      ...getPadding(),
    },
    text: {
      color: getTextColor(),
      fontWeight: theme.components.badge.typography.fontWeight,
      textAlign: 'center',
    },
  })
}
