import { StyleSheet } from 'react-native'
import { responsiveSpacing, responsiveDimensions } from '../../../utils/responsive'

export const headerStyles = (theme, { backgroundColor, textColor }) => {
  return StyleSheet.create({
    wrapper: {
      backgroundColor: backgroundColor || theme.colors.primary,
    },
    statusBarSpacer: {
      backgroundColor: backgroundColor || theme.colors.primary,
    },
    container: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: responsiveSpacing.md,
      paddingVertical: responsiveSpacing.md,
      minHeight: responsiveDimensions.headerHeight,
      backgroundColor: backgroundColor || theme.colors.primary,
      ...theme.components.header.shadow,
    },
    leftContainer: {
      alignItems: "center",
      flexDirection: "row",
      flex: 1,
    },
    rightContainer: {
      alignItems: "center",
      flexDirection: "row",
    },
    backButton: {
      marginRight: responsiveSpacing.sm,
      width: responsiveDimensions.buttonHeight.small,
      height: responsiveDimensions.buttonHeight.small,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: responsiveDimensions.buttonHeight.small / 2,
    },
    title: {
      color: textColor || theme.colors.textInverse,
      fontWeight: theme.components.header.typography.fontWeight,
      flex: 1,
    },
  })
}
