import { StyleSheet } from 'react-native'
import { responsiveSpacing } from '../../../utils/responsive'

export const cardStyles = (theme, { variant, padding }) => {
  const getCardStyle = () => {
    const baseStyle = {
      backgroundColor: theme.colors.card,
      borderRadius: theme.components.card.borderRadius,
      overflow: "hidden",
    }

    switch (variant) {
      case "elevated":
        return {
          ...baseStyle,
          ...theme.components.card.shadow,
        }
      case "outlined":
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: theme.colors.border,
        }
      default:
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: theme.colors.border,
        }
    }
  }

  const getPadding = () => {
    switch (padding) {
      case "none":
        return {}
      case "small":
        return { padding: responsiveSpacing.sm }
      case "medium":
        return { padding: responsiveSpacing.md }
      case "large":
        return { padding: responsiveSpacing.lg }
      default:
        return { padding: responsiveSpacing.md }
    }
  }

  return StyleSheet.create({
    card: {
      ...getCardStyle(),
      ...getPadding(),
    },
  })
}
