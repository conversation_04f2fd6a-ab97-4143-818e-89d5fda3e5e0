import { StyleSheet, Dimensions } from 'react-native';
import { responsiveSpacing, wp } from '../../../utils/responsive';

const { width } = Dimensions.get('window');

export const toastStyles = (theme) => {
  return StyleSheet.create({
    container: {
      position: 'absolute',
      top: 50,
      left: responsiveSpacing.md,
      right: responsiveSpacing.md,
      zIndex: theme.zIndex.toast,
      borderRadius: theme.components.toast.borderRadius,
      padding: theme.components.toast.padding.paddingVertical,
      paddingHorizontal: theme.components.toast.padding.paddingHorizontal,
      ...theme.components.toast.shadow,
    },
    content: {
      flex: 1,
    },
    text1: {
      fontSize: theme.typography.body.fontSize,
      fontWeight: theme.typography.button.fontWeight,
      color: theme.colors.textInverse,
      marginBottom: 2,
    },
    text2: {
      fontSize: theme.typography.bodySmall.fontSize,
      color: theme.colors.textInverse,
      opacity: 0.9,
    },
    successToast: {
      backgroundColor: theme.colors.success,
    },
    errorToast: {
      backgroundColor: theme.colors.error,
    },
    warningToast: {
      backgroundColor: theme.colors.warning,
    },
    infoToast: {
      backgroundColor: theme.colors.info,
    },
  });
};
