import { StyleSheet } from 'react-native'
import { responsiveDimensions, responsiveSpacing } from '../../../utils/responsive'

export const buttonStyles = (theme, { variant, size, disabled, fullWidth }) => {
  const getBackgroundColor = () => {
    if (disabled) return theme.colors.border

    switch (variant) {
      case "primary":
        return theme.colors.primary
      case "secondary":
        return theme.colors.secondary
      case "warning":
        return theme.colors.warning
      case "outline":
        return "transparent"
      default:
        return theme.colors.primary
    }
  }

  const getTextColor = () => {
    if (disabled) return theme.colors.textLight

    switch (variant) {
      case "primary":
      case "secondary":
        return theme.colors.textInverse
      case "warning":
        return theme.colors.text
      case "outline":
        return theme.colors.primary
      default:
        return theme.colors.textInverse
    }
  }

  const getPadding = () => {
    switch (size) {
      case "small":
        return { 
          paddingVertical: responsiveSpacing.sm, 
          paddingHorizontal: responsiveSpacing.md 
        }
      case "medium":
        return { 
          paddingVertical: responsiveSpacing.md, 
          paddingHorizontal: responsiveSpacing.lg 
        }
      case "large":
        return { 
          paddingVertical: responsiveSpacing.lg, 
          paddingHorizontal: responsiveSpacing.xl 
        }
      default:
        return { 
          paddingVertical: responsiveSpacing.md, 
          paddingHorizontal: responsiveSpacing.lg 
        }
    }
  }

  const getBorderStyle = () => {
    return variant === "outline" 
      ? { borderWidth: 1, borderColor: theme.colors.primary } 
      : {}
  }

  const getMinHeight = () => {
    switch (size) {
      case "small":
        return responsiveDimensions.buttonHeight.small
      case "medium":
        return responsiveDimensions.buttonHeight.medium
      case "large":
        return responsiveDimensions.buttonHeight.large
      default:
        return responsiveDimensions.buttonHeight.medium
    }
  }

  const getFontSize = () => {
    switch (size) {
      case "small":
        return theme.typography.buttonSmall.fontSize
      case "large":
        return theme.typography.buttonLarge.fontSize
      default:
        return theme.typography.button.fontSize
    }
  }

  return StyleSheet.create({
    button: {
      backgroundColor: getBackgroundColor(),
      borderRadius: theme.components.button.borderRadius,
      ...getPadding(),
      minHeight: getMinHeight(),
      alignItems: "center",
      justifyContent: "center",
      flexDirection: "row",
      ...getBorderStyle(),
      width: fullWidth ? "100%" : "auto",
      ...theme.components.button.shadows.default,
    },
    text: {
      color: getTextColor(),
      fontWeight: theme.typography.button.fontWeight,
      fontSize: getFontSize(),
      lineHeight: theme.typography.button.lineHeight,
    },
    iconContainer: {
      marginRight: responsiveSpacing.sm,
    },
  })
}
