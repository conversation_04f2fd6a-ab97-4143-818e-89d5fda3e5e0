import { StyleSheet } from 'react-native'
import { responsiveSpacing, responsiveDimensions } from '../../../utils/responsive'

export const reviewCardStyles = (theme) => {
  return StyleSheet.create({
    container: {
      marginBottom: responsiveSpacing.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: responsiveSpacing.sm,
    },
    customerInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    avatar: {
      width: responsiveDimensions.avatarSize.sm,
      height: responsiveDimensions.avatarSize.sm,
      borderRadius: responsiveDimensions.avatarSize.sm / 2,
      marginRight: responsiveSpacing.sm,
    },
    avatarPlaceholder: {
      width: responsiveDimensions.avatarSize.sm,
      height: responsiveDimensions.avatarSize.sm,
      borderRadius: responsiveDimensions.avatarSize.sm / 2,
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: theme.colors.border,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: responsiveSpacing.sm,
    },
    customerDetails: {
      flex: 1,
    },
    customerName: {
      fontWeight: theme.typography.button.fontWeight,
      marginBottom: responsiveSpacing.xxs,
    },
    date: {
      color: theme.colors.textSecondary,
    },
    ratingContainer: {
      alignItems: 'flex-end',
    },
    starsContainer: {
      flexDirection: 'row',
      marginBottom: responsiveSpacing.xxs,
    },
    star: {
      marginLeft: responsiveSpacing.xxs,
    },
    ratingText: {
      color: theme.colors.textSecondary,
    },
    serviceSection: {
      marginBottom: responsiveSpacing.sm,
    },
    serviceName: {
      color: theme.colors.primary,
      fontStyle: 'italic',
    },
    commentSection: {
      marginBottom: responsiveSpacing.md,
    },
    comment: {
      lineHeight: theme.typography.body.lineHeight * theme.typography.body.fontSize,
      color: theme.colors.text,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: responsiveSpacing.sm,
    },
    helpfulSection: {
      flex: 1,
    },
    helpfulText: {
      color: theme.colors.textSecondary,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: responsiveSpacing.xs,
      paddingHorizontal: responsiveSpacing.sm,
    },
    actionText: {
      marginLeft: responsiveSpacing.xs,
      color: theme.colors.textSecondary,
    },
  })
}
