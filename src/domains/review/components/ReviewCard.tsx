import React from "react"
import { View, Image } from "react-native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../../../context/ThemeContext"
import { Card, ResponsiveText } from "../../shared/components"
import { reviewCardStyles } from "../styles/ReviewCard.styles"

interface ReviewCardProps {
  id: string
  customerName: string
  customerAvatar?: string
  rating: number
  comment: string
  date: string
  serviceName?: string
  helpful?: number
  onHelpfulPress?: () => void
}

const ReviewCard: React.FC<ReviewCardProps> = ({
  customerName,
  customerAvatar,
  rating,
  comment,
  date,
  serviceName,
  helpful = 0,
  onHelpfulPress,
}) => {
  const theme = useTheme()
  const styles = reviewCardStyles(theme)

  const renderStars = () => {
    const stars = []
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Feather
          key={i}
          name="star"
          size={16}
          color={i <= rating ? theme.colors.warning : theme.colors.border}
          style={styles.star}
        />
      )
    }
    return stars
  }

  return (
    <Card style={styles.container} variant="outlined" padding="medium">
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.customerInfo}>
          {customerAvatar ? (
            <Image source={{ uri: customerAvatar }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Feather name="user" size={20} color={theme.colors.textSecondary} />
            </View>
          )}
          <View style={styles.customerDetails}>
            <ResponsiveText variant="body" style={styles.customerName}>
              {customerName}
            </ResponsiveText>
            <ResponsiveText variant="caption" style={styles.date}>
              {date}
            </ResponsiveText>
          </View>
        </View>
        
        {/* Rating */}
        <View style={styles.ratingContainer}>
          <View style={styles.starsContainer}>
            {renderStars()}
          </View>
          <ResponsiveText variant="caption" style={styles.ratingText}>
            {rating}/5
          </ResponsiveText>
        </View>
      </View>

      {/* Service Name */}
      {serviceName && (
        <View style={styles.serviceSection}>
          <ResponsiveText variant="bodySmall" style={styles.serviceName}>
            Service: {serviceName}
          </ResponsiveText>
        </View>
      )}

      {/* Comment */}
      <View style={styles.commentSection}>
        <ResponsiveText variant="body" style={styles.comment}>
          {comment}
        </ResponsiveText>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.helpfulSection}>
          <ResponsiveText variant="caption" style={styles.helpfulText}>
            {helpful} people found this helpful
          </ResponsiveText>
        </View>
        
        {onHelpfulPress && (
          <View style={styles.actionButton}>
            <Feather name="thumbs-up" size={16} color={theme.colors.textSecondary} />
            <ResponsiveText variant="caption" style={styles.actionText}>
              Helpful
            </ResponsiveText>
          </View>
        )}
      </View>
    </Card>
  )
}

export default ReviewCard
