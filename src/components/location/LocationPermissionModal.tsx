import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Platform,
  Linking,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { useTheme } from '../context/ThemeContext';
import Button from './Button';
import Storage from '../utils/storage';

interface LocationPermissionModalProps {
  visible: boolean;
  onClose: () => void;
  onLocationGranted: (location: Location.LocationObject) => void;
  onLocationDenied: () => void;
}

/**
 * LocationPermissionModal Component
 *
 * A modal that requests location permission from the user and handles
 * the different permission states (granted, denied, etc.)
 */
const LocationPermissionModal = ({
  visible,
  onClose,
  onLocationGranted,
  onLocationDenied,
}: LocationPermissionModalProps) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [permissionDenied, setPermissionDenied] = useState(false);

  // Request location permission
  const requestLocationPermission = useCallback(async () => {
    setLoading(true);
    try {
      // Request foreground location permission
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status === 'granted') {
        // Get current location
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });

        // Save permission status
        await Storage.setItem('locationPermissionStatus', 'granted');

        // Close the modal first to prevent it from staying open
        onClose();

        // Then call the callback with the location
        onLocationGranted(location);
      } else {
        // Permission denied
        setPermissionDenied(true);
        await Storage.setItem('locationPermissionStatus', 'denied');
        onLocationDenied();
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setPermissionDenied(true);
      onLocationDenied();
    } finally {
      setLoading(false);
    }
  }, [onLocationGranted, onLocationDenied, onClose]);

  // Open app settings
  const openSettings = useCallback(async () => {
    if (Platform.OS === 'ios') {
      await Linking.openURL('app-settings:');
    } else {
      await Linking.openSettings();
    }
    onClose();
  }, [onClose]);

  // Handle manual location entry
  const handleManualLocation = useCallback(() => {
    Storage.setItem('locationPermissionStatus', 'manual');
    // Close the modal first
    onClose();
    // Then call the location denied callback
    onLocationDenied();
  }, [onLocationDenied, onClose]);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Feather name="x" size={24} color={theme.colors.text} />
          </TouchableOpacity>

          <View style={styles.iconContainer}>
            <Feather
              name={permissionDenied ? "map-pin-off" : "map-pin"}
              size={48}
              color={theme.colors.primary}
            />
          </View>

          <Text style={[styles.title, { color: theme.colors.text }]}>
            {permissionDenied
              ? "Location Access Denied"
              : "Share Your Location"}
          </Text>

          <Text style={[styles.description, { color: theme.colors.textLight }]}>
            {permissionDenied
              ? "We need your location to show you nearby service providers. Please enable location access in your device settings."
              : "CleanConnect uses your location to find nearby service providers and provide accurate service. Your location is only used when you're using the app."}
          </Text>

          <View style={styles.buttonContainer}>
            {permissionDenied ? (
              <>
                <Button
                  title="Open Settings"
                  variant="primary"
                  fullWidth
                  onPress={openSettings}
                  style={styles.button}
                />
                <Button
                  title="Enter Location Manually"
                  variant="outline"
                  fullWidth
                  onPress={handleManualLocation}
                  style={styles.button}
                />
              </>
            ) : (
              <>
                <Button
                  title={loading ? "Getting Location..." : "Allow Location Access"}
                  variant="primary"
                  fullWidth
                  onPress={requestLocationPermission}
                  loading={loading}
                  style={styles.button}
                />
                <Button
                  title="Enter Location Manually"
                  variant="outline"
                  fullWidth
                  onPress={handleManualLocation}
                  style={styles.button}
                />
              </>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
  },
  iconContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  buttonContainer: {
    width: '100%',
  },
  button: {
    marginBottom: 12,
  },
});

export default LocationPermissionModal;
