import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import Button from './Button';

export interface Location {
  id: string;
  name: string;
  type: 'HOME' | 'WORK' | 'OFFICE' | 'OTHER';
  address: string;
  isDefault?: boolean;
}

interface LocationSelectorProps {
  currentLocation: Location | null;
  savedLocations: Location[];
  onLocationSelect: (location: Location) => void;
  onAddNewLocation: () => void;
  onEditLocation: (location: Location) => void;
}

/**
 * LocationSelector Component
 * 
 * A component that displays the current location in the header
 * and allows the user to select from saved locations or add a new one
 */
const LocationSelector = ({
  currentLocation,
  savedLocations,
  onLocationSelect,
  onAddNewLocation,
  onEditLocation,
}: LocationSelectorProps) => {
  const theme = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Open location selector modal
  const openModal = useCallback(() => {
    setModalVisible(true);
  }, []);

  // Close location selector modal
  const closeModal = useCallback(() => {
    setModalVisible(false);
    setSearchQuery('');
  }, []);

  // Handle location selection
  const handleLocationSelect = useCallback((location: Location) => {
    onLocationSelect(location);
    closeModal();
  }, [onLocationSelect, closeModal]);

  // Handle add new location
  const handleAddNewLocation = useCallback(() => {
    closeModal();
    onAddNewLocation();
  }, [closeModal, onAddNewLocation]);

  // Handle edit location
  const handleEditLocation = useCallback((location: Location) => {
    closeModal();
    onEditLocation(location);
  }, [closeModal, onEditLocation]);

  // Filter locations based on search query
  const filteredLocations = searchQuery
    ? savedLocations.filter(location => 
        location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.address.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : savedLocations;

  // Format location name for display (limit to 12 chars)
  const formatLocationName = (location: Location | null) => {
    if (!location) return 'Add Location';
    
    const displayName = `${location.type} - ${location.name}`;
    return displayName.length > 12 ? `${displayName.substring(0, 12)}...` : displayName;
  };

  // Render location item
  const renderLocationItem = ({ item }: { item: Location }) => (
    <View style={styles.locationItem}>
      <TouchableOpacity 
        style={styles.locationContent}
        onPress={() => handleLocationSelect(item)}
      >
        <View style={styles.locationIconContainer}>
          <Feather 
            name={item.type === 'HOME' ? 'home' : item.type === 'WORK' ? 'briefcase' : 'map-pin'} 
            size={20} 
            color={theme.colors.primary} 
          />
        </View>
        <View style={styles.locationInfo}>
          <Text style={[styles.locationName, { color: theme.colors.text }]}>
            {item.type} - {item.name}
          </Text>
          <Text style={[styles.locationAddress, { color: theme.colors.textLight }]} numberOfLines={1}>
            {item.address}
          </Text>
        </View>
        {item.isDefault && (
          <View style={[styles.defaultBadge, { backgroundColor: theme.colors.primary }]}>
            <Text style={styles.defaultText}>Default</Text>
          </View>
        )}
      </TouchableOpacity>
      <TouchableOpacity 
        style={styles.editButton}
        onPress={() => handleEditLocation(item)}
      >
        <Feather name="edit-2" size={18} color={theme.colors.textLight} />
      </TouchableOpacity>
    </View>
  );

  return (
    <>
      <TouchableOpacity style={styles.container} onPress={openModal}>
        <View style={styles.locationDisplay}>
          <Text style={[styles.locationText, { color: theme.colors.text }]}>
            {formatLocationName(currentLocation)}
          </Text>
          <Feather name="chevron-down" size={18} color={theme.colors.text} style={styles.chevron} />
        </View>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent
        onRequestClose={closeModal}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Select Location</Text>
              <TouchableOpacity onPress={closeModal}>
                <Feather name="x" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <View style={[styles.searchContainer, { backgroundColor: theme.colors.background }]}>
              <Feather name="search" size={20} color={theme.colors.textLight} style={styles.searchIcon} />
              <TextInput
                style={[styles.searchInput, { color: theme.colors.text }]}
                placeholder="Search saved locations"
                placeholderTextColor={theme.colors.textLight}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>

            <FlatList
              data={filteredLocations}
              renderItem={renderLocationItem}
              keyExtractor={(item) => item.id}
              style={styles.locationsList}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={[styles.emptyText, { color: theme.colors.textLight }]}>
                    {searchQuery ? 'No locations match your search' : 'No saved locations'}
                  </Text>
                </View>
              }
            />

            <Button
              title="Add New Location"
              variant="primary"
              fullWidth
              onPress={handleAddNewLocation}
              icon={<Feather name="plus" size={18} color="white" />}
            />
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 16,
    fontWeight: '500',
  },
  chevron: {
    marginLeft: 4,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    padding: 0,
  },
  locationsList: {
    marginBottom: 16,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  locationContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(111, 209, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: 14,
  },
  defaultBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  defaultText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  editButton: {
    padding: 8,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default LocationSelector;
