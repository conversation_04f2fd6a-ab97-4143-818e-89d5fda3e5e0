import React from "react"
import { View, StyleSheet, type ViewStyle, type StyleProp, Dimensions } from "react-native"

interface ResponsiveGridProps {
  children: React.ReactNode
  columns?: number
  spacing?: number
  style?: StyleProp<ViewStyle>
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = 2,
  spacing = 16,
  style,
}) => {
  // Determine number of columns based on screen width
  const screenWidth = Dimensions.get('window').width
  let numColumns = columns

  // Adjust columns based on screen width for better mobile experience
  if (screenWidth < 360) {
    numColumns = Math.min(numColumns, 1)
  } else if (screenWidth < 480) {
    numColumns = Math.min(numColumns, 2)
  }

  // Convert children to array
  const childrenArray = React.Children.toArray(children)

  // Create rows based on number of columns
  const rows = []
  for (let i = 0; i < childrenArray.length; i += numColumns) {
    rows.push(childrenArray.slice(i, i + numColumns))
  }

  const styles = StyleSheet.create({
    container: {
      width: "100%",
    },
    row: {
      flexDirection: "row",
      marginHorizontal: -spacing / 2,
      marginBottom: spacing,
    },
    column: {
      flex: 1,
      paddingHorizontal: spacing / 2,
    },
  })

  return (
    <View style={[styles.container, style]}>
      {rows.map((row, rowIndex) => (
        <View key={`row-${rowIndex}`} style={styles.row}>
          {row.map((col, colIndex) => (
            <View key={`col-${rowIndex}-${colIndex}`} style={styles.column}>
              {col}
            </View>
          ))}
          {/* Add empty columns if row is not full */}
          {row.length < numColumns &&
            Array(numColumns - row.length)
              .fill(null)
              .map((_, index) => <View key={`empty-${rowIndex}-${index}`} style={styles.column} />)}
        </View>
      ))}
    </View>
  )
}

export default ResponsiveGrid
