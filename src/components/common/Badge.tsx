import React from "react"
import { View, Text, StyleSheet } from "react-native"
import { useTheme } from "../../context/ThemeContext"

interface BadgeProps {
  children: React.ReactNode
  variant?: "default" | "primary" | "success" | "warning" | "danger"
}

const Badge: React.FC<BadgeProps> = ({ children, variant = "default" }) => {
  const theme = useTheme()

  const styles = StyleSheet.create({
    badge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 4,
      alignSelf: "flex-start",
    },
    default: {
      backgroundColor: `${theme.colors.textLight}20`,
    },
    primary: {
      backgroundColor: `${theme.colors.primary}20`,
    },
    success: {
      backgroundColor: `${theme.colors.secondary}20`,
    },
    warning: {
      backgroundColor: `${theme.colors.warning}20`,
    },
    danger: {
      backgroundColor: `${theme.colors.notification}20`,
    },
    text: {
      fontSize: 12,
      fontWeight: "600",
    },
    defaultText: {
      color: theme.colors.textLight,
    },
    primaryText: {
      color: theme.colors.primary,
    },
    successText: {
      color: theme.colors.secondary,
    },
    warningText: {
      color: theme.colors.warning,
    },
    dangerText: {
      color: theme.colors.notification,
    },
  })

  return (
    <View style={[styles.badge, styles[variant]]}>
      <Text style={[styles.text, styles[`${variant}Text`]]}>{children}</Text>
    </View>
  )
}

export default Badge
