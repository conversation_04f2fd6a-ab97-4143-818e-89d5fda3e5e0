import type React from "react"
import { Image, type ImageStyle, type StyleProp, StyleSheet } from "react-native"

interface ResponsiveImageProps {
  source: any
  style?: StyleProp<ImageStyle>
  resizeMode?: "cover" | "contain" | "stretch" | "repeat" | "center"
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  source,
  style,
  resizeMode = "cover",
}) => {
  const styles = StyleSheet.create({
    image: {
      width: "100%",
      height: undefined,
      aspectRatio: 16 / 9, // Default aspect ratio
    },
  })

  return (
    <Image source={source} style={[styles.image, style]} resizeMode={resizeMode} />
  )
}

export default ResponsiveImage
