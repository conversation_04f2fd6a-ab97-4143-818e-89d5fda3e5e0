import React, { useState, useEffect, createContext, useContext, useRef } from 'react';
import { View, StyleSheet } from 'react-native';
import ToastComponent from './Toast';

type ToastType = 'success' | 'error' | 'info';

interface ToastOptions {
  type?: ToastType;
  text1?: string;
  text2?: string;
  visibilityTime?: number;
  autoHide?: boolean;
  onHide?: () => void;
}

interface ToastContextType {
  show: (options: ToastOptions) => void;
  hide: () => void;
}

const ToastContext = createContext<ToastContextType>({
  show: () => {},
  hide: () => {},
});

export const useToast = () => useContext(ToastContext);

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [type, setType] = useState<ToastType>('info');
  const [duration, setDuration] = useState(3000);
  const [autoHide, setAutoHide] = useState(true);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const onHideCallback = useRef<(() => void) | null>(null);

  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  const show = (options: ToastOptions) => {
    // Clear any existing timeout
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }

    // Set toast properties
    setType(options.type || 'info');
    setMessage(options.text2 || options.text1 || '');
    setDuration(options.visibilityTime || 3000);
    setAutoHide(options.autoHide !== false);
    onHideCallback.current = options.onHide || null;

    // Show the toast
    setVisible(true);

    // Auto hide if enabled
    if (options.autoHide !== false) {
      hideTimeoutRef.current = setTimeout(() => {
        hide();
      }, options.visibilityTime || 3000);
    }
  };

  const hide = () => {
    setVisible(false);
    if (onHideCallback.current) {
      onHideCallback.current();
      onHideCallback.current = null;
    }
  };

  return (
    <ToastContext.Provider value={{ show, hide }}>
      {children}
      <ToastComponent
        visible={visible}
        message={message}
        type={type}
        duration={duration}
        onClose={hide}
      />
    </ToastContext.Provider>
  );
};

// Create a standalone ToastService object for easier usage
export const ToastService = {
  show: (options: ToastOptions) => {
    // This will be replaced by the actual implementation when the provider is mounted
    console.warn('ToastService.show was called before ToastProvider was mounted');
  },
  hide: () => {
    // This will be replaced by the actual implementation when the provider is mounted
    console.warn('ToastService.hide was called before ToastProvider was mounted');
  },
};

// Initialize the ToastService object with the context
export const initToast = (context: ToastContextType) => {
  ToastService.show = context.show;
  ToastService.hide = context.hide;
};

// Component to initialize the ToastService object
export const ToastInitializer: React.FC = () => {
  const toast = useToast();

  useEffect(() => {
    initToast(toast);
  }, [toast]);

  return null;
};
