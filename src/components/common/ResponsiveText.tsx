import type React from "react"
import { Text, type TextStyle, type StyleProp, StyleSheet } from "react-native"
import { useResponsive } from "../../hooks/useResponsive"
import { useTheme } from "../../context/ThemeContext"

interface ResponsiveTextProps {
  children: React.ReactNode
  style?: StyleProp<TextStyle>
  variant?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "body" | "caption" | "button"
  numberOfLines?: number
}

const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  style,
  variant = "body",
  numberOfLines,
  ...props
}) => {
  const { deviceType } = useResponsive()
  const theme = useTheme()

  // Define font sizes based on variant and device type
  const getFontSize = () => {
    const baseSizes = {
      h1: { phone: 24, tablet: 28 },
      h2: { phone: 20, tablet: 24 },
      h3: { phone: 18, tablet: 20 },
      h4: { phone: 16, tablet: 18 },
      h5: { phone: 14, tablet: 16 },
      h6: { phone: 12, tablet: 14 },
      body: { phone: 14, tablet: 16 },
      caption: { phone: 12, tablet: 12 },
      button: { phone: 14, tablet: 16 },
    }

    return baseSizes[variant][deviceType]
  }

  // Define font weights based on variant
  const getFontWeight = () => {
    switch (variant) {
      case "h1":
      case "h2":
      case "h3":
        return "700" // bold
      case "h4":
      case "h5":
      case "h6":
      case "button":
        return "600" // semi-bold
      default:
        return "400" // normal
    }
  }

  const styles = StyleSheet.create({
    text: {
      fontSize: getFontSize(),
      fontWeight: getFontWeight() as TextStyle["fontWeight"],
      color: theme.colors.text,
    },
  })

  return (
    <Text style={[styles.text, style]} numberOfLines={numberOfLines} {...props}>
      {children}
    </Text>
  )
}

export default ResponsiveText
