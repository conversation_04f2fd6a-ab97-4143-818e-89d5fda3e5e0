import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Animated,
  Dimensions,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import { useBooking } from '../../context/BookingContext';
import { Button, Toast } from '../common';

interface AuthModalProps {
  visible: boolean;
  onClose: () => void;
  returnTo?: string;
  returnParams?: any;
  message?: string;
}

const AuthModal: React.FC<AuthModalProps> = ({
  visible,
  onClose,
  returnTo,
  returnParams,
  message = 'Please sign in or create an account to continue',
}) => {
  const navigation = useNavigation<any>();
  const theme = useTheme();
  const { getUserRole } = useAuth();
  const { hasStoredBooking } = useBooking();

  const [slideAnim] = useState(new Animated.Value(0));
  const [showToast, setShowToast] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [hasBooking, setHasBooking] = useState(false);

  const { height } = Dimensions.get('window');

  // Load user role when modal becomes visible
  useEffect(() => {
    if (visible) {
      const loadRole = async () => {
        const role = await getUserRole();
        setUserRole(role);
      };

      const checkBooking = async () => {
        const hasBooking = await hasStoredBooking();
        setHasBooking(hasBooking);
      };

      loadRole();
      checkBooking();

      // Animate modal sliding up
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Reset animation when modal is hidden
      slideAnim.setValue(0);
    }
  }, [visible, getUserRole, hasStoredBooking]);

  // Handle login button press
  const handleLogin = () => {
    onClose();

    // Show toast notification if booking data is saved
    if (hasBooking) {
      setShowToast(true);
      console.log('Showing toast for saved booking data');
    }

    console.log('Navigating to login with return parameters:', {
      returnTo: returnTo || 'Main',
      fromBooking: hasBooking
    });

    // Navigate to login screen with return parameters
    navigation.navigate('Login', {
      returnTo: returnTo || 'Main',
      returnParams,
      fromBooking: hasBooking
    });
  };

  // Handle register button press
  const handleRegister = () => {
    onClose();

    // Show toast notification if booking data is saved
    if (hasBooking) {
      setShowToast(true);
      console.log('Showing toast for saved booking data');
    }

    console.log('Navigating to register with return parameters:', {
      returnTo: returnTo || 'Main',
      fromBooking: hasBooking,
      selectedRole: returnTo === 'PaymentOption' ? 'CUSTOMER' : undefined
    });

    // For booking flow, we always want to register as a customer
    if (returnTo === 'PaymentOption') {
      navigation.navigate('Register', {
        returnTo: returnTo || 'Main',
        returnParams,
        fromBooking: hasBooking,
        selectedRole: 'CUSTOMER' // Force customer role for booking flow
      });
    }
    // If we have a role, go directly to Register, otherwise go to RoleSelection
    else if (userRole) {
      navigation.navigate('Register', {
        returnTo: returnTo || 'Main',
        returnParams,
        fromBooking: hasBooking
      });
    } else {
      navigation.navigate('RoleSelection');
    }
  };

  // Calculate transform based on animation value
  const translateY = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [height, 0],
  });

  return (
    <>
      <Modal
        visible={visible}
        transparent
        animationType="none"
        onRequestClose={onClose}
      >
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.overlay}>
            <TouchableWithoutFeedback>
              <Animated.View
                style={[
                  styles.modalContainer,
                  { backgroundColor: theme.colors.card },
                  { transform: [{ translateY }] },
                ]}
              >
                <View style={styles.header}>
                  <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                    <Feather name="x" size={24} color={theme.colors.text} />
                  </TouchableOpacity>
                </View>

                <View style={styles.content}>
                  <Feather name="user-check" size={60} color={theme.colors.primary} style={styles.icon} />

                  <Text style={[styles.title, { color: theme.colors.text }]}>
                    Authentication Required
                  </Text>

                  <Text style={[styles.message, { color: theme.colors.textLight }]}>
                    {message}
                  </Text>

                  {hasBooking && (
                    <View style={[styles.infoBox, { backgroundColor: `${theme.colors.primary}15` }]}>
                      <Feather name="info" size={20} color={theme.colors.primary} style={styles.infoIcon} />
                      <Text style={[styles.infoText, { color: theme.colors.text }]}>
                        Your booking details will be saved
                      </Text>
                    </View>
                  )}

                  <View style={styles.buttonContainer}>
                    <Button
                      title="Sign In"
                      variant="primary"
                      fullWidth
                      onPress={handleLogin}
                    />

                    <Button
                      title="Create Account"
                      variant="outline"
                      fullWidth
                      onPress={handleRegister}
                      style={{ marginTop: 12 }}
                    />
                  </View>
                </View>
              </Animated.View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* Toast notification */}
      <Toast
        visible={showToast}
        message="Your booking details have been saved"
        type="success"
        onDismiss={() => setShowToast(false)}
        duration={3000}
      />
    </>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 24,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -3 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  icon: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    width: '100%',
  },
  infoIcon: {
    marginRight: 12,
  },
  infoText: {
    fontSize: 14,
    flex: 1,
  },
  buttonContainer: {
    width: '100%',
  },
});

export default AuthModal;
