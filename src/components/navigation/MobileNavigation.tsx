"use client"

import type React from "react"
import { View, Text, StyleSheet, TouchableOpacity } from "react-native"
import { useNavigation } from "@react-navigation/native"
import { Feather } from "@expo/vector-icons"
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useTheme } from "../../context/ThemeContext"

interface MobileNavigationProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({ activeTab, onTabChange }) => {
  const navigation = useNavigation()
  const theme = useTheme()
  const insets = useSafeAreaInsets()

  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: "white",
      height: 60 + (insets.bottom > 0 ? insets.bottom - 10 : 0),
      paddingBottom: insets.bottom > 0 ? insets.bottom - 10 : 0,
      width: '100%',
    },
    tabItem: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: 8,
    },
    tabIcon: {
      marginBottom: 4,
    },
    tabLabel: {
      fontSize: 12,
    },
  })

  const tabs = [
    { id: "home", label: "Home", icon: "home" },
    { id: "search", label: "Services", icon: "search" },
    { id: "bookings", label: "Bookings", icon: "calendar" },
    { id: "profile", label: "Profile", icon: "user" },
  ]

  return (
    <View style={styles.container}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.id}
          style={styles.tabItem}
          onPress={() => {
            onTabChange(tab.id)
            navigation.navigate(
              tab.id === "home"
                ? "Home"
                : tab.id === "search"
                  ? "ServiceBrowsing"
                  : tab.id === "bookings"
                    ? "BookingHistory"
                    : "Profile",
            )
          }}
        >
          <Feather
            name={tab.icon}
            size={20}
            color={activeTab === tab.id ? theme.colors.primary : theme.colors.textLight}
            style={styles.tabIcon}
          />
          <Text
            style={[styles.tabLabel, { color: activeTab === tab.id ? theme.colors.primary : theme.colors.textLight }]}
          >
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  )
}

export default MobileNavigation
