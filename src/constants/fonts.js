// Design System - Typography
// Centralized font definitions for consistent typography

export const fonts = {
  // Font Families
  families: {
    primary: "System", // Default system font
    secondary: "System", // Alternative font family
    monospace: "Courier", // Monospace font for code
  },
  
  // Font Sizes
  sizes: {
    xxs: 10,
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 28,
    display: 32,
    hero: 40,
  },
  
  // Font Weights
  weights: {
    light: "300",
    normal: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
    extrabold: "800",
  },
  
  // Line Heights (relative to font size)
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
  
  // Letter Spacing
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },
}

// Typography presets for common text styles
export const typography = {
  // Headings
  h1: {
    fontSize: fonts.sizes.hero,
    fontWeight: fonts.weights.bold,
    lineHeight: fonts.lineHeights.tight,
    letterSpacing: fonts.letterSpacing.tight,
  },
  h2: {
    fontSize: fonts.sizes.display,
    fontWeight: fonts.weights.bold,
    lineHeight: fonts.lineHeights.tight,
    letterSpacing: fonts.letterSpacing.tight,
  },
  h3: {
    fontSize: fonts.sizes.xxxl,
    fontWeight: fonts.weights.semibold,
    lineHeight: fonts.lineHeights.normal,
  },
  h4: {
    fontSize: fonts.sizes.xxl,
    fontWeight: fonts.weights.semibold,
    lineHeight: fonts.lineHeights.normal,
  },
  h5: {
    fontSize: fonts.sizes.xl,
    fontWeight: fonts.weights.medium,
    lineHeight: fonts.lineHeights.normal,
  },
  h6: {
    fontSize: fonts.sizes.lg,
    fontWeight: fonts.weights.medium,
    lineHeight: fonts.lineHeights.normal,
  },
  
  // Body Text
  body: {
    fontSize: fonts.sizes.md,
    fontWeight: fonts.weights.normal,
    lineHeight: fonts.lineHeights.normal,
  },
  bodyLarge: {
    fontSize: fonts.sizes.lg,
    fontWeight: fonts.weights.normal,
    lineHeight: fonts.lineHeights.normal,
  },
  bodySmall: {
    fontSize: fonts.sizes.sm,
    fontWeight: fonts.weights.normal,
    lineHeight: fonts.lineHeights.normal,
  },
  
  // Labels and UI Text
  label: {
    fontSize: fonts.sizes.sm,
    fontWeight: fonts.weights.medium,
    lineHeight: fonts.lineHeights.normal,
  },
  labelLarge: {
    fontSize: fonts.sizes.md,
    fontWeight: fonts.weights.medium,
    lineHeight: fonts.lineHeights.normal,
  },
  labelSmall: {
    fontSize: fonts.sizes.xs,
    fontWeight: fonts.weights.medium,
    lineHeight: fonts.lineHeights.normal,
  },
  
  // Captions and Helper Text
  caption: {
    fontSize: fonts.sizes.xs,
    fontWeight: fonts.weights.normal,
    lineHeight: fonts.lineHeights.normal,
  },
  overline: {
    fontSize: fonts.sizes.xxs,
    fontWeight: fonts.weights.medium,
    lineHeight: fonts.lineHeights.normal,
    letterSpacing: fonts.letterSpacing.wide,
    textTransform: 'uppercase',
  },
  
  // Button Text
  button: {
    fontSize: fonts.sizes.md,
    fontWeight: fonts.weights.semibold,
    lineHeight: fonts.lineHeights.tight,
  },
  buttonLarge: {
    fontSize: fonts.sizes.lg,
    fontWeight: fonts.weights.semibold,
    lineHeight: fonts.lineHeights.tight,
  },
  buttonSmall: {
    fontSize: fonts.sizes.sm,
    fontWeight: fonts.weights.semibold,
    lineHeight: fonts.lineHeights.tight,
  },
}

export default fonts
