// Design System - Colors
// Centralized color palette for consistent theming

export const colors = {
  // Primary Colors
  primary: "#6FD1FF", // Sky Blue
  primaryDark: "#4AA8E0",
  primaryLight: "#ADE1FF",
  
  // Secondary Colors
  secondary: "#A2E2BD", // Mint Green
  secondaryDark: "#5AB987",
  secondaryLight: "#C3EFD7",
  
  // Accent Colors
  accent: "#84D3F2", // Light Cyan
  accentDark: "#3CA9DA",
  accentLight: "#B0E0F4",
  
  // Status Colors
  warning: "#FFD43B", // Bright Yellow
  warningDark: "#E6B000",
  warningLight: "#FFE99D",
  success: "#52C41A",
  successDark: "#389E0D",
  successLight: "#B7EB8F",
  error: "#FF3B30",
  errorDark: "#D9363E",
  errorLight: "#FFCCC7",
  info: "#1890FF",
  infoDark: "#096DD9",
  infoLight: "#BAE7FF",
  
  // Neutral Colors
  background: "#F5F5F5",
  backgroundDark: "#E8E8E8",
  backgroundLight: "#FAFAFA",
  card: "#FFFFFF",
  cardDark: "#F8F8F8",
  surface: "#FFFFFF",
  surfaceDark: "#F5F5F5",
  
  // Text Colors
  text: "#333333",
  textSecondary: "#666666",
  textLight: "#999999",
  textDisabled: "#CCCCCC",
  textInverse: "#FFFFFF",
  
  // Border Colors
  border: "#E1E1E1",
  borderLight: "#F0F0F0",
  borderDark: "#D9D9D9",
  divider: "#EEEEEE",
  
  // Overlay Colors
  overlay: "rgba(0, 0, 0, 0.5)",
  overlayLight: "rgba(0, 0, 0, 0.3)",
  overlayDark: "rgba(0, 0, 0, 0.7)",
  
  // Notification Colors
  notification: "#FF4D4F",
  notificationDark: "#CF1322",
  notificationLight: "#FFF1F0",
  
  // Transparent
  transparent: "transparent",
  
  // Gradients (for LinearGradient components)
  gradients: {
    primary: ["#6FD1FF", "#4AA8E0"],
    secondary: ["#A2E2BD", "#5AB987"],
    accent: ["#84D3F2", "#3CA9DA"],
    warm: ["#FFD43B", "#E6B000"],
    cool: ["#6FD1FF", "#84D3F2"],
  },
}

// Color utility functions
export const getColorWithOpacity = (color, opacity) => {
  // Convert hex to rgba
  const hex = color.replace('#', '')
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)
  return `rgba(${r}, ${g}, ${b}, ${opacity})`
}

export const isDarkColor = (color) => {
  // Simple check for dark colors
  const hex = color.replace('#', '')
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  return brightness < 128
}

export default colors
