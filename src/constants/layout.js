// Design System - Layout
// Centralized layout utilities and flex helpers

export const layout = {
  // Flex utilities
  flex: {
    // Flex direction
    row: { flexDirection: 'row' },
    column: { flexDirection: 'column' },
    rowReverse: { flexDirection: 'row-reverse' },
    columnReverse: { flexDirection: 'column-reverse' },
    
    // Justify content
    justifyStart: { justifyContent: 'flex-start' },
    justifyEnd: { justifyContent: 'flex-end' },
    justifyCenter: { justifyContent: 'center' },
    justifyBetween: { justifyContent: 'space-between' },
    justifyAround: { justifyContent: 'space-around' },
    justifyEvenly: { justifyContent: 'space-evenly' },
    
    // Align items
    alignStart: { alignItems: 'flex-start' },
    alignEnd: { alignItems: 'flex-end' },
    alignCenter: { alignItems: 'center' },
    alignStretch: { alignItems: 'stretch' },
    alignBaseline: { alignItems: 'baseline' },
    
    // Align self
    selfStart: { alignSelf: 'flex-start' },
    selfEnd: { alignSelf: 'flex-end' },
    selfCenter: { alignSelf: 'center' },
    selfStretch: { alignSelf: 'stretch' },
    selfBaseline: { alignSelf: 'baseline' },
    
    // Flex values
    flex1: { flex: 1 },
    flex2: { flex: 2 },
    flex3: { flex: 3 },
    flexNone: { flex: 0 },
    flexGrow: { flexGrow: 1 },
    flexShrink: { flexShrink: 1 },
    
    // Flex wrap
    wrap: { flexWrap: 'wrap' },
    nowrap: { flexWrap: 'nowrap' },
    wrapReverse: { flexWrap: 'wrap-reverse' },
  },
  
  // Position utilities
  position: {
    relative: { position: 'relative' },
    absolute: { position: 'absolute' },
    
    // Absolute positioning helpers
    topLeft: { position: 'absolute', top: 0, left: 0 },
    topRight: { position: 'absolute', top: 0, right: 0 },
    bottomLeft: { position: 'absolute', bottom: 0, left: 0 },
    bottomRight: { position: 'absolute', bottom: 0, right: 0 },
    center: { 
      position: 'absolute', 
      top: '50%', 
      left: '50%', 
      transform: [{ translateX: -50 }, { translateY: -50 }] 
    },
    
    // Fill parent
    fill: { position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 },
  },
  
  // Common layout patterns
  patterns: {
    // Center content both horizontally and vertically
    centerAll: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    
    // Center content horizontally
    centerHorizontal: {
      alignItems: 'center',
    },
    
    // Center content vertically
    centerVertical: {
      justifyContent: 'center',
    },
    
    // Row with space between items
    rowBetween: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    
    // Row with centered items
    rowCenter: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    
    // Row with items aligned to start
    rowStart: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
    },
    
    // Row with items aligned to end
    rowEnd: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    
    // Column with space between items
    columnBetween: {
      flexDirection: 'column',
      justifyContent: 'space-between',
    },
    
    // Column with centered items
    columnCenter: {
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },
    
    // Full width and height
    fullSize: {
      width: '100%',
      height: '100%',
    },
    
    // Full width
    fullWidth: {
      width: '100%',
    },
    
    // Full height
    fullHeight: {
      height: '100%',
    },
  },
  
  // Grid-like utilities for consistent spacing
  grid: {
    // Container with consistent padding
    container: {
      paddingHorizontal: 16,
    },
    
    // Row with consistent spacing between items
    row: {
      flexDirection: 'row',
      marginHorizontal: -8,
    },
    
    // Column item with consistent spacing
    col: {
      paddingHorizontal: 8,
    },
    
    // Grid item spacing
    item: {
      marginBottom: 16,
    },
  },
}

// Breakpoint utilities for responsive design
export const breakpoints = {
  xs: 0,
  sm: 360,  // Small phones
  md: 480,  // Large phones
  lg: 768,  // Tablets
  xl: 1024, // Large tablets/small desktops
}

// Helper function to create responsive styles
export const createResponsiveStyle = (styles) => {
  // This would be used with the responsive hook
  // Example: createResponsiveStyle({ xs: { fontSize: 14 }, md: { fontSize: 16 } })
  return styles
}

export default layout
