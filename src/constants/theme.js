// Design System - Complete Theme
// Centralized theme object combining all design tokens

import { colors } from './colors'
import { fonts, typography } from './fonts'
import { spacing, borderRadius, shadows, zIndex } from './spacing'
import { layout, breakpoints } from './layout'

// Complete theme object
export const theme = {
  // Colors
  colors,
  
  // Typography
  fonts,
  typography,
  
  // Spacing and layout
  spacing: spacing.layout, // Use layout spacing for backward compatibility
  borderRadius,
  shadows,
  zIndex,
  
  // Layout utilities
  layout,
  breakpoints,
  
  // Component-specific themes
  components: {
    button: {
      borderRadius: borderRadius.component.button,
      padding: spacing.component.padding,
      typography: typography.button,
      shadows: {
        default: shadows.sm,
        pressed: shadows.xs,
      },
    },
    
    card: {
      borderRadius: borderRadius.component.card,
      padding: spacing.component.padding.md,
      shadow: shadows.sm,
      backgroundColor: colors.card,
    },
    
    input: {
      borderRadius: borderRadius.component.input,
      padding: spacing.component.padding.md,
      typography: typography.body,
      borderColor: colors.border,
      focusedBorderColor: colors.primary,
    },
    
    modal: {
      borderRadius: borderRadius.component.modal,
      padding: spacing.component.padding.lg,
      shadow: shadows.lg,
      backgroundColor: colors.card,
      overlayColor: colors.overlay,
    },
    
    header: {
      height: 56,
      padding: spacing.component.padding.md,
      backgroundColor: colors.card,
      shadow: shadows.sm,
      typography: typography.h6,
    },
    
    tabBar: {
      height: 60,
      padding: spacing.component.padding.sm,
      backgroundColor: colors.card,
      shadow: shadows.md,
    },
    
    listItem: {
      padding: spacing.component.padding.md,
      borderRadius: borderRadius.sm,
      spacing: spacing.layout.listItemSpacing,
    },
    
    avatar: {
      borderRadius: borderRadius.component.avatar,
      sizes: {
        xs: 24,
        sm: 32,
        md: 40,
        lg: 56,
        xl: 72,
      },
    },
    
    badge: {
      borderRadius: borderRadius.round,
      padding: { paddingVertical: 2, paddingHorizontal: 8 },
      typography: typography.labelSmall,
    },
    
    toast: {
      borderRadius: borderRadius.md,
      padding: spacing.component.padding.md,
      shadow: shadows.lg,
      zIndex: zIndex.toast,
    },
  },
  
  // Animation durations
  animations: {
    fast: 150,
    normal: 300,
    slow: 500,
    
    // Easing curves
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    },
  },
  
  // Common dimensions
  dimensions: {
    headerHeight: 56,
    tabBarHeight: 60,
    buttonHeight: {
      small: 32,
      medium: 44,
      large: 56,
    },
    inputHeight: 44,
    touchableMinHeight: 44, // Minimum touch target size
  },
}

// Theme utility functions
export const getThemeColor = (colorPath, theme) => {
  // Helper to get nested color values
  // Example: getThemeColor('colors.primary', theme)
  return colorPath.split('.').reduce((obj, key) => obj?.[key], theme)
}

export const createThemedStyles = (styleFunction) => {
  // Helper to create styles that depend on theme
  // Example: createThemedStyles((theme) => ({ color: theme.colors.primary }))
  return (theme) => styleFunction(theme)
}

// Export individual design tokens for direct import
export {
  colors,
  fonts,
  typography,
  spacing,
  borderRadius,
  shadows,
  zIndex,
  layout,
  breakpoints,
}

export default theme
