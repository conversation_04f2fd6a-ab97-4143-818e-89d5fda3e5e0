// Design System - Main Export
// Centralized export for all design tokens and utilities

// Export all design tokens
export { colors, getColorWithOpacity, isDarkColor } from './colors'
export { fonts, typography } from './fonts'
export { spacing, borderRadius, shadows, zIndex } from './spacing'
export { layout, breakpoints, createResponsiveStyle } from './layout'
export { 
  theme, 
  getThemeColor, 
  createThemedStyles 
} from './theme'

// Export responsive utilities
export {
  wp,
  hp,
  rf,
  rs,
  isTablet,
  isSmallDevice,
  isLargeDevice,
  getBreakpoint,
  responsiveValue,
  responsiveDimensions,
  responsiveSpacing,
  responsiveFontSizes,
} from '../utils/responsive'

// Re-export the complete theme as default
export { default } from './theme'
