// Design System - Spacing
// Centralized spacing values for consistent layouts

export const spacing = {
  // Base spacing units (in pixels)
  xxs: 2,
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
  huge: 96,
  
  // Component-specific spacing
  component: {
    // Padding for different component sizes
    padding: {
      xs: { paddingVertical: 4, paddingHorizontal: 8 },
      sm: { paddingVertical: 8, paddingHorizontal: 12 },
      md: { paddingVertical: 12, paddingHorizontal: 16 },
      lg: { paddingVertical: 16, paddingHorizontal: 24 },
      xl: { paddingVertical: 20, paddingHorizontal: 32 },
    },
    
    // Margin for different component sizes
    margin: {
      xs: { marginVertical: 4, marginHorizontal: 8 },
      sm: { marginVertical: 8, marginHorizontal: 12 },
      md: { marginVertical: 12, marginHorizontal: 16 },
      lg: { marginVertical: 16, marginHorizontal: 24 },
      xl: { marginVertical: 20, marginHorizontal: 32 },
    },
  },
  
  // Layout spacing
  layout: {
    // Screen padding
    screenPadding: 16,
    screenPaddingHorizontal: 16,
    screenPaddingVertical: 24,
    
    // Section spacing
    sectionSpacing: 32,
    sectionSpacingSmall: 24,
    sectionSpacingLarge: 48,
    
    // Card spacing
    cardPadding: 16,
    cardMargin: 8,
    cardSpacing: 16,
    
    // List spacing
    listItemSpacing: 12,
    listSectionSpacing: 24,
    
    // Form spacing
    formFieldSpacing: 16,
    formSectionSpacing: 24,
    formButtonSpacing: 32,
  },
}

// Border radius values
export const borderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  round: 9999, // For circular elements
  
  // Component-specific border radius
  component: {
    button: 8,
    card: 12,
    input: 8,
    modal: 16,
    image: 8,
    avatar: 9999,
  },
}

// Shadow definitions
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  },
}

// Z-index values for layering
export const zIndex = {
  base: 0,
  raised: 1,
  dropdown: 10,
  sticky: 20,
  overlay: 30,
  modal: 40,
  popover: 50,
  tooltip: 60,
  toast: 70,
  max: 9999,
}

export default spacing
