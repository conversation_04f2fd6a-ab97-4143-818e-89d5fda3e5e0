# CleanConnect App - Frontend-Only Demo

CleanConnect is a mobile application for booking cleaning services, built with React Native and Expo. This is a **frontend-only demo version** that works entirely offline with realistic Gambian context data.

## Project Structure

This is a standalone frontend application with no backend dependencies:

- `/src` - Contains the React Native frontend application
- `/src/services` - Local data services that replace API calls
- `/src/data` - Static data with Gambian context

## Features

- **Offline-first**: Works completely without internet connection
- **Gambian Context**: Uses realistic data for The Gambia (locations, names, phone numbers)
- **Full UI/UX**: All screens and user interactions work with local data
- **Role-based Authentication**: Supports both customer and provider roles
- **Local Data Persistence**: Uses AsyncStorage and SecureStore for data
- **Booking System**: Complete booking flow with local data storage

## Demo Credentials

### Provider Login (Email/Password)
- **Email**: `<EMAIL>`
- **Password**: `M12345a@`

### Customer Login (Phone OTP)
- **Phone**: Any Gambian number format (+2207XXXXXXX)
- **OTP**: Any 4-digit code (e.g., 1234)

### Auto-Registration
- New phone numbers are automatically registered as customers
- New email addresses can be registered as providers

## Running the Application

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm start
   ```

3. Use Expo Go on your mobile device or an emulator to run the app

## Local Data

The app uses realistic Gambian context data including:

- **Locations**: Serrekunda, Banjul, Bakau, Kanifing areas
- **Services**: Home cleaning, office cleaning, deep cleaning, post-construction cleanup
- **Providers**: Local Gambian names and phone numbers
- **Pricing**: In Gambian Dalasi (GMD)

All data is stored locally using:
- **AsyncStorage**: For general app data
- **SecureStore**: For authentication tokens and sensitive data
